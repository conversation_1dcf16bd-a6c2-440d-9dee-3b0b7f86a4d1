{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    ref: \"globeContainer\",\n    staticClass: \"globe-container\"\n  }, [_c(\"div\", {\n    ref: \"canvasContainer\",\n    staticClass: \"globe-canvas\"\n  }), _vm.isLoading ? _c(\"div\", {\n    staticClass: \"loading-indicator\"\n  }, [_c(\"div\", {\n    staticClass: \"loading-spinner\"\n  }), _c(\"div\", {\n    staticClass: \"loading-text\"\n  }, [_vm._v(\"正在加载全球算力网络...\")])]) : _vm._e(), !_vm.isLoading ? _c(\"div\", {\n    staticClass: \"globe-overlay\"\n  }, [_c(\"div\", {\n    staticClass: \"globe-title\"\n  }, [_vm._v(\"全球算力布局\")]), _c(\"div\", {\n    staticClass: \"globe-subtitle\"\n  }, [_vm._v(\"分布式高性能计算网络\")]), _c(\"div\", {\n    staticClass: \"interaction-hint\"\n  }, [_vm._v(\"拖拽旋转 | 点击节点查看详情\")]), _vm.selectedNode ? _c(\"div\", {\n    staticClass: \"node-info\",\n    style: _vm.nodeInfoStyle\n  }, [_c(\"h4\", [_vm._v(_vm._s(_vm.selectedNode.name))]), _c(\"p\", [_vm._v(_vm._s(_vm.selectedNode.description))]), _c(\"div\", {\n    staticClass: \"node-stats\"\n  }, [_c(\"span\", [_vm._v(\"GPU节点: \" + _vm._s(_vm.selectedNode.gpuCount))]), _c(\"span\", [_vm._v(\"算力: \" + _vm._s(_vm.selectedNode.computePower))])])]) : _vm._e()]) : _vm._e()]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "ref", "staticClass", "isLoading", "_v", "_e", "selectedNode", "style", "nodeInfoStyle", "_s", "name", "description", "gpuCount", "computePower", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/components/common/Globe3D.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { ref: \"globeContainer\", staticClass: \"globe-container\" }, [\n    _c(\"div\", { ref: \"canvasContainer\", staticClass: \"globe-canvas\" }),\n    _vm.isLoading\n      ? _c(\"div\", { staticClass: \"loading-indicator\" }, [\n          _c(\"div\", { staticClass: \"loading-spinner\" }),\n          _c(\"div\", { staticClass: \"loading-text\" }, [\n            _vm._v(\"正在加载全球算力网络...\"),\n          ]),\n        ])\n      : _vm._e(),\n    !_vm.isLoading\n      ? _c(\"div\", { staticClass: \"globe-overlay\" }, [\n          _c(\"div\", { staticClass: \"globe-title\" }, [_vm._v(\"全球算力布局\")]),\n          _c(\"div\", { staticClass: \"globe-subtitle\" }, [\n            _vm._v(\"分布式高性能计算网络\"),\n          ]),\n          _c(\"div\", { staticClass: \"interaction-hint\" }, [\n            _vm._v(\"拖拽旋转 | 点击节点查看详情\"),\n          ]),\n          _vm.selectedNode\n            ? _c(\n                \"div\",\n                { staticClass: \"node-info\", style: _vm.nodeInfoStyle },\n                [\n                  _c(\"h4\", [_vm._v(_vm._s(_vm.selectedNode.name))]),\n                  _c(\"p\", [_vm._v(_vm._s(_vm.selectedNode.description))]),\n                  _c(\"div\", { staticClass: \"node-stats\" }, [\n                    _c(\"span\", [\n                      _vm._v(\"GPU节点: \" + _vm._s(_vm.selectedNode.gpuCount)),\n                    ]),\n                    _c(\"span\", [\n                      _vm._v(\"算力: \" + _vm._s(_vm.selectedNode.computePower)),\n                    ]),\n                  ]),\n                ]\n              )\n            : _vm._e(),\n        ])\n      : _vm._e(),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,GAAG,EAAE,gBAAgB;IAAEC,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC1EH,EAAE,CAAC,KAAK,EAAE;IAAEE,GAAG,EAAE,iBAAiB;IAAEC,WAAW,EAAE;EAAe,CAAC,CAAC,EAClEJ,GAAG,CAACK,SAAS,GACTJ,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC7CH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCJ,GAAG,CAACM,EAAE,CAAC,eAAe,CAAC,CACxB,CAAC,CACH,CAAC,GACFN,GAAG,CAACO,EAAE,EAAE,EACZ,CAACP,GAAG,CAACK,SAAS,GACVJ,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAc,CAAC,EAAE,CAACJ,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC7DL,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CJ,GAAG,CAACM,EAAE,CAAC,YAAY,CAAC,CACrB,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CJ,GAAG,CAACM,EAAE,CAAC,iBAAiB,CAAC,CAC1B,CAAC,EACFN,GAAG,CAACQ,YAAY,GACZP,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE,WAAW;IAAEK,KAAK,EAAET,GAAG,CAACU;EAAc,CAAC,EACtD,CACET,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACQ,YAAY,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC,EACjDX,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACQ,YAAY,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC,EACvDZ,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACM,EAAE,CAAC,SAAS,GAAGN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACQ,YAAY,CAACM,QAAQ,CAAC,CAAC,CACtD,CAAC,EACFb,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACM,EAAE,CAAC,MAAM,GAAGN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACQ,YAAY,CAACO,YAAY,CAAC,CAAC,CACvD,CAAC,CACH,CAAC,CACH,CACF,GACDf,GAAG,CAACO,EAAE,EAAE,CACb,CAAC,GACFP,GAAG,CAACO,EAAE,EAAE,CACb,CAAC;AACJ,CAAC;AACD,IAAIS,eAAe,GAAG,EAAE;AACxBjB,MAAM,CAACkB,aAAa,GAAG,IAAI;AAE3B,SAASlB,MAAM,EAAEiB,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}