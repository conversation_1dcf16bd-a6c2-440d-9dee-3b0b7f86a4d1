{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as THREE from 'three';\nexport default {\n  name: 'Globe3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      globe: null,\n      nodes: [],\n      selectedNode: null,\n      mousePosition: {\n        x: 0,\n        y: 0\n      },\n      animationId: null,\n      // 全球算力节点数据\n      computeNodes: [{\n        name: '北京节点',\n        lat: 39.9042,\n        lng: 116.4074,\n        description: '华北地区主要算力中心',\n        gpuCount: '2000+',\n        computePower: '500 PFLOPS'\n      }, {\n        name: '上海节点',\n        lat: 31.2304,\n        lng: 121.4737,\n        description: '华东地区核心算力枢纽',\n        gpuCount: '1800+',\n        computePower: '450 PFLOPS'\n      }, {\n        name: '深圳节点',\n        lat: 22.3193,\n        lng: 114.1694,\n        description: '华南地区智算中心',\n        gpuCount: '1500+',\n        computePower: '380 PFLOPS'\n      }, {\n        name: '成都节点',\n        lat: 30.5728,\n        lng: 104.0668,\n        description: '西南地区算力基地',\n        gpuCount: '1200+',\n        computePower: '300 PFLOPS'\n      }, {\n        name: '杭州节点',\n        lat: 30.2741,\n        lng: 120.1551,\n        description: '长三角算力集群',\n        gpuCount: '1000+',\n        computePower: '250 PFLOPS'\n      }, {\n        name: '新加坡节点',\n        lat: 1.3521,\n        lng: 103.8198,\n        description: '东南亚算力中心',\n        gpuCount: '800+',\n        computePower: '200 PFLOPS'\n      }]\n    };\n  },\n  computed: {\n    nodeInfoStyle() {\n      return {\n        left: this.mousePosition.x + 20 + 'px',\n        top: this.mousePosition.y - 50 + 'px'\n      };\n    }\n  },\n  mounted() {\n    this.initThree();\n    this.createGlobe();\n    this.createNodes();\n    this.createStars();\n    this.animate();\n    this.addEventListeners();\n  },\n  beforeDestroy() {\n    this.cleanup();\n  },\n  methods: {\n    initThree() {\n      // 创建场景\n      this.scene = new THREE.Scene();\n\n      // 创建相机\n      const container = this.$refs.canvasContainer;\n      const width = container.clientWidth;\n      const height = container.clientHeight;\n      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);\n      this.camera.position.z = 3;\n\n      // 创建渲染器\n      this.renderer = new THREE.WebGLRenderer({\n        antialias: true,\n        alpha: true\n      });\n      this.renderer.setSize(width, height);\n      this.renderer.setClearColor(0x000000, 0);\n      container.appendChild(this.renderer.domElement);\n\n      // 添加环境光\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.6);\n      this.scene.add(ambientLight);\n\n      // 添加方向光\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n      directionalLight.position.set(1, 1, 1);\n      this.scene.add(directionalLight);\n    },\n    createGlobe() {\n      // 创建地球几何体\n      const geometry = new THREE.SphereGeometry(1, 64, 64);\n\n      // 创建地球材质 - 使用更真实的地球效果\n      const material = new THREE.MeshPhongMaterial({\n        color: 0x2E86AB,\n        transparent: true,\n        opacity: 0.9,\n        shininess: 100,\n        wireframe: false\n      });\n      this.globe = new THREE.Mesh(geometry, material);\n      this.scene.add(this.globe);\n\n      // 添加地球轮廓线 - 经纬线效果\n      const wireframeGeometry = new THREE.SphereGeometry(1.005, 32, 16);\n      const wireframeMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        wireframe: true,\n        transparent: true,\n        opacity: 0.4\n      });\n      const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial);\n      this.scene.add(wireframe);\n\n      // 添加大气层效果\n      const atmosphereGeometry = new THREE.SphereGeometry(1.1, 32, 32);\n      const atmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4A90FF,\n        transparent: true,\n        opacity: 0.1,\n        side: THREE.BackSide\n      });\n      const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);\n      this.scene.add(atmosphere);\n    },\n    createNodes() {\n      this.computeNodes.forEach(nodeData => {\n        const node = this.createNode(nodeData);\n        this.nodes.push(node);\n        this.scene.add(node.mesh);\n      });\n\n      // 创建节点间的连接线\n      this.createConnections();\n    },\n    createNode(nodeData) {\n      // 将经纬度转换为3D坐标\n      const phi = (90 - nodeData.lat) * (Math.PI / 180);\n      const theta = (nodeData.lng + 180) * (Math.PI / 180);\n      const x = -(1.05 * Math.sin(phi) * Math.cos(theta));\n      const y = 1.05 * Math.cos(phi);\n      const z = 1.05 * Math.sin(phi) * Math.sin(theta);\n\n      // 创建节点几何体\n      const geometry = new THREE.SphereGeometry(0.02, 16, 16);\n      const material = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true\n      });\n      const mesh = new THREE.Mesh(geometry, material);\n      mesh.position.set(x, y, z);\n\n      // 创建光环效果\n      const ringGeometry = new THREE.RingGeometry(0.03, 0.05, 16);\n      const ringMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        transparent: true,\n        opacity: 0.6,\n        side: THREE.DoubleSide\n      });\n      const ring = new THREE.Mesh(ringGeometry, ringMaterial);\n      ring.position.copy(mesh.position);\n      ring.lookAt(new THREE.Vector3(0, 0, 0));\n      this.scene.add(ring);\n      return {\n        mesh,\n        ring,\n        data: nodeData,\n        position: {\n          x,\n          y,\n          z\n        }\n      };\n    },\n    createConnections() {\n      // 创建主要节点间的连接线\n      const connections = [[0, 1],\n      // 北京-上海\n      [1, 2],\n      // 上海-深圳\n      [0, 3],\n      // 北京-成都\n      [1, 4],\n      // 上海-杭州\n      [2, 5] // 深圳-新加坡\n      ];\n\n      connections.forEach(([startIdx, endIdx]) => {\n        const startNode = this.nodes[startIdx];\n        const endNode = this.nodes[endIdx];\n        if (startNode && endNode) {\n          const curve = new THREE.QuadraticBezierCurve3(new THREE.Vector3(startNode.position.x, startNode.position.y, startNode.position.z), new THREE.Vector3(0, 0, 0),\n          // 控制点在地球中心上方\n          new THREE.Vector3(endNode.position.x, endNode.position.y, endNode.position.z));\n          const points = curve.getPoints(50);\n          const geometry = new THREE.BufferGeometry().setFromPoints(points);\n          const material = new THREE.LineBasicMaterial({\n            color: 0x4A90FF,\n            transparent: true,\n            opacity: 0.6\n          });\n          const line = new THREE.Line(geometry, material);\n          this.scene.add(line);\n        }\n      });\n    },\n    animate() {\n      this.animationId = requestAnimationFrame(this.animate);\n\n      // 旋转地球\n      if (this.globe) {\n        this.globe.rotation.y += 0.005;\n      }\n\n      // 节点脉冲动画\n      this.nodes.forEach((node, index) => {\n        const time = Date.now() * 0.001;\n        const scale = 1 + Math.sin(time * 2 + index) * 0.3;\n        node.mesh.scale.setScalar(scale);\n\n        // 光环旋转\n        if (node.ring) {\n          node.ring.rotation.z += 0.02;\n        }\n      });\n      this.renderer.render(this.scene, this.camera);\n    },\n    addEventListeners() {\n      window.addEventListener('resize', this.onWindowResize);\n      this.renderer.domElement.addEventListener('mousemove', this.onMouseMove);\n      this.renderer.domElement.addEventListener('click', this.onMouseClick);\n    },\n    onWindowResize() {\n      const container = this.$refs.canvasContainer;\n      const width = container.clientWidth;\n      const height = container.clientHeight;\n      this.camera.aspect = width / height;\n      this.camera.updateProjectionMatrix();\n      this.renderer.setSize(width, height);\n    },\n    onMouseMove(event) {\n      this.mousePosition.x = event.clientX;\n      this.mousePosition.y = event.clientY;\n\n      // 射线检测\n      const mouse = new THREE.Vector2();\n      const rect = this.renderer.domElement.getBoundingClientRect();\n      mouse.x = (event.clientX - rect.left) / rect.width * 2 - 1;\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;\n      const raycaster = new THREE.Raycaster();\n      raycaster.setFromCamera(mouse, this.camera);\n      const intersects = raycaster.intersectObjects(this.nodes.map(n => n.mesh));\n      if (intersects.length > 0) {\n        const intersectedNode = this.nodes.find(n => n.mesh === intersects[0].object);\n        this.selectedNode = intersectedNode ? intersectedNode.data : null;\n        this.renderer.domElement.style.cursor = 'pointer';\n      } else {\n        this.selectedNode = null;\n        this.renderer.domElement.style.cursor = 'default';\n      }\n    },\n    onMouseClick() {\n      if (this.selectedNode) {\n        this.$emit('node-selected', this.selectedNode);\n      }\n    },\n    cleanup() {\n      if (this.animationId) {\n        cancelAnimationFrame(this.animationId);\n      }\n      window.removeEventListener('resize', this.onWindowResize);\n      if (this.renderer) {\n        this.renderer.dispose();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["THREE", "name", "data", "scene", "camera", "renderer", "globe", "nodes", "selectedNode", "mousePosition", "x", "y", "animationId", "computeNodes", "lat", "lng", "description", "gpuCount", "computePower", "computed", "nodeInfoStyle", "left", "top", "mounted", "initThree", "createGlobe", "createNodes", "createStars", "animate", "addEventListeners", "<PERSON><PERSON><PERSON><PERSON>", "cleanup", "methods", "Scene", "container", "$refs", "canvasContainer", "width", "clientWidth", "height", "clientHeight", "PerspectiveCamera", "position", "z", "WebGLRenderer", "antialias", "alpha", "setSize", "setClearColor", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "add", "directionalLight", "DirectionalLight", "set", "geometry", "SphereGeometry", "material", "MeshPhongMaterial", "color", "transparent", "opacity", "shininess", "wireframe", "<PERSON><PERSON>", "wireframeGeometry", "wireframeMaterial", "MeshBasicMaterial", "atmosphereGeometry", "atmosphereMaterial", "side", "BackSide", "atmosphere", "for<PERSON>ach", "nodeData", "node", "createNode", "push", "mesh", "createConnections", "phi", "Math", "PI", "theta", "sin", "cos", "ringGeometry", "RingGeometry", "ringMaterial", "DoubleSide", "ring", "copy", "lookAt", "Vector3", "connections", "startIdx", "endIdx", "startNode", "endNode", "curve", "QuadraticBezierCurve3", "points", "getPoints", "BufferGeometry", "setFromPoints", "LineBasicMaterial", "line", "Line", "requestAnimationFrame", "rotation", "index", "time", "Date", "now", "scale", "setScalar", "render", "window", "addEventListener", "onWindowResize", "onMouseMove", "onMouseClick", "aspect", "updateProjectionMatrix", "event", "clientX", "clientY", "mouse", "Vector2", "rect", "getBoundingClientRect", "raycaster", "Raycaster", "setFromCamera", "intersects", "intersectObjects", "map", "n", "length", "intersectedNode", "find", "object", "style", "cursor", "$emit", "cancelAnimationFrame", "removeEventListener", "dispose"], "sources": ["src/components/common/Globe3D.vue"], "sourcesContent": ["<template>\n  <div class=\"globe-container\" ref=\"globeContainer\">\n    <div class=\"globe-canvas\" ref=\"canvasContainer\"></div>\n    <div class=\"globe-overlay\">\n      <div class=\"globe-title\">全球算力布局</div>\n      <div class=\"globe-subtitle\">分布式高性能计算网络</div>\n      <div class=\"node-info\" v-if=\"selectedNode\" :style=\"nodeInfoStyle\">\n        <h4>{{ selectedNode.name }}</h4>\n        <p>{{ selectedNode.description }}</p>\n        <div class=\"node-stats\">\n          <span>GPU节点: {{ selectedNode.gpuCount }}</span>\n          <span>算力: {{ selectedNode.computePower }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as THREE from 'three'\n\nexport default {\n  name: 'Globe3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      globe: null,\n      nodes: [],\n      selectedNode: null,\n      mousePosition: { x: 0, y: 0 },\n      animationId: null,\n      // 全球算力节点数据\n      computeNodes: [\n        {\n          name: '北京节点',\n          lat: 39.9042,\n          lng: 116.4074,\n          description: '华北地区主要算力中心',\n          gpuCount: '2000+',\n          computePower: '500 PFLOPS'\n        },\n        {\n          name: '上海节点',\n          lat: 31.2304,\n          lng: 121.4737,\n          description: '华东地区核心算力枢纽',\n          gpuCount: '1800+',\n          computePower: '450 PFLOPS'\n        },\n        {\n          name: '深圳节点',\n          lat: 22.3193,\n          lng: 114.1694,\n          description: '华南地区智算中心',\n          gpuCount: '1500+',\n          computePower: '380 PFLOPS'\n        },\n        {\n          name: '成都节点',\n          lat: 30.5728,\n          lng: 104.0668,\n          description: '西南地区算力基地',\n          gpuCount: '1200+',\n          computePower: '300 PFLOPS'\n        },\n        {\n          name: '杭州节点',\n          lat: 30.2741,\n          lng: 120.1551,\n          description: '长三角算力集群',\n          gpuCount: '1000+',\n          computePower: '250 PFLOPS'\n        },\n        {\n          name: '新加坡节点',\n          lat: 1.3521,\n          lng: 103.8198,\n          description: '东南亚算力中心',\n          gpuCount: '800+',\n          computePower: '200 PFLOPS'\n        }\n      ]\n    }\n  },\n  computed: {\n    nodeInfoStyle() {\n      return {\n        left: this.mousePosition.x + 20 + 'px',\n        top: this.mousePosition.y - 50 + 'px'\n      }\n    }\n  },\n  mounted() {\n    this.initThree()\n    this.createGlobe()\n    this.createNodes()\n    this.createStars()\n    this.animate()\n    this.addEventListeners()\n  },\n  beforeDestroy() {\n    this.cleanup()\n  },\n  methods: {\n    initThree() {\n      // 创建场景\n      this.scene = new THREE.Scene()\n      \n      // 创建相机\n      const container = this.$refs.canvasContainer\n      const width = container.clientWidth\n      const height = container.clientHeight\n      \n      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)\n      this.camera.position.z = 3\n      \n      // 创建渲染器\n      this.renderer = new THREE.WebGLRenderer({ \n        antialias: true, \n        alpha: true \n      })\n      this.renderer.setSize(width, height)\n      this.renderer.setClearColor(0x000000, 0)\n      container.appendChild(this.renderer.domElement)\n      \n      // 添加环境光\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)\n      this.scene.add(ambientLight)\n      \n      // 添加方向光\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)\n      directionalLight.position.set(1, 1, 1)\n      this.scene.add(directionalLight)\n    },\n    \n    createGlobe() {\n      // 创建地球几何体\n      const geometry = new THREE.SphereGeometry(1, 64, 64)\n\n      // 创建地球材质 - 使用更真实的地球效果\n      const material = new THREE.MeshPhongMaterial({\n        color: 0x2E86AB,\n        transparent: true,\n        opacity: 0.9,\n        shininess: 100,\n        wireframe: false\n      })\n\n      this.globe = new THREE.Mesh(geometry, material)\n      this.scene.add(this.globe)\n\n      // 添加地球轮廓线 - 经纬线效果\n      const wireframeGeometry = new THREE.SphereGeometry(1.005, 32, 16)\n      const wireframeMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        wireframe: true,\n        transparent: true,\n        opacity: 0.4\n      })\n      const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial)\n      this.scene.add(wireframe)\n\n      // 添加大气层效果\n      const atmosphereGeometry = new THREE.SphereGeometry(1.1, 32, 32)\n      const atmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4A90FF,\n        transparent: true,\n        opacity: 0.1,\n        side: THREE.BackSide\n      })\n      const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial)\n      this.scene.add(atmosphere)\n    },\n    \n    createNodes() {\n      this.computeNodes.forEach(nodeData => {\n        const node = this.createNode(nodeData)\n        this.nodes.push(node)\n        this.scene.add(node.mesh)\n      })\n\n      // 创建节点间的连接线\n      this.createConnections()\n    },\n    \n    createNode(nodeData) {\n      // 将经纬度转换为3D坐标\n      const phi = (90 - nodeData.lat) * (Math.PI / 180)\n      const theta = (nodeData.lng + 180) * (Math.PI / 180)\n      \n      const x = -(1.05 * Math.sin(phi) * Math.cos(theta))\n      const y = 1.05 * Math.cos(phi)\n      const z = 1.05 * Math.sin(phi) * Math.sin(theta)\n      \n      // 创建节点几何体\n      const geometry = new THREE.SphereGeometry(0.02, 16, 16)\n      const material = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true\n      })\n      \n      const mesh = new THREE.Mesh(geometry, material)\n      mesh.position.set(x, y, z)\n      \n      // 创建光环效果\n      const ringGeometry = new THREE.RingGeometry(0.03, 0.05, 16)\n      const ringMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        transparent: true,\n        opacity: 0.6,\n        side: THREE.DoubleSide\n      })\n      const ring = new THREE.Mesh(ringGeometry, ringMaterial)\n      ring.position.copy(mesh.position)\n      ring.lookAt(new THREE.Vector3(0, 0, 0))\n      this.scene.add(ring)\n      \n      return {\n        mesh,\n        ring,\n        data: nodeData,\n        position: { x, y, z }\n      }\n    },\n\n    createConnections() {\n      // 创建主要节点间的连接线\n      const connections = [\n        [0, 1], // 北京-上海\n        [1, 2], // 上海-深圳\n        [0, 3], // 北京-成都\n        [1, 4], // 上海-杭州\n        [2, 5]  // 深圳-新加坡\n      ]\n\n      connections.forEach(([startIdx, endIdx]) => {\n        const startNode = this.nodes[startIdx]\n        const endNode = this.nodes[endIdx]\n\n        if (startNode && endNode) {\n          const curve = new THREE.QuadraticBezierCurve3(\n            new THREE.Vector3(startNode.position.x, startNode.position.y, startNode.position.z),\n            new THREE.Vector3(0, 0, 0), // 控制点在地球中心上方\n            new THREE.Vector3(endNode.position.x, endNode.position.y, endNode.position.z)\n          )\n\n          const points = curve.getPoints(50)\n          const geometry = new THREE.BufferGeometry().setFromPoints(points)\n          const material = new THREE.LineBasicMaterial({\n            color: 0x4A90FF,\n            transparent: true,\n            opacity: 0.6\n          })\n\n          const line = new THREE.Line(geometry, material)\n          this.scene.add(line)\n        }\n      })\n    },\n    \n    animate() {\n      this.animationId = requestAnimationFrame(this.animate)\n      \n      // 旋转地球\n      if (this.globe) {\n        this.globe.rotation.y += 0.005\n      }\n      \n      // 节点脉冲动画\n      this.nodes.forEach((node, index) => {\n        const time = Date.now() * 0.001\n        const scale = 1 + Math.sin(time * 2 + index) * 0.3\n        node.mesh.scale.setScalar(scale)\n        \n        // 光环旋转\n        if (node.ring) {\n          node.ring.rotation.z += 0.02\n        }\n      })\n      \n      this.renderer.render(this.scene, this.camera)\n    },\n    \n    addEventListeners() {\n      window.addEventListener('resize', this.onWindowResize)\n      this.renderer.domElement.addEventListener('mousemove', this.onMouseMove)\n      this.renderer.domElement.addEventListener('click', this.onMouseClick)\n    },\n    \n    onWindowResize() {\n      const container = this.$refs.canvasContainer\n      const width = container.clientWidth\n      const height = container.clientHeight\n      \n      this.camera.aspect = width / height\n      this.camera.updateProjectionMatrix()\n      this.renderer.setSize(width, height)\n    },\n    \n    onMouseMove(event) {\n      this.mousePosition.x = event.clientX\n      this.mousePosition.y = event.clientY\n      \n      // 射线检测\n      const mouse = new THREE.Vector2()\n      const rect = this.renderer.domElement.getBoundingClientRect()\n      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1\n      \n      const raycaster = new THREE.Raycaster()\n      raycaster.setFromCamera(mouse, this.camera)\n      \n      const intersects = raycaster.intersectObjects(this.nodes.map(n => n.mesh))\n      \n      if (intersects.length > 0) {\n        const intersectedNode = this.nodes.find(n => n.mesh === intersects[0].object)\n        this.selectedNode = intersectedNode ? intersectedNode.data : null\n        this.renderer.domElement.style.cursor = 'pointer'\n      } else {\n        this.selectedNode = null\n        this.renderer.domElement.style.cursor = 'default'\n      }\n    },\n    \n    onMouseClick() {\n      if (this.selectedNode) {\n        this.$emit('node-selected', this.selectedNode)\n      }\n    },\n    \n    cleanup() {\n      if (this.animationId) {\n        cancelAnimationFrame(this.animationId)\n      }\n      \n      window.removeEventListener('resize', this.onWindowResize)\n      \n      if (this.renderer) {\n        this.renderer.dispose()\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.globe-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n\n.globe-canvas {\n  width: 100%;\n  height: 100%;\n}\n\n.globe-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n  color: white;\n}\n\n.globe-title {\n  font-size: 32px;\n  font-weight: 600;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);\n}\n\n.globe-subtitle {\n  font-size: 18px;\n  opacity: 0.9;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n}\n\n.node-info {\n  position: fixed;\n  background: rgba(20, 112, 255, 0.95);\n  color: white;\n  padding: 15px;\n  border-radius: 8px;\n  box-shadow: 0 4px 15px rgba(0,0,0,0.3);\n  pointer-events: none;\n  z-index: 1000;\n  min-width: 200px;\n  backdrop-filter: blur(10px);\n}\n\n.node-info h4 {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.node-info p {\n  margin: 0 0 10px 0;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.node-stats {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.node-stats span {\n  font-size: 12px;\n  opacity: 0.8;\n}\n\n@media (max-width: 768px) {\n  .globe-title {\n    font-size: 24px;\n  }\n  \n  .globe-subtitle {\n    font-size: 16px;\n  }\n  \n  .node-info {\n    min-width: 180px;\n    padding: 12px;\n  }\n}\n</style>\n"], "mappings": ";AAmBA,YAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,KAAA;MACAC,MAAA;MACAC,QAAA;MACAC,KAAA;MACAC,KAAA;MACAC,YAAA;MACAC,aAAA;QAAAC,CAAA;QAAAC,CAAA;MAAA;MACAC,WAAA;MACA;MACAC,YAAA,GACA;QACAZ,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAjB,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAjB,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAjB,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAjB,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAjB,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA;IAEA;EACA;EACAC,QAAA;IACAC,cAAA;MACA;QACAC,IAAA,OAAAZ,aAAA,CAAAC,CAAA;QACAY,GAAA,OAAAb,aAAA,CAAAE,CAAA;MACA;IACA;EACA;EACAY,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,WAAA;IACA,KAAAC,WAAA;IACA,KAAAC,WAAA;IACA,KAAAC,OAAA;IACA,KAAAC,iBAAA;EACA;EACAC,cAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAR,UAAA;MACA;MACA,KAAArB,KAAA,OAAAH,KAAA,CAAAiC,KAAA;;MAEA;MACA,MAAAC,SAAA,QAAAC,KAAA,CAAAC,eAAA;MACA,MAAAC,KAAA,GAAAH,SAAA,CAAAI,WAAA;MACA,MAAAC,MAAA,GAAAL,SAAA,CAAAM,YAAA;MAEA,KAAApC,MAAA,OAAAJ,KAAA,CAAAyC,iBAAA,KAAAJ,KAAA,GAAAE,MAAA;MACA,KAAAnC,MAAA,CAAAsC,QAAA,CAAAC,CAAA;;MAEA;MACA,KAAAtC,QAAA,OAAAL,KAAA,CAAA4C,aAAA;QACAC,SAAA;QACAC,KAAA;MACA;MACA,KAAAzC,QAAA,CAAA0C,OAAA,CAAAV,KAAA,EAAAE,MAAA;MACA,KAAAlC,QAAA,CAAA2C,aAAA;MACAd,SAAA,CAAAe,WAAA,MAAA5C,QAAA,CAAA6C,UAAA;;MAEA;MACA,MAAAC,YAAA,OAAAnD,KAAA,CAAAoD,YAAA;MACA,KAAAjD,KAAA,CAAAkD,GAAA,CAAAF,YAAA;;MAEA;MACA,MAAAG,gBAAA,OAAAtD,KAAA,CAAAuD,gBAAA;MACAD,gBAAA,CAAAZ,QAAA,CAAAc,GAAA;MACA,KAAArD,KAAA,CAAAkD,GAAA,CAAAC,gBAAA;IACA;IAEA7B,YAAA;MACA;MACA,MAAAgC,QAAA,OAAAzD,KAAA,CAAA0D,cAAA;;MAEA;MACA,MAAAC,QAAA,OAAA3D,KAAA,CAAA4D,iBAAA;QACAC,KAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;MACA;MAEA,KAAA3D,KAAA,OAAAN,KAAA,CAAAkE,IAAA,CAAAT,QAAA,EAAAE,QAAA;MACA,KAAAxD,KAAA,CAAAkD,GAAA,MAAA/C,KAAA;;MAEA;MACA,MAAA6D,iBAAA,OAAAnE,KAAA,CAAA0D,cAAA;MACA,MAAAU,iBAAA,OAAApE,KAAA,CAAAqE,iBAAA;QACAR,KAAA;QACAI,SAAA;QACAH,WAAA;QACAC,OAAA;MACA;MACA,MAAAE,SAAA,OAAAjE,KAAA,CAAAkE,IAAA,CAAAC,iBAAA,EAAAC,iBAAA;MACA,KAAAjE,KAAA,CAAAkD,GAAA,CAAAY,SAAA;;MAEA;MACA,MAAAK,kBAAA,OAAAtE,KAAA,CAAA0D,cAAA;MACA,MAAAa,kBAAA,OAAAvE,KAAA,CAAAqE,iBAAA;QACAR,KAAA;QACAC,WAAA;QACAC,OAAA;QACAS,IAAA,EAAAxE,KAAA,CAAAyE;MACA;MACA,MAAAC,UAAA,OAAA1E,KAAA,CAAAkE,IAAA,CAAAI,kBAAA,EAAAC,kBAAA;MACA,KAAApE,KAAA,CAAAkD,GAAA,CAAAqB,UAAA;IACA;IAEAhD,YAAA;MACA,KAAAb,YAAA,CAAA8D,OAAA,CAAAC,QAAA;QACA,MAAAC,IAAA,QAAAC,UAAA,CAAAF,QAAA;QACA,KAAArE,KAAA,CAAAwE,IAAA,CAAAF,IAAA;QACA,KAAA1E,KAAA,CAAAkD,GAAA,CAAAwB,IAAA,CAAAG,IAAA;MACA;;MAEA;MACA,KAAAC,iBAAA;IACA;IAEAH,WAAAF,QAAA;MACA;MACA,MAAAM,GAAA,SAAAN,QAAA,CAAA9D,GAAA,KAAAqE,IAAA,CAAAC,EAAA;MACA,MAAAC,KAAA,IAAAT,QAAA,CAAA7D,GAAA,WAAAoE,IAAA,CAAAC,EAAA;MAEA,MAAA1E,CAAA,YAAAyE,IAAA,CAAAG,GAAA,CAAAJ,GAAA,IAAAC,IAAA,CAAAI,GAAA,CAAAF,KAAA;MACA,MAAA1E,CAAA,UAAAwE,IAAA,CAAAI,GAAA,CAAAL,GAAA;MACA,MAAAvC,CAAA,UAAAwC,IAAA,CAAAG,GAAA,CAAAJ,GAAA,IAAAC,IAAA,CAAAG,GAAA,CAAAD,KAAA;;MAEA;MACA,MAAA5B,QAAA,OAAAzD,KAAA,CAAA0D,cAAA;MACA,MAAAC,QAAA,OAAA3D,KAAA,CAAAqE,iBAAA;QACAR,KAAA;QACAC,WAAA;MACA;MAEA,MAAAkB,IAAA,OAAAhF,KAAA,CAAAkE,IAAA,CAAAT,QAAA,EAAAE,QAAA;MACAqB,IAAA,CAAAtC,QAAA,CAAAc,GAAA,CAAA9C,CAAA,EAAAC,CAAA,EAAAgC,CAAA;;MAEA;MACA,MAAA6C,YAAA,OAAAxF,KAAA,CAAAyF,YAAA;MACA,MAAAC,YAAA,OAAA1F,KAAA,CAAAqE,iBAAA;QACAR,KAAA;QACAC,WAAA;QACAC,OAAA;QACAS,IAAA,EAAAxE,KAAA,CAAA2F;MACA;MACA,MAAAC,IAAA,OAAA5F,KAAA,CAAAkE,IAAA,CAAAsB,YAAA,EAAAE,YAAA;MACAE,IAAA,CAAAlD,QAAA,CAAAmD,IAAA,CAAAb,IAAA,CAAAtC,QAAA;MACAkD,IAAA,CAAAE,MAAA,KAAA9F,KAAA,CAAA+F,OAAA;MACA,KAAA5F,KAAA,CAAAkD,GAAA,CAAAuC,IAAA;MAEA;QACAZ,IAAA;QACAY,IAAA;QACA1F,IAAA,EAAA0E,QAAA;QACAlC,QAAA;UAAAhC,CAAA;UAAAC,CAAA;UAAAgC;QAAA;MACA;IACA;IAEAsC,kBAAA;MACA;MACA,MAAAe,WAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;;MAEAA,WAAA,CAAArB,OAAA,GAAAsB,QAAA,EAAAC,MAAA;QACA,MAAAC,SAAA,QAAA5F,KAAA,CAAA0F,QAAA;QACA,MAAAG,OAAA,QAAA7F,KAAA,CAAA2F,MAAA;QAEA,IAAAC,SAAA,IAAAC,OAAA;UACA,MAAAC,KAAA,OAAArG,KAAA,CAAAsG,qBAAA,CACA,IAAAtG,KAAA,CAAA+F,OAAA,CAAAI,SAAA,CAAAzD,QAAA,CAAAhC,CAAA,EAAAyF,SAAA,CAAAzD,QAAA,CAAA/B,CAAA,EAAAwF,SAAA,CAAAzD,QAAA,CAAAC,CAAA,GACA,IAAA3C,KAAA,CAAA+F,OAAA;UAAA;UACA,IAAA/F,KAAA,CAAA+F,OAAA,CAAAK,OAAA,CAAA1D,QAAA,CAAAhC,CAAA,EAAA0F,OAAA,CAAA1D,QAAA,CAAA/B,CAAA,EAAAyF,OAAA,CAAA1D,QAAA,CAAAC,CAAA,EACA;UAEA,MAAA4D,MAAA,GAAAF,KAAA,CAAAG,SAAA;UACA,MAAA/C,QAAA,OAAAzD,KAAA,CAAAyG,cAAA,GAAAC,aAAA,CAAAH,MAAA;UACA,MAAA5C,QAAA,OAAA3D,KAAA,CAAA2G,iBAAA;YACA9C,KAAA;YACAC,WAAA;YACAC,OAAA;UACA;UAEA,MAAA6C,IAAA,OAAA5G,KAAA,CAAA6G,IAAA,CAAApD,QAAA,EAAAE,QAAA;UACA,KAAAxD,KAAA,CAAAkD,GAAA,CAAAuD,IAAA;QACA;MACA;IACA;IAEAhF,QAAA;MACA,KAAAhB,WAAA,GAAAkG,qBAAA,MAAAlF,OAAA;;MAEA;MACA,SAAAtB,KAAA;QACA,KAAAA,KAAA,CAAAyG,QAAA,CAAApG,CAAA;MACA;;MAEA;MACA,KAAAJ,KAAA,CAAAoE,OAAA,EAAAE,IAAA,EAAAmC,KAAA;QACA,MAAAC,IAAA,GAAAC,IAAA,CAAAC,GAAA;QACA,MAAAC,KAAA,OAAAjC,IAAA,CAAAG,GAAA,CAAA2B,IAAA,OAAAD,KAAA;QACAnC,IAAA,CAAAG,IAAA,CAAAoC,KAAA,CAAAC,SAAA,CAAAD,KAAA;;QAEA;QACA,IAAAvC,IAAA,CAAAe,IAAA;UACAf,IAAA,CAAAe,IAAA,CAAAmB,QAAA,CAAApE,CAAA;QACA;MACA;MAEA,KAAAtC,QAAA,CAAAiH,MAAA,MAAAnH,KAAA,OAAAC,MAAA;IACA;IAEAyB,kBAAA;MACA0F,MAAA,CAAAC,gBAAA,gBAAAC,cAAA;MACA,KAAApH,QAAA,CAAA6C,UAAA,CAAAsE,gBAAA,mBAAAE,WAAA;MACA,KAAArH,QAAA,CAAA6C,UAAA,CAAAsE,gBAAA,eAAAG,YAAA;IACA;IAEAF,eAAA;MACA,MAAAvF,SAAA,QAAAC,KAAA,CAAAC,eAAA;MACA,MAAAC,KAAA,GAAAH,SAAA,CAAAI,WAAA;MACA,MAAAC,MAAA,GAAAL,SAAA,CAAAM,YAAA;MAEA,KAAApC,MAAA,CAAAwH,MAAA,GAAAvF,KAAA,GAAAE,MAAA;MACA,KAAAnC,MAAA,CAAAyH,sBAAA;MACA,KAAAxH,QAAA,CAAA0C,OAAA,CAAAV,KAAA,EAAAE,MAAA;IACA;IAEAmF,YAAAI,KAAA;MACA,KAAArH,aAAA,CAAAC,CAAA,GAAAoH,KAAA,CAAAC,OAAA;MACA,KAAAtH,aAAA,CAAAE,CAAA,GAAAmH,KAAA,CAAAE,OAAA;;MAEA;MACA,MAAAC,KAAA,OAAAjI,KAAA,CAAAkI,OAAA;MACA,MAAAC,IAAA,QAAA9H,QAAA,CAAA6C,UAAA,CAAAkF,qBAAA;MACAH,KAAA,CAAAvH,CAAA,IAAAoH,KAAA,CAAAC,OAAA,GAAAI,IAAA,CAAA9G,IAAA,IAAA8G,IAAA,CAAA9F,KAAA;MACA4F,KAAA,CAAAtH,CAAA,MAAAmH,KAAA,CAAAE,OAAA,GAAAG,IAAA,CAAA7G,GAAA,IAAA6G,IAAA,CAAA5F,MAAA;MAEA,MAAA8F,SAAA,OAAArI,KAAA,CAAAsI,SAAA;MACAD,SAAA,CAAAE,aAAA,CAAAN,KAAA,OAAA7H,MAAA;MAEA,MAAAoI,UAAA,GAAAH,SAAA,CAAAI,gBAAA,MAAAlI,KAAA,CAAAmI,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAA3D,IAAA;MAEA,IAAAwD,UAAA,CAAAI,MAAA;QACA,MAAAC,eAAA,QAAAtI,KAAA,CAAAuI,IAAA,CAAAH,CAAA,IAAAA,CAAA,CAAA3D,IAAA,KAAAwD,UAAA,IAAAO,MAAA;QACA,KAAAvI,YAAA,GAAAqI,eAAA,GAAAA,eAAA,CAAA3I,IAAA;QACA,KAAAG,QAAA,CAAA6C,UAAA,CAAA8F,KAAA,CAAAC,MAAA;MACA;QACA,KAAAzI,YAAA;QACA,KAAAH,QAAA,CAAA6C,UAAA,CAAA8F,KAAA,CAAAC,MAAA;MACA;IACA;IAEAtB,aAAA;MACA,SAAAnH,YAAA;QACA,KAAA0I,KAAA,uBAAA1I,YAAA;MACA;IACA;IAEAuB,QAAA;MACA,SAAAnB,WAAA;QACAuI,oBAAA,MAAAvI,WAAA;MACA;MAEA2G,MAAA,CAAA6B,mBAAA,gBAAA3B,cAAA;MAEA,SAAApH,QAAA;QACA,KAAAA,QAAA,CAAAgJ,OAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}