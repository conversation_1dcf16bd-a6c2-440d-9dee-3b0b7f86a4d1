{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"Layout\", [_c(\"div\", {\n    staticClass: \"layout-container\",\n    staticStyle: {\n      width: \"100%\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"page-header\"\n  }, [_c(\"div\", {\n    staticClass: \"am-container\"\n  }, [_c(\"h1\", {\n    staticClass: \"page-header-title\"\n  }, [_vm._v(\"公司动态\")])])]), _c(\"div\", {\n    staticClass: \"breadcrumb-box\"\n  }, [_c(\"div\", {\n    staticClass: \"am-container\"\n  }, [_c(\"ol\", {\n    staticClass: \"am-breadcrumb\"\n  }, [_c(\"li\", [_c(\"router-link\", {\n    attrs: {\n      to: \"/\"\n    }\n  }, [_vm._v(\"首页\")])], 1), _c(\"li\", {\n    staticClass: \"am-active\"\n  }, [_vm._v(\"公司动态\")])])])])]), _c(\"div\", {\n    staticClass: \"section\"\n  }, [_c(\"div\", {\n    staticClass: \"container\",\n    staticStyle: {\n      \"max-width\": \"1160px\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"section--header\"\n  }, [_c(\"h2\", {\n    staticClass: \"section--title\"\n  }, [_vm._v(\"最近新闻\")]), _c(\"p\", {\n    staticClass: \"section--description\"\n  }, [_vm._v(\" 云适配与中建材信息技术股份有限公司（以下简称“中建信息”）联合举办的“战略 \"), _c(\"br\"), _vm._v(\"合作签约仪式暨全国跨屏行动启动大会”在北京成功举办。 \")])]), _c(\"div\", {\n    staticClass: \"news-contaier\"\n  }, [_c(\"div\", {\n    staticClass: \"blog\"\n  }, [_c(\"div\", {\n    staticClass: \"am-g\"\n  }, _vm._l(_vm.articles.records, function (article, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"am-u-lg-4 am-u-md-6 am-u-end\"\n    }, [_c(\"div\", {\n      staticClass: \"article\"\n    }, [_c(\"div\", {\n      staticClass: \"article-img\"\n    }, [_c(\"img\", {\n      attrs: {\n        src: article.cover,\n        alt: \"\"\n      }\n    })]), _c(\"div\", {\n      staticClass: \"article-header\"\n    }, [_c(\"h2\", [_c(\"router-link\", {\n      attrs: {\n        to: {\n          name: \"newsDetails\",\n          params: {\n            newsId: article.articleId\n          }\n        },\n        rel: \"\"\n      }\n    }, [_vm._v(_vm._s(article.title))])], 1), _c(\"ul\", {\n      staticClass: \"article--meta\"\n    }, [_c(\"li\", {\n      staticClass: \"article--meta_item -date\"\n    }, [_vm._v(_vm._s(article.createTime))])])]), _c(\"div\", {\n      staticClass: \"article--content\"\n    }, [_c(\"p\", [_vm._v(_vm._s(article.introduction))])]), _c(\"div\", {\n      staticClass: \"article--footer\"\n    }, [_c(\"router-link\", {\n      staticClass: \"link\",\n      attrs: {\n        to: {\n          name: \"newsDetails\",\n          params: {\n            newsId: article.articleId\n          }\n        }\n      }\n    }, [_vm._v(\"查看更多\")])], 1)])]);\n  }), 0), _c(\"ul\", {\n    staticClass: \"am-pagination\",\n    staticStyle: {\n      \"text-align\": \"center\"\n    }\n  }, [_c(\"li\", {\n    class: _vm.pageIndex === 1 ? \"am-disabled\" : \"\",\n    on: {\n      click: function ($event) {\n        return _vm.changeIndex(_vm.pageIndex - 1);\n      }\n    }\n  }, [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"«\")])]), _vm._l(_vm.articles.pages, function (p, index) {\n    return _c(\"li\", {\n      key: index,\n      class: _vm.pageIndex === p ? \"am-active\" : \"\",\n      on: {\n        click: function ($event) {\n          return _vm.changeIndex(p);\n        }\n      }\n    }, [_c(\"a\", {\n      attrs: {\n        href: \"#\"\n      }\n    }, [_vm._v(_vm._s(p))])]);\n  }), _c(\"li\", {\n    class: _vm.pageIndex === _vm.articles.pages ? \"am-disabled\" : \"\",\n    on: {\n      click: function ($event) {\n        return _vm.changeIndex(_vm.pageIndex + 1);\n      }\n    }\n  }, [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"»\")])])], 2)])])])])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "width", "_v", "attrs", "to", "_l", "articles", "records", "article", "index", "key", "src", "cover", "alt", "name", "params", "newsId", "articleId", "rel", "_s", "title", "createTime", "introduction", "class", "pageIndex", "on", "click", "$event", "changeIndex", "href", "pages", "p", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/views/NewsView.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"Layout\", [\n    _c(\n      \"div\",\n      { staticClass: \"layout-container\", staticStyle: { width: \"100%\" } },\n      [\n        _c(\"div\", { staticClass: \"page-header\" }, [\n          _c(\"div\", { staticClass: \"am-container\" }, [\n            _c(\"h1\", { staticClass: \"page-header-title\" }, [\n              _vm._v(\"公司动态\"),\n            ]),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"breadcrumb-box\" }, [\n          _c(\"div\", { staticClass: \"am-container\" }, [\n            _c(\"ol\", { staticClass: \"am-breadcrumb\" }, [\n              _c(\n                \"li\",\n                [_c(\"router-link\", { attrs: { to: \"/\" } }, [_vm._v(\"首页\")])],\n                1\n              ),\n              _c(\"li\", { staticClass: \"am-active\" }, [_vm._v(\"公司动态\")]),\n            ]),\n          ]),\n        ]),\n      ]\n    ),\n    _c(\"div\", { staticClass: \"section\" }, [\n      _c(\n        \"div\",\n        { staticClass: \"container\", staticStyle: { \"max-width\": \"1160px\" } },\n        [\n          _c(\"div\", { staticClass: \"section--header\" }, [\n            _c(\"h2\", { staticClass: \"section--title\" }, [_vm._v(\"最近新闻\")]),\n            _c(\"p\", { staticClass: \"section--description\" }, [\n              _vm._v(\n                \" 云适配与中建材信息技术股份有限公司（以下简称“中建信息”）联合举办的“战略 \"\n              ),\n              _c(\"br\"),\n              _vm._v(\"合作签约仪式暨全国跨屏行动启动大会”在北京成功举办。 \"),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"news-contaier\" }, [\n            _c(\"div\", { staticClass: \"blog\" }, [\n              _c(\n                \"div\",\n                { staticClass: \"am-g\" },\n                _vm._l(_vm.articles.records, function (article, index) {\n                  return _c(\n                    \"div\",\n                    { key: index, staticClass: \"am-u-lg-4 am-u-md-6 am-u-end\" },\n                    [\n                      _c(\"div\", { staticClass: \"article\" }, [\n                        _c(\"div\", { staticClass: \"article-img\" }, [\n                          _c(\"img\", { attrs: { src: article.cover, alt: \"\" } }),\n                        ]),\n                        _c(\"div\", { staticClass: \"article-header\" }, [\n                          _c(\n                            \"h2\",\n                            [\n                              _c(\n                                \"router-link\",\n                                {\n                                  attrs: {\n                                    to: {\n                                      name: \"newsDetails\",\n                                      params: { newsId: article.articleId },\n                                    },\n                                    rel: \"\",\n                                  },\n                                },\n                                [_vm._v(_vm._s(article.title))]\n                              ),\n                            ],\n                            1\n                          ),\n                          _c(\"ul\", { staticClass: \"article--meta\" }, [\n                            _c(\n                              \"li\",\n                              { staticClass: \"article--meta_item -date\" },\n                              [_vm._v(_vm._s(article.createTime))]\n                            ),\n                          ]),\n                        ]),\n                        _c(\"div\", { staticClass: \"article--content\" }, [\n                          _c(\"p\", [_vm._v(_vm._s(article.introduction))]),\n                        ]),\n                        _c(\n                          \"div\",\n                          { staticClass: \"article--footer\" },\n                          [\n                            _c(\n                              \"router-link\",\n                              {\n                                staticClass: \"link\",\n                                attrs: {\n                                  to: {\n                                    name: \"newsDetails\",\n                                    params: { newsId: article.articleId },\n                                  },\n                                },\n                              },\n                              [_vm._v(\"查看更多\")]\n                            ),\n                          ],\n                          1\n                        ),\n                      ]),\n                    ]\n                  )\n                }),\n                0\n              ),\n              _c(\n                \"ul\",\n                {\n                  staticClass: \"am-pagination\",\n                  staticStyle: { \"text-align\": \"center\" },\n                },\n                [\n                  _c(\n                    \"li\",\n                    {\n                      class: _vm.pageIndex === 1 ? \"am-disabled\" : \"\",\n                      on: {\n                        click: function ($event) {\n                          return _vm.changeIndex(_vm.pageIndex - 1)\n                        },\n                      },\n                    },\n                    [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"«\")])]\n                  ),\n                  _vm._l(_vm.articles.pages, function (p, index) {\n                    return _c(\n                      \"li\",\n                      {\n                        key: index,\n                        class: _vm.pageIndex === p ? \"am-active\" : \"\",\n                        on: {\n                          click: function ($event) {\n                            return _vm.changeIndex(p)\n                          },\n                        },\n                      },\n                      [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(_vm._s(p))])]\n                    )\n                  }),\n                  _c(\n                    \"li\",\n                    {\n                      class:\n                        _vm.pageIndex === _vm.articles.pages\n                          ? \"am-disabled\"\n                          : \"\",\n                      on: {\n                        click: function ($event) {\n                          return _vm.changeIndex(_vm.pageIndex + 1)\n                        },\n                      },\n                    },\n                    [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"»\")])]\n                  ),\n                ],\n                2\n              ),\n            ]),\n          ]),\n        ]\n      ),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,QAAQ,EAAE,CAClBA,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,kBAAkB;IAAEC,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EACnE,CACEJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC7CH,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCF,EAAE,CACA,IAAI,EACJ,CAACA,EAAE,CAAC,aAAa,EAAE;IAAEM,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAC3D,CAAC,CACF,EACDL,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACzD,CAAC,CACH,CAAC,CACH,CAAC,CACH,CACF,EACDL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,WAAW;IAAEC,WAAW,EAAE;MAAE,WAAW,EAAE;IAAS;EAAE,CAAC,EACpE,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC7DL,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CAC/CH,GAAG,CAACM,EAAE,CACJ,yCAAyC,CAC1C,EACDL,EAAE,CAAC,IAAI,CAAC,EACRD,GAAG,CAACM,EAAE,CAAC,6BAA6B,CAAC,CACtC,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAO,CAAC,EACvBH,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,QAAQ,CAACC,OAAO,EAAE,UAAUC,OAAO,EAAEC,KAAK,EAAE;IACrD,OAAOZ,EAAE,CACP,KAAK,EACL;MAAEa,GAAG,EAAED,KAAK;MAAEV,WAAW,EAAE;IAA+B,CAAC,EAC3D,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAU,CAAC,EAAE,CACpCF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;MAAEM,KAAK,EAAE;QAAEQ,GAAG,EAAEH,OAAO,CAACI,KAAK;QAAEC,GAAG,EAAE;MAAG;IAAE,CAAC,CAAC,CACtD,CAAC,EACFhB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAiB,CAAC,EAAE,CAC3CF,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CACA,aAAa,EACb;MACEM,KAAK,EAAE;QACLC,EAAE,EAAE;UACFU,IAAI,EAAE,aAAa;UACnBC,MAAM,EAAE;YAAEC,MAAM,EAAER,OAAO,CAACS;UAAU;QACtC,CAAC;QACDC,GAAG,EAAE;MACP;IACF,CAAC,EACD,CAACtB,GAAG,CAACM,EAAE,CAACN,GAAG,CAACuB,EAAE,CAACX,OAAO,CAACY,KAAK,CAAC,CAAC,CAAC,CAChC,CACF,EACD,CAAC,CACF,EACDvB,EAAE,CAAC,IAAI,EAAE;MAAEE,WAAW,EAAE;IAAgB,CAAC,EAAE,CACzCF,EAAE,CACA,IAAI,EACJ;MAAEE,WAAW,EAAE;IAA2B,CAAC,EAC3C,CAACH,GAAG,CAACM,EAAE,CAACN,GAAG,CAACuB,EAAE,CAACX,OAAO,CAACa,UAAU,CAAC,CAAC,CAAC,CACrC,CACF,CAAC,CACH,CAAC,EACFxB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACuB,EAAE,CAACX,OAAO,CAACc,YAAY,CAAC,CAAC,CAAC,CAAC,CAChD,CAAC,EACFzB,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAClC,CACEF,EAAE,CACA,aAAa,EACb;MACEE,WAAW,EAAE,MAAM;MACnBI,KAAK,EAAE;QACLC,EAAE,EAAE;UACFU,IAAI,EAAE,aAAa;UACnBC,MAAM,EAAE;YAAEC,MAAM,EAAER,OAAO,CAACS;UAAU;QACtC;MACF;IACF,CAAC,EACD,CAACrB,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,EACD,CAAC,CACF,CACF,CAAC,CACH,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,EACDL,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE;MAAE,YAAY,EAAE;IAAS;EACxC,CAAC,EACD,CACEH,EAAE,CACA,IAAI,EACJ;IACE0B,KAAK,EAAE3B,GAAG,CAAC4B,SAAS,KAAK,CAAC,GAAG,aAAa,GAAG,EAAE;IAC/CC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAO/B,GAAG,CAACgC,WAAW,CAAChC,GAAG,CAAC4B,SAAS,GAAG,CAAC,CAAC;MAC3C;IACF;EACF,CAAC,EACD,CAAC3B,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAE0B,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACjC,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACnD,EACDN,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,QAAQ,CAACwB,KAAK,EAAE,UAAUC,CAAC,EAAEtB,KAAK,EAAE;IAC7C,OAAOZ,EAAE,CACP,IAAI,EACJ;MACEa,GAAG,EAAED,KAAK;MACVc,KAAK,EAAE3B,GAAG,CAAC4B,SAAS,KAAKO,CAAC,GAAG,WAAW,GAAG,EAAE;MAC7CN,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAO/B,GAAG,CAACgC,WAAW,CAACG,CAAC,CAAC;QAC3B;MACF;IACF,CAAC,EACD,CAAClC,EAAE,CAAC,GAAG,EAAE;MAAEM,KAAK,EAAE;QAAE0B,IAAI,EAAE;MAAI;IAAE,CAAC,EAAE,CAACjC,GAAG,CAACM,EAAE,CAACN,GAAG,CAACuB,EAAE,CAACY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACzD;EACH,CAAC,CAAC,EACFlC,EAAE,CACA,IAAI,EACJ;IACE0B,KAAK,EACH3B,GAAG,CAAC4B,SAAS,KAAK5B,GAAG,CAACU,QAAQ,CAACwB,KAAK,GAChC,aAAa,GACb,EAAE;IACRL,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAO/B,GAAG,CAACgC,WAAW,CAAChC,GAAG,CAAC4B,SAAS,GAAG,CAAC,CAAC;MAC3C;IACF;EACF,CAAC,EACD,CAAC3B,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAE0B,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACjC,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACnD,CACF,EACD,CAAC,CACF,CACF,CAAC,CACH,CAAC,CACH,CACF,CACF,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAI8B,eAAe,GAAG,EAAE;AACxBrC,MAAM,CAACsC,aAAa,GAAG,IAAI;AAE3B,SAAStC,MAAM,EAAEqC,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}