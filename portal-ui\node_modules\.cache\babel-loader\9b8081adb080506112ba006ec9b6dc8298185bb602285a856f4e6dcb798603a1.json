{"ast": null, "code": "// 引入 axios\nimport axios from \"axios\";\nimport Cookies from 'js-cookie';\n\n// let base = '/api';\n// let base = ' http://114.215.252.21/prod-api';\nlet base = ' https://test.tiangongkaiwu.top/prod-api';\n// let base = ' http://192.168.110.109:8080';\n//let base = ' https://tiangongkaiwu.top/prod-api';\n\n//传送json格式的Post请求55\nexport const postAnyData = (url, params) => {\n  return axios({\n    headers: {\n      'authorization': Cookies.get(\"Admin-Token\"),\n      // 必须设置\n      'Content-Type': 'application/x-www-form-urlencoded' // 必须设置\n    },\n\n    method: 'post',\n    url: `${base}${url}`,\n    data: params\n  });\n};\nexport const postJsonData = (url, parmas) => {\n  return axios({\n    headers: {\n      'authorization': Cookies.get(\"Admin-Token\"),\n      // 必须设置\n      'Content-Type': 'application/json;charset=UTF-8' // 必须设置\n    },\n\n    method: 'post',\n    url: `${base}${url}`,\n    data: parmas\n  });\n};\nexport const postLogin = (url, params) => {\n  return axios({\n    headers: {\n      'Content-Type': 'application/x-www-form-urlencoded' // 必须设置\n    },\n\n    method: 'post',\n    url: `${base}${url}`,\n    data: params\n  });\n};\nexport const postNotAuth = (url, params) => {\n  return axios({\n    headers: {\n      'Content-Type': 'application/json;charset=UTF-8' // 必须设置\n    },\n\n    method: 'post',\n    url: `${base}${url}`,\n    data: params\n  });\n};\nexport const getNotAuth = (url, params) => {\n  return axios({\n    headers: {\n      'Content-Type': 'application/json;charset=UTF-8' // 必须设置\n    },\n\n    method: 'get',\n    url: `${base}${url}`,\n    params: params\n  });\n};\nexport const getAnyData = (url, params) => {\n  return axios({\n    headers: {\n      'authorization': Cookies.get(\"Admin-Token\"),\n      // 必须设置\n      'Content-Type': 'application/x-www-form-urlencoded' // 必须设置\n    },\n\n    method: 'get',\n    url: `${base}${url}`,\n    params: params\n  });\n};", "map": {"version": 3, "names": ["axios", "Cookies", "base", "postAnyData", "url", "params", "headers", "get", "method", "data", "postJsonData", "parmas", "postLogin", "postNotAuth", "getNotAuth", "getAnyData"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/api/login.js"], "sourcesContent": ["// 引入 axios\r\nimport axios from \"axios\";\r\n\r\nimport Cookies from 'js-cookie'\r\n\r\n// let base = '/api';\r\n// let base = ' http://114.215.252.21/prod-api';\r\nlet base = ' https://test.tiangongkaiwu.top/prod-api';\r\n   // let base = ' http://192.168.110.109:8080';\r\n//let base = ' https://tiangongkaiwu.top/prod-api';\r\n\r\n//传送json格式的Post请求55\r\nexport const postAnyData=(url,params)=>{\r\n    return axios({\r\n        headers: {\r\n            'authorization': Cookies.get(\"Admin-Token\"), // 必须设置\r\n            'Content-Type': 'application/x-www-form-urlencoded' // 必须设置\r\n        },\r\n        method:'post',\r\n        url:`${base}${url}`,\r\n        data: params,\r\n    })\r\n}\r\nexport const postJsonData =(url,parmas)=>{\r\n    return axios({\r\n        headers:{\r\n            'authorization': Cookies.get(\"Admin-Token\"), // 必须设置\r\n            'Content-Type': 'application/json;charset=UTF-8' // 必须设置\r\n        },\r\n        method:'post',\r\n        url:`${base}${url}`,\r\n        data:parmas\r\n    })\r\n}\r\n\r\nexport const postLogin=(url,params)=> {\r\n    return axios({\r\n        headers: {\r\n            'Content-Type': 'application/x-www-form-urlencoded' // 必须设置\r\n        },\r\n        method: 'post',\r\n        url: `${base}${url}`,\r\n        data: params\r\n    })\r\n}\r\nexport const postNotAuth=(url,params)=> {\r\n    return axios({\r\n        headers: {\r\n            'Content-Type': 'application/json;charset=UTF-8' // 必须设置\r\n        },\r\n        method: 'post',\r\n        url: `${base}${url}`,\r\n        data: params\r\n    })\r\n}\r\nexport const getNotAuth=(url,params)=> {\r\n    return axios({\r\n        headers: {\r\n            'Content-Type': 'application/json;charset=UTF-8' // 必须设置\r\n        },\r\n        method: 'get',\r\n        url: `${base}${url}`,\r\n        params: params\r\n    })\r\n}\r\n\r\nexport const getAnyData=(url,params)=>{\r\n    return axios({\r\n        headers: {\r\n            'authorization': Cookies.get(\"Admin-Token\"), // 必须设置\r\n            'Content-Type': 'application/x-www-form-urlencoded' // 必须设置\r\n        },\r\n        method:'get',\r\n        url:`${base}${url}`,\r\n        params: params\r\n    })\r\n}\r\n"], "mappings": "AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAOC,OAAO,MAAM,WAAW;;AAE/B;AACA;AACA,IAAIC,IAAI,GAAG,0CAA0C;AAClD;AACH;;AAEA;AACA,OAAO,MAAMC,WAAW,GAACA,CAACC,GAAG,EAACC,MAAM,KAAG;EACnC,OAAOL,KAAK,CAAC;IACTM,OAAO,EAAE;MACL,eAAe,EAAEL,OAAO,CAACM,GAAG,CAAC,aAAa,CAAC;MAAE;MAC7C,cAAc,EAAE,mCAAmC,CAAC;IACxD,CAAC;;IACDC,MAAM,EAAC,MAAM;IACbJ,GAAG,EAAE,GAAEF,IAAK,GAAEE,GAAI,EAAC;IACnBK,IAAI,EAAEJ;EACV,CAAC,CAAC;AACN,CAAC;AACD,OAAO,MAAMK,YAAY,GAAEA,CAACN,GAAG,EAACO,MAAM,KAAG;EACrC,OAAOX,KAAK,CAAC;IACTM,OAAO,EAAC;MACJ,eAAe,EAAEL,OAAO,CAACM,GAAG,CAAC,aAAa,CAAC;MAAE;MAC7C,cAAc,EAAE,gCAAgC,CAAC;IACrD,CAAC;;IACDC,MAAM,EAAC,MAAM;IACbJ,GAAG,EAAE,GAAEF,IAAK,GAAEE,GAAI,EAAC;IACnBK,IAAI,EAACE;EACT,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMC,SAAS,GAACA,CAACR,GAAG,EAACC,MAAM,KAAI;EAClC,OAAOL,KAAK,CAAC;IACTM,OAAO,EAAE;MACL,cAAc,EAAE,mCAAmC,CAAC;IACxD,CAAC;;IACDE,MAAM,EAAE,MAAM;IACdJ,GAAG,EAAG,GAAEF,IAAK,GAAEE,GAAI,EAAC;IACpBK,IAAI,EAAEJ;EACV,CAAC,CAAC;AACN,CAAC;AACD,OAAO,MAAMQ,WAAW,GAACA,CAACT,GAAG,EAACC,MAAM,KAAI;EACpC,OAAOL,KAAK,CAAC;IACTM,OAAO,EAAE;MACL,cAAc,EAAE,gCAAgC,CAAC;IACrD,CAAC;;IACDE,MAAM,EAAE,MAAM;IACdJ,GAAG,EAAG,GAAEF,IAAK,GAAEE,GAAI,EAAC;IACpBK,IAAI,EAAEJ;EACV,CAAC,CAAC;AACN,CAAC;AACD,OAAO,MAAMS,UAAU,GAACA,CAACV,GAAG,EAACC,MAAM,KAAI;EACnC,OAAOL,KAAK,CAAC;IACTM,OAAO,EAAE;MACL,cAAc,EAAE,gCAAgC,CAAC;IACrD,CAAC;;IACDE,MAAM,EAAE,KAAK;IACbJ,GAAG,EAAG,GAAEF,IAAK,GAAEE,GAAI,EAAC;IACpBC,MAAM,EAAEA;EACZ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMU,UAAU,GAACA,CAACX,GAAG,EAACC,MAAM,KAAG;EAClC,OAAOL,KAAK,CAAC;IACTM,OAAO,EAAE;MACL,eAAe,EAAEL,OAAO,CAACM,GAAG,CAAC,aAAa,CAAC;MAAE;MAC7C,cAAc,EAAE,mCAAmC,CAAC;IACxD,CAAC;;IACDC,MAAM,EAAC,KAAK;IACZJ,GAAG,EAAE,GAAEF,IAAK,GAAEE,GAAI,EAAC;IACnBC,MAAM,EAAEA;EACZ,CAAC,CAAC;AACN,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}