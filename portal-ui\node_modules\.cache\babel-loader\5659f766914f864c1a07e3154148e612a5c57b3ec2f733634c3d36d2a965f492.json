{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { postAnyData, postLogin, postNotAuth } from \"@/api/login\";\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\nimport backgroundlogin from '@/views/Login/backgroundlogin.vue';\nexport default {\n  name: \"forgetpass\",\n  components: {\n    SlideNotification,\n    backgroundlogin\n  },\n  data() {\n    return {\n      resetForm: {\n        phone: '',\n        code: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      passwordVisible: false,\n      confirmPasswordVisible: false,\n      errors: {\n        phone: '',\n        code: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      codeSent: false,\n      countdown: 60,\n      timer: null,\n      showCodeSent: false,\n      // 验证码发送提示状态\n      isVerifying: false,\n      // 验证中状态\n      showVerifying: false,\n      // 验证中提示状态\n      notificationMinHeight: '50px' // 自定义通知高度\n    };\n  },\n\n  computed: {\n    isFormValid() {\n      return this.resetForm.phone && this.resetForm.code && this.resetForm.newPassword && this.resetForm.confirmPassword && !this.errors.phone && !this.errors.code && !this.errors.newPassword && !this.errors.confirmPassword;\n    }\n  },\n  created() {\n    // 页面创建时检查本地存储中的计时器状态\n    this.$emit('hiden-layout');\n  },\n  methods: {\n    validatePhone() {\n      const phoneRegex = /^1[3-9]\\d{9}$/;\n      if (!this.resetForm.phone) {\n        this.errors.phone = '请输入手机号';\n      } else if (!phoneRegex.test(this.resetForm.phone)) {\n        this.errors.phone = '请输入有效的手机号';\n      } else {\n        this.errors.phone = '';\n      }\n    },\n    validateCode() {\n      if (!this.resetForm.code) {\n        this.errors.code = '请输入验证码';\n      } else if (this.resetForm.code.length !== 4 || !/^\\d+$/.test(this.resetForm.code)) {\n        this.errors.code = '验证码格式不正确';\n      } else {\n        this.errors.code = '';\n      }\n    },\n    validateNewPassword() {\n      const newPassword = this.resetForm.newPassword;\n      this.errors.newPassword = ''; // 重置错误信息\n\n      if (!newPassword) {\n        this.errors.newPassword = '请输入新密码';\n        return;\n      }\n      const hasMinLength = newPassword.length >= 8;\n      if (!hasMinLength) {\n        this.errors.newPassword = '密码长度至少为8位';\n        return;\n      }\n\n      // 下面是三选二的强度项\n      const hasLetter = /[a-zA-Z]/.test(newPassword);\n      const hasNumber = /[0-9]/.test(newPassword);\n      const hasSymbol = /[!@#$%^&*()_+\\-=$${};':\"\\\\|,.<>\\/?]/.test(newPassword);\n      const hasUpperLower = /[A-Z]/.test(newPassword) && /[a-z]/.test(newPassword);\n      let strengthCount = 0;\n      if (hasLetter && hasNumber) strengthCount++; // 字母+数字\n      if (hasSymbol) strengthCount++;\n      if (hasUpperLower) strengthCount++;\n      if (strengthCount >= 2) {\n        this.errors.newPassword = ''; // 密码符合要求\n      } else {\n        const missingRequirements = [];\n        if (!(hasLetter && hasNumber)) missingRequirements.push('包含数字和字母');\n        if (!hasSymbol) missingRequirements.push('包含特殊符号');\n        if (!hasUpperLower) missingRequirements.push('包含大小写字母');\n        this.errors.newPassword = `密码强度不足，请满足以下至少两项要求：${missingRequirements.join('、')}`;\n      }\n\n      // 校验确认密码是否一致\n      if (this.resetForm.confirmPassword) {\n        this.validateConfirmPassword();\n      }\n    },\n    validateConfirmPassword() {\n      if (!this.resetForm.confirmPassword) {\n        this.errors.confirmPassword = '请确认新密码';\n      } else if (this.resetForm.confirmPassword !== this.resetForm.newPassword) {\n        this.errors.confirmPassword = '两次输入的密码不一致';\n      } else {\n        this.errors.confirmPassword = '';\n      }\n    },\n    async getVerificationCode() {\n      this.validatePhone();\n      if (this.errors.phone) return;\n\n      // 调用发送验证码API\n      try {\n        // 显示发送中状态\n        this.codeSent = true;\n        this.isSendingCode = true;\n        // 调用发送验证码接口\n        const response = await postLogin(\"/auth/sendCode\", {\n          phone: this.resetForm.phone\n        });\n\n        // 处理响应\n        if (response.data.code === 200) {\n          this.showCodeSent = true;\n          setTimeout(() => {\n            this.showCodeSent = false;\n          }, 3000);\n          this.startCountdown();\n        } else {\n          this.errors.code = response.data.msg || '验证码发送失败';\n          this.codeSent = false; // 发送失败时可重新发送\n        }\n      } catch (error) {\n        this.errors.code = '验证码发送失败，请稍后重试';\n        this.codeSent = false;\n      } finally {\n        this.isSendingCode = false;\n      }\n    },\n    startCountdown() {\n      // 清除可能存在的旧定时器\n      if (this.timer) {\n        clearInterval(this.timer);\n      }\n      this.countdown = 60;\n      // 使用固定的时间间隔\n      this.timer = setInterval(() => {\n        if (this.countdown <= 1) {\n          clearInterval(this.timer);\n          this.codeSent = false;\n          this.countdown = 60;\n        } else {\n          this.countdown--;\n        }\n      }, 1000);\n    },\n    // 验证验证码是否正确\n    verifyCode() {\n      return new Promise((resolve, reject) => {\n        this.isVerifying = true;\n        this.showVerifying = true;\n        postAnyData(\"/auth/verifyCode\", {\n          phone: this.resetForm.phone,\n          code: this.resetForm.code\n        }).then(res => {\n          this.showVerifying = false;\n          if (res.data && res.data.code === 200) {\n            resolve(true);\n          } else {\n            this.errors.code = res.data.msg || '验证码验证失败';\n            reject(new Error(res.data.msg || '验证码验证失败'));\n          }\n        }).catch(err => {\n          this.showVerifying = false;\n          this.errors.code = '验证码验证失败';\n          reject(err);\n        }).finally(() => {\n          this.isVerifying = false;\n        });\n      });\n    },\n    resetPassword() {\n      // 验证所有字段\n      this.validatePhone();\n      this.validateCode();\n      this.validateNewPassword();\n      this.validateConfirmPassword();\n      if (!this.isFormValid) return;\n\n      // 验证验证码\n      this.verifyCode().then(verified => {\n        if (verified) {\n          // 验证码正确，继续重置密码\n          postLogin(\"/auth/resetPassword\", {\n            phone: this.resetForm.phone,\n            code: this.resetForm.code,\n            password: this.resetForm.newPassword\n          }).then(res => {\n            if (res.data && res.data.code === 200) {\n              this.goToLogin();\n            } else {\n              this.errors.code = res.data.message || '密码重置失败';\n            }\n          }).catch(err => {\n            this.errors.code = res.data.message || '网络异常，密码重置失败';\n          });\n        }\n      }).catch(err => {});\n    },\n    goToLogin() {\n      this.$router.push('/login');\n    },\n    togglePasswordVisibility() {\n      this.passwordVisible = !this.passwordVisible;\n    },\n    toggleConfirmPasswordVisibility() {\n      this.confirmPasswordVisible = !this.confirmPasswordVisible;\n    }\n  },\n  beforeDestroy() {\n    this.$emit('hiden-layout');\n  }\n};", "map": {"version": 3, "names": ["postAnyData", "postLogin", "postNotAuth", "SlideNotification", "backgroundlogin", "name", "components", "data", "resetForm", "phone", "code", "newPassword", "confirmPassword", "passwordVisible", "confirmPasswordVisible", "errors", "codeSent", "countdown", "timer", "showCodeSent", "isVerifying", "showVerifying", "notificationMinHeight", "computed", "isFormValid", "created", "$emit", "methods", "validatePhone", "phoneRegex", "test", "validateCode", "length", "validateNewPassword", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasLetter", "hasNumber", "hasSymbol", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "strengthCount", "missingRequirements", "push", "join", "validateConfirmPassword", "getVerificationCode", "isSendingCode", "response", "setTimeout", "startCountdown", "msg", "error", "clearInterval", "setInterval", "verifyCode", "Promise", "resolve", "reject", "then", "res", "Error", "catch", "err", "finally", "resetPassword", "verified", "password", "goToLogin", "message", "$router", "togglePasswordVisibility", "toggleConfirmPasswordVisibility", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/Login/ForgetPassView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login-page\">\r\n    <SlideNotification\r\n        v-if=\"showCodeSent\"\r\n        message=\"验证码已发送，可能会有延迟，请耐心等待！\"\r\n        type=\"success\"\r\n        :min-height=\"notificationMinHeight\"\r\n        @close=\"showCodeSent = false\"\r\n    />\r\n\r\n\r\n    <div class=\"left-side\">\r\n      <backgroundlogin />\r\n    </div>\r\n\r\n    <div class=\"right-side\">\r\n      <div class=\"login-form-container\">\r\n        <h3>重置统一登录密码</h3>\r\n\r\n        <div class=\"form-container\">\r\n          <div class=\"login-form\">\r\n            <p class=\"form-note\">请输入手机号接收验证码</p>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.phone }\">\r\n              <input\r\n                  type=\"text\"\r\n                  v-model=\"resetForm.phone\"\r\n                  placeholder=\"请输入手机号\"\r\n                  @blur=\"validatePhone\"\r\n              />\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.phone\" class=\"error-message\">{{ errors.phone }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group verification-code\" :class=\"{ 'error': errors.code }\">\r\n              <div class=\"code-input-container\">\r\n                <input\r\n                    type=\"text\"\r\n                    v-model=\"resetForm.code\"\r\n                    placeholder=\"请输入验证码\"\r\n                    @blur=\"validateCode\"\r\n                />\r\n                <button\r\n                    class=\"get-code-btn-inline\"\r\n                    @click=\"getVerificationCode\"\r\n                    :disabled=\"!resetForm.phone || errors.phone || codeSent\"\r\n                >\r\n                  {{ codeSent ? `${countdown}秒后重试` : '获取验证码' }}\r\n                </button>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.code\" class=\"error-message\">{{ errors.code }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.newPassword }\">\r\n              <div class=\"password-input-container\">\r\n                <input\r\n                    :type=\"passwordVisible ? 'text' : 'password'\"\r\n                    v-model=\"resetForm.newPassword\"\r\n                    placeholder=\"请输入新密码\"\r\n                    @blur=\"validateNewPassword\"\r\n                />\r\n                <span class=\"password-toggle\" @click=\"togglePasswordVisibility\">\r\n                  <i :class=\"['eye-icon', passwordVisible ? 'visible' : '']\"></i>\r\n                </span>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.newPassword\" class=\"error-message\">{{ errors.newPassword }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.confirmPassword }\">\r\n              <div class=\"password-input-container\">\r\n                <input\r\n                    :type=\"confirmPasswordVisible ? 'text' : 'password'\"\r\n                    v-model=\"resetForm.confirmPassword\"\r\n                    placeholder=\"请再次输入新密码\"\r\n                    @blur=\"validateConfirmPassword\"\r\n                />\r\n                <span class=\"password-toggle\" @click=\"toggleConfirmPasswordVisibility\">\r\n                  <i :class=\"['eye-icon', confirmPasswordVisible ? 'visible' : '']\"></i>\r\n                </span>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.confirmPassword\" class=\"error-message\">{{ errors.confirmPassword }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <button\r\n                class=\"login-btn\"\r\n                @click=\"resetPassword\"\r\n                :disabled=\"!isFormValid || isVerifying\"\r\n            >\r\n              {{ isVerifying ? '验证中...' : '重置密码' }}\r\n            </button>\r\n            <div class=\"login-link\">\r\n              <a href=\"#\" @click=\"goToLogin\">返回登录</a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {postAnyData, postLogin, postNotAuth} from \"@/api/login\";\r\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\r\nimport backgroundlogin from '@/views/Login/backgroundlogin.vue';\r\n\r\n\r\nexport default {\r\n  name: \"forgetpass\",\r\n  components: {\r\n    SlideNotification,\r\n    backgroundlogin\r\n  },\r\n  data() {\r\n    return {\r\n      resetForm: {\r\n        phone: '',\r\n        code: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      },\r\n      passwordVisible: false,\r\n      confirmPasswordVisible: false,\r\n      errors: {\r\n        phone: '',\r\n        code: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      },\r\n      codeSent: false,\r\n      countdown: 60,\r\n      timer: null,\r\n      showCodeSent: false, // 验证码发送提示状态\r\n      isVerifying: false,  // 验证中状态\r\n      showVerifying: false, // 验证中提示状态\r\n      notificationMinHeight: '50px' // 自定义通知高度\r\n    }\r\n  },\r\n  computed: {\r\n    isFormValid() {\r\n      return this.resetForm.phone &&\r\n          this.resetForm.code &&\r\n          this.resetForm.newPassword &&\r\n          this.resetForm.confirmPassword &&\r\n          !this.errors.phone &&\r\n          !this.errors.code &&\r\n          !this.errors.newPassword &&\r\n          !this.errors.confirmPassword;\r\n    }\r\n  },\r\n  created() {\r\n    // 页面创建时检查本地存储中的计时器状态\r\n    this.$emit('hiden-layout')\r\n  },\r\n\r\n  methods: {\r\n    validatePhone() {\r\n      const phoneRegex = /^1[3-9]\\d{9}$/;\r\n      if (!this.resetForm.phone) {\r\n        this.errors.phone = '请输入手机号';\r\n      } else if (!phoneRegex.test(this.resetForm.phone)) {\r\n        this.errors.phone = '请输入有效的手机号';\r\n      } else {\r\n        this.errors.phone = '';\r\n      }\r\n    },\r\n\r\n    validateCode() {\r\n      if (!this.resetForm.code) {\r\n        this.errors.code = '请输入验证码';\r\n      } else if (this.resetForm.code.length !== 4 || !/^\\d+$/.test(this.resetForm.code)) {\r\n        this.errors.code = '验证码格式不正确';\r\n      } else {\r\n        this.errors.code = '';\r\n      }\r\n    },\r\n\r\n    validateNewPassword() {\r\n      const newPassword = this.resetForm.newPassword;\r\n      this.errors.newPassword = ''; // 重置错误信息\r\n\r\n      if (!newPassword) {\r\n        this.errors.newPassword = '请输入新密码';\r\n        return;\r\n      }\r\n\r\n      const hasMinLength = newPassword.length >= 8;\r\n      if (!hasMinLength) {\r\n        this.errors.newPassword = '密码长度至少为8位';\r\n        return;\r\n      }\r\n\r\n      // 下面是三选二的强度项\r\n      const hasLetter = /[a-zA-Z]/.test(newPassword);\r\n      const hasNumber = /[0-9]/.test(newPassword);\r\n      const hasSymbol = /[!@#$%^&*()_+\\-=$${};':\"\\\\|,.<>\\/?]/.test(newPassword);\r\n      const hasUpperLower = /[A-Z]/.test(newPassword) && /[a-z]/.test(newPassword);\r\n\r\n      let strengthCount = 0;\r\n      if (hasLetter && hasNumber) strengthCount++; // 字母+数字\r\n      if (hasSymbol) strengthCount++;\r\n      if (hasUpperLower) strengthCount++;\r\n\r\n      if (strengthCount >= 2) {\r\n        this.errors.newPassword = ''; // 密码符合要求\r\n      } else {\r\n        const missingRequirements = [];\r\n        if (!(hasLetter && hasNumber)) missingRequirements.push('包含数字和字母');\r\n        if (!hasSymbol) missingRequirements.push('包含特殊符号');\r\n        if (!hasUpperLower) missingRequirements.push('包含大小写字母');\r\n\r\n        this.errors.newPassword = `密码强度不足，请满足以下至少两项要求：${missingRequirements.join('、')}`;\r\n      }\r\n\r\n      // 校验确认密码是否一致\r\n      if (this.resetForm.confirmPassword) {\r\n        this.validateConfirmPassword();\r\n      }\r\n    },\r\n\r\n\r\n    validateConfirmPassword() {\r\n      if (!this.resetForm.confirmPassword) {\r\n        this.errors.confirmPassword = '请确认新密码';\r\n      } else if (this.resetForm.confirmPassword !== this.resetForm.newPassword) {\r\n        this.errors.confirmPassword = '两次输入的密码不一致';\r\n      } else {\r\n        this.errors.confirmPassword = '';\r\n      }\r\n    },\r\n\r\n    async getVerificationCode() {\r\n      this.validatePhone();\r\n      if (this.errors.phone) return;\r\n\r\n      // 调用发送验证码API\r\n      try {\r\n        // 显示发送中状态\r\n        this.codeSent = true;\r\n        this.isSendingCode = true;\r\n        // 调用发送验证码接口\r\n        const response = await postLogin(\"/auth/sendCode\", {\r\n          phone: this.resetForm.phone\r\n        });\r\n\r\n        // 处理响应\r\n        if (response.data.code === 200) {\r\n          this.showCodeSent = true;\r\n          setTimeout(() => {\r\n            this.showCodeSent = false;\r\n          }, 3000);\r\n          this.startCountdown();\r\n        } else {\r\n          this.errors.code = response.data.msg || '验证码发送失败';\r\n          this.codeSent = false; // 发送失败时可重新发送\r\n        }\r\n      } catch (error) {\r\n        this.errors.code = '验证码发送失败，请稍后重试';\r\n        this.codeSent = false;\r\n      } finally {\r\n        this.isSendingCode = false;\r\n      }\r\n    },\r\n\r\n    startCountdown() {\r\n      // 清除可能存在的旧定时器\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n      }\r\n\r\n      this.countdown = 60;\r\n      // 使用固定的时间间隔\r\n      this.timer = setInterval(() => {\r\n        if (this.countdown <= 1) {\r\n          clearInterval(this.timer);\r\n          this.codeSent = false;\r\n          this.countdown = 60;\r\n        } else {\r\n          this.countdown--;\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // 验证验证码是否正确\r\n    verifyCode() {\r\n      return new Promise((resolve, reject) => {\r\n        this.isVerifying = true;\r\n        this.showVerifying = true;\r\n\r\n        postAnyData(\"/auth/verifyCode\", {\r\n          phone: this.resetForm.phone,\r\n          code: this.resetForm.code\r\n        }).then(res => {\r\n          this.showVerifying = false;\r\n          if (res.data && res.data.code === 200) {\r\n            resolve(true);\r\n          } else {\r\n            this.errors.code = res.data.msg || '验证码验证失败';\r\n            reject(new Error(res.data.msg || '验证码验证失败'));\r\n          }\r\n        }).catch(err => {\r\n          this.showVerifying = false;\r\n          this.errors.code = '验证码验证失败';\r\n          reject(err);\r\n        }).finally(() => {\r\n          this.isVerifying = false;\r\n        });\r\n      });\r\n    },\r\n\r\n    resetPassword() {\r\n      // 验证所有字段\r\n      this.validatePhone();\r\n      this.validateCode();\r\n      this.validateNewPassword();\r\n      this.validateConfirmPassword();\r\n\r\n      if (!this.isFormValid) return;\r\n\r\n      // 验证验证码\r\n      this.verifyCode().then(verified => {\r\n        if (verified) {\r\n          // 验证码正确，继续重置密码\r\n          postLogin(\"/auth/resetPassword\", {\r\n            phone: this.resetForm.phone,\r\n            code: this.resetForm.code,\r\n            password: this.resetForm.newPassword,\r\n          }).then(res => {\r\n            if (res.data && res.data.code === 200) {\r\n              this.goToLogin();\r\n            } else {\r\n              this.errors.code= res.data.message || '密码重置失败';\r\n            }\r\n          }).catch(err => {\r\n            this.errors.code= res.data.message || '网络异常，密码重置失败';\r\n          });\r\n        }\r\n      }).catch(err => {\r\n      });\r\n    },\r\n\r\n    goToLogin() {\r\n      this.$router.push('/login');\r\n    },\r\n\r\n    togglePasswordVisibility() {\r\n      this.passwordVisible = !this.passwordVisible;\r\n    },\r\n\r\n    toggleConfirmPasswordVisibility() {\r\n      this.confirmPasswordVisible = !this.confirmPasswordVisible;\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n\r\n    this.$emit('hiden-layout')\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* Full page layout */\r\n.login-page {\r\n  display: flex;\r\n  min-height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Left side styling */\r\n.left-side {\r\n  flex: 1;\r\n  position: relative;\r\n  background: linear-gradient(135deg, #cdb3e5, #5127d5);\r\n  display: flex;\r\n  flex-direction: column;\r\n  /*padding: 2rem;*/\r\n  color: #030303;\r\n}\r\n\r\n.logo-container {\r\n  z-index: 2;\r\n  padding: 1rem 0;\r\n}\r\n\r\n.logo {\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  color: white;\r\n}\r\n\r\n.logo-subtitle {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.headline-container {\r\n  margin-top: 10px;\r\n  text-align: left;\r\n  z-index: 2;\r\n  max-width: 80%;\r\n  margin-left: 100px;\r\n}\r\n\r\n.headline {\r\n  font-size: 25px;\r\n  font-weight: 500;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.subheadline {\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n  opacity: 0.9;\r\n  margin-bottom: 1rem\r\n}\r\n\r\n/* Animated background */\r\n.animated-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  overflow: hidden;\r\n  opacity: 0.4;\r\n}\r\n\r\n.hex-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.hex {\r\n  position: absolute;\r\n  width: 100px;\r\n  height: 110px;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);\r\n}\r\n\r\n.hex1 {\r\n  top: 20%;\r\n  left: 10%;\r\n  transform: scale(1.5);\r\n  animation: float 8s infinite ease-in-out;\r\n}\r\n\r\n.hex2 {\r\n  top: 60%;\r\n  left: 20%;\r\n  transform: scale(1.2);\r\n  animation: float 7s infinite ease-in-out reverse;\r\n}\r\n\r\n.hex3 {\r\n  top: 30%;\r\n  left: 50%;\r\n  transform: scale(1.3);\r\n  animation: float 10s infinite ease-in-out 1s;\r\n}\r\n\r\n.hex4 {\r\n  top: 70%;\r\n  left: 70%;\r\n  transform: scale(1.1);\r\n  animation: float 6s infinite ease-in-out 2s;\r\n}\r\n\r\n.hex5 {\r\n  top: 40%;\r\n  left: 80%;\r\n  transform: scale(1.4);\r\n  animation: float 9s infinite ease-in-out 3s;\r\n}\r\n\r\n.floating-cube {\r\n  position: absolute;\r\n  width: 50px;\r\n  height: 50px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  top: 25%;\r\n  left: 30%;\r\n  animation: float 8s infinite ease-in-out, rotate 15s infinite linear;\r\n}\r\n\r\n.floating-sphere {\r\n  position: absolute;\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  top: 50%;\r\n  left: 40%;\r\n  animation: float 10s infinite ease-in-out reverse;\r\n}\r\n\r\n.floating-diamond {\r\n  position: absolute;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: rotate(45deg);\r\n  top: 65%;\r\n  left: 60%;\r\n  animation: float 7s infinite ease-in-out 2s;\r\n}\r\n\r\n.ripple-effect {\r\n  position: absolute;\r\n  width: 200px;\r\n  height: 200px;\r\n  border-radius: 50%;\r\n  border: 3px solid rgba(255, 255, 255, 0.1);\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  animation: ripple 6s infinite linear;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(20px);\r\n  }\r\n}\r\n\r\n@keyframes rotate {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n@keyframes ripple {\r\n  0% {\r\n    width: 0;\r\n    height: 0;\r\n    opacity: 0.8;\r\n  }\r\n  100% {\r\n    width: 300px;\r\n    height: 300px;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* Right side styling */\r\n.right-side {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #f8f9fa;\r\n  padding: 2rem;\r\n}\r\n\r\n.login-form-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  padding: 2rem;\r\n  width: 100%;\r\n  max-width: 400px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.login-form-container h3 {\r\n  font-size: 24px;\r\n  font-weight: 500;\r\n  margin-bottom: 20px;\r\n  text-align: center;\r\n  color: #333;\r\n}\r\n\r\n/* 表单容器，定义固定高度 */\r\n.form-container {\r\n  min-height: 300px;\r\n  position: relative;\r\n}\r\n\r\n.login-form {\r\n  margin-top: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.form-note {\r\n  color: #999;\r\n  font-size: 12px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.input-group {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n}\r\n\r\n.input-group input {\r\n  width: 100%;\r\n  padding: 12px 15px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.input-group input:focus {\r\n  outline: none;\r\n  border-color: #6a26cd;\r\n}\r\n\r\n/*错误输入框变红*/\r\n.input-group.error input {\r\n  outline: none;\r\n  border: 2px solid #ff4d4f; /* 红色边框 */\r\n}\r\n\r\n/* 错误信息容器，固定高度 */\r\n.error-container {\r\n  min-height: 10px;\r\n  display: block;\r\n}\r\n\r\n/* 验证码输入框与按钮在同一行 */\r\n.verification-code .code-input-container {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.verification-code input {\r\n  flex: 1;\r\n  margin-right: 0; /* 移除原有的右边距 */\r\n}\r\n\r\n.error-container {\r\n  order: 3; /* 将错误容器放在最下方 */\r\n  width: 100%;\r\n  margin-top: 4px;\r\n}\r\n\r\n.get-code-btn-inline {\r\n  flex-shrink: 0;\r\n  width: 130px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  background-color: #2196f3;\r\n  color: #ffffff;\r\n}\r\n\r\n/* 密码输入容器，确保图标垂直居中 */\r\n.password-input-container {\r\n  position: relative;\r\n  margin-top: 30px;\r\n  /*margin-bottom: 20px;*/\r\n}\r\n\r\n.password-toggle {\r\n  position: absolute;\r\n\r\n  right: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\ninput[type=\"password\"]::-ms-reveal,\r\ninput[type=\"password\"]::-webkit-credentials-auto-fill-button,\r\ninput[type=\"password\"]::-webkit-clear-button {\r\n  display: none !important;\r\n  pointer-events: none;\r\n}\r\n\r\n.eye-icon {\r\n  display: inline-block;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle></svg>');\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  opacity: 0.5;\r\n}\r\n\r\n.eye-icon.visible {\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle><line x1=\"1\" y1=\"1\" x2=\"23\" y2=\"23\"></line></svg>');\r\n}\r\n\r\n.login-btn {\r\n  width: 100%;\r\n  padding: 12px 0;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.login-btn:hover:not(:disabled) {\r\n  background-color: #043ef1;\r\n}\r\n\r\n.login-btn:disabled {\r\n  background-color: #2196f3;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/*错误提示词提示*/\r\n.error-message {\r\n  color: #f44336;\r\n  font-size: 12px;\r\n  margin-top: 0px;\r\n  max-height: 10px;\r\n}\r\n\r\n/* Login link */\r\n.login-link {\r\n  margin-top: 15px;\r\n  text-align: center;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-link a {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n.logo-area {\r\n  flex: 0 0 auto;\r\n  margin-right: 10px;\r\n}\r\n\r\n.logo-link {\r\n  display: flex;\r\n  align-items: center;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n}\r\n\r\n.logo-area img {\r\n  height: 30px;\r\n  max-width: 100%;\r\n}\r\n\r\n.logo-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-left: 10px;\r\n}\r\n@media screen and (max-width: 768px) {\r\n  .left-side {\r\n    display: none; /* 在手机端隐藏左侧背景 */\r\n  }\r\n\r\n  .right-side {\r\n    flex: 1 0 100%; /* 让右侧占据全部宽度 */\r\n    padding: 1rem; /* 减少内边距以适应小屏幕 */\r\n  }\r\n\r\n  .login-form-container {\r\n    max-width: 100%; /* 让登录表单占据全部可用宽度 */\r\n    box-shadow: none; /* 移除阴影以节省空间 */\r\n    padding: 1.5rem; /* 调整内边距 */\r\n  }\r\n\r\n}\r\n</style>\r\n"], "mappings": ";AA4GA,SAAAA,WAAA,EAAAC,SAAA,EAAAC,WAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,eAAA;AAGA;EACAC,IAAA;EACAC,UAAA;IACAH,iBAAA;IACAC;EACA;EACAG,KAAA;IACA;MACAC,SAAA;QACAC,KAAA;QACAC,IAAA;QACAC,WAAA;QACAC,eAAA;MACA;MACAC,eAAA;MACAC,sBAAA;MACAC,MAAA;QACAN,KAAA;QACAC,IAAA;QACAC,WAAA;QACAC,eAAA;MACA;MACAI,QAAA;MACAC,SAAA;MACAC,KAAA;MACAC,YAAA;MAAA;MACAC,WAAA;MAAA;MACAC,aAAA;MAAA;MACAC,qBAAA;IACA;EACA;;EACAC,QAAA;IACAC,YAAA;MACA,YAAAhB,SAAA,CAAAC,KAAA,IACA,KAAAD,SAAA,CAAAE,IAAA,IACA,KAAAF,SAAA,CAAAG,WAAA,IACA,KAAAH,SAAA,CAAAI,eAAA,IACA,MAAAG,MAAA,CAAAN,KAAA,IACA,MAAAM,MAAA,CAAAL,IAAA,IACA,MAAAK,MAAA,CAAAJ,WAAA,IACA,MAAAI,MAAA,CAAAH,eAAA;IACA;EACA;EACAa,QAAA;IACA;IACA,KAAAC,KAAA;EACA;EAEAC,OAAA;IACAC,cAAA;MACA,MAAAC,UAAA;MACA,UAAArB,SAAA,CAAAC,KAAA;QACA,KAAAM,MAAA,CAAAN,KAAA;MACA,YAAAoB,UAAA,CAAAC,IAAA,MAAAtB,SAAA,CAAAC,KAAA;QACA,KAAAM,MAAA,CAAAN,KAAA;MACA;QACA,KAAAM,MAAA,CAAAN,KAAA;MACA;IACA;IAEAsB,aAAA;MACA,UAAAvB,SAAA,CAAAE,IAAA;QACA,KAAAK,MAAA,CAAAL,IAAA;MACA,gBAAAF,SAAA,CAAAE,IAAA,CAAAsB,MAAA,mBAAAF,IAAA,MAAAtB,SAAA,CAAAE,IAAA;QACA,KAAAK,MAAA,CAAAL,IAAA;MACA;QACA,KAAAK,MAAA,CAAAL,IAAA;MACA;IACA;IAEAuB,oBAAA;MACA,MAAAtB,WAAA,QAAAH,SAAA,CAAAG,WAAA;MACA,KAAAI,MAAA,CAAAJ,WAAA;;MAEA,KAAAA,WAAA;QACA,KAAAI,MAAA,CAAAJ,WAAA;QACA;MACA;MAEA,MAAAuB,YAAA,GAAAvB,WAAA,CAAAqB,MAAA;MACA,KAAAE,YAAA;QACA,KAAAnB,MAAA,CAAAJ,WAAA;QACA;MACA;;MAEA;MACA,MAAAwB,SAAA,cAAAL,IAAA,CAAAnB,WAAA;MACA,MAAAyB,SAAA,WAAAN,IAAA,CAAAnB,WAAA;MACA,MAAA0B,SAAA,yCAAAP,IAAA,CAAAnB,WAAA;MACA,MAAA2B,aAAA,WAAAR,IAAA,CAAAnB,WAAA,aAAAmB,IAAA,CAAAnB,WAAA;MAEA,IAAA4B,aAAA;MACA,IAAAJ,SAAA,IAAAC,SAAA,EAAAG,aAAA;MACA,IAAAF,SAAA,EAAAE,aAAA;MACA,IAAAD,aAAA,EAAAC,aAAA;MAEA,IAAAA,aAAA;QACA,KAAAxB,MAAA,CAAAJ,WAAA;MACA;QACA,MAAA6B,mBAAA;QACA,MAAAL,SAAA,IAAAC,SAAA,GAAAI,mBAAA,CAAAC,IAAA;QACA,KAAAJ,SAAA,EAAAG,mBAAA,CAAAC,IAAA;QACA,KAAAH,aAAA,EAAAE,mBAAA,CAAAC,IAAA;QAEA,KAAA1B,MAAA,CAAAJ,WAAA,yBAAA6B,mBAAA,CAAAE,IAAA;MACA;;MAEA;MACA,SAAAlC,SAAA,CAAAI,eAAA;QACA,KAAA+B,uBAAA;MACA;IACA;IAGAA,wBAAA;MACA,UAAAnC,SAAA,CAAAI,eAAA;QACA,KAAAG,MAAA,CAAAH,eAAA;MACA,gBAAAJ,SAAA,CAAAI,eAAA,UAAAJ,SAAA,CAAAG,WAAA;QACA,KAAAI,MAAA,CAAAH,eAAA;MACA;QACA,KAAAG,MAAA,CAAAH,eAAA;MACA;IACA;IAEA,MAAAgC,oBAAA;MACA,KAAAhB,aAAA;MACA,SAAAb,MAAA,CAAAN,KAAA;;MAEA;MACA;QACA;QACA,KAAAO,QAAA;QACA,KAAA6B,aAAA;QACA;QACA,MAAAC,QAAA,SAAA7C,SAAA;UACAQ,KAAA,OAAAD,SAAA,CAAAC;QACA;;QAEA;QACA,IAAAqC,QAAA,CAAAvC,IAAA,CAAAG,IAAA;UACA,KAAAS,YAAA;UACA4B,UAAA;YACA,KAAA5B,YAAA;UACA;UACA,KAAA6B,cAAA;QACA;UACA,KAAAjC,MAAA,CAAAL,IAAA,GAAAoC,QAAA,CAAAvC,IAAA,CAAA0C,GAAA;UACA,KAAAjC,QAAA;QACA;MACA,SAAAkC,KAAA;QACA,KAAAnC,MAAA,CAAAL,IAAA;QACA,KAAAM,QAAA;MACA;QACA,KAAA6B,aAAA;MACA;IACA;IAEAG,eAAA;MACA;MACA,SAAA9B,KAAA;QACAiC,aAAA,MAAAjC,KAAA;MACA;MAEA,KAAAD,SAAA;MACA;MACA,KAAAC,KAAA,GAAAkC,WAAA;QACA,SAAAnC,SAAA;UACAkC,aAAA,MAAAjC,KAAA;UACA,KAAAF,QAAA;UACA,KAAAC,SAAA;QACA;UACA,KAAAA,SAAA;QACA;MACA;IACA;IAEA;IACAoC,WAAA;MACA,WAAAC,OAAA,EAAAC,OAAA,EAAAC,MAAA;QACA,KAAApC,WAAA;QACA,KAAAC,aAAA;QAEArB,WAAA;UACAS,KAAA,OAAAD,SAAA,CAAAC,KAAA;UACAC,IAAA,OAAAF,SAAA,CAAAE;QACA,GAAA+C,IAAA,CAAAC,GAAA;UACA,KAAArC,aAAA;UACA,IAAAqC,GAAA,CAAAnD,IAAA,IAAAmD,GAAA,CAAAnD,IAAA,CAAAG,IAAA;YACA6C,OAAA;UACA;YACA,KAAAxC,MAAA,CAAAL,IAAA,GAAAgD,GAAA,CAAAnD,IAAA,CAAA0C,GAAA;YACAO,MAAA,KAAAG,KAAA,CAAAD,GAAA,CAAAnD,IAAA,CAAA0C,GAAA;UACA;QACA,GAAAW,KAAA,CAAAC,GAAA;UACA,KAAAxC,aAAA;UACA,KAAAN,MAAA,CAAAL,IAAA;UACA8C,MAAA,CAAAK,GAAA;QACA,GAAAC,OAAA;UACA,KAAA1C,WAAA;QACA;MACA;IACA;IAEA2C,cAAA;MACA;MACA,KAAAnC,aAAA;MACA,KAAAG,YAAA;MACA,KAAAE,mBAAA;MACA,KAAAU,uBAAA;MAEA,UAAAnB,WAAA;;MAEA;MACA,KAAA6B,UAAA,GAAAI,IAAA,CAAAO,QAAA;QACA,IAAAA,QAAA;UACA;UACA/D,SAAA;YACAQ,KAAA,OAAAD,SAAA,CAAAC,KAAA;YACAC,IAAA,OAAAF,SAAA,CAAAE,IAAA;YACAuD,QAAA,OAAAzD,SAAA,CAAAG;UACA,GAAA8C,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAnD,IAAA,IAAAmD,GAAA,CAAAnD,IAAA,CAAAG,IAAA;cACA,KAAAwD,SAAA;YACA;cACA,KAAAnD,MAAA,CAAAL,IAAA,GAAAgD,GAAA,CAAAnD,IAAA,CAAA4D,OAAA;YACA;UACA,GAAAP,KAAA,CAAAC,GAAA;YACA,KAAA9C,MAAA,CAAAL,IAAA,GAAAgD,GAAA,CAAAnD,IAAA,CAAA4D,OAAA;UACA;QACA;MACA,GAAAP,KAAA,CAAAC,GAAA,KACA;IACA;IAEAK,UAAA;MACA,KAAAE,OAAA,CAAA3B,IAAA;IACA;IAEA4B,yBAAA;MACA,KAAAxD,eAAA,SAAAA,eAAA;IACA;IAEAyD,gCAAA;MACA,KAAAxD,sBAAA,SAAAA,sBAAA;IACA;EACA;EACAyD,cAAA;IAEA,KAAA7C,KAAA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}