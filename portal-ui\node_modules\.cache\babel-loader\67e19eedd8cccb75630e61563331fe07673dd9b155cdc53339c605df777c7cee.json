{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", [!_vm.isMobile ? _c(\"div\", {\n    staticClass: \"desktop-layout\"\n  }, [_c(\"div\", {\n    staticClass: \"banner-section\"\n  }, [_c(\"div\", {\n    staticClass: \"banner-container\"\n  }, [_c(\"div\", {\n    staticClass: \"big-box\"\n  }, [_c(\"div\", {\n    staticClass: \"img-box\"\n  }, [_c(\"div\", {\n    staticClass: \"show-box\",\n    style: {\n      transform: \"translateX(\" + _vm.translate + \")\",\n      transition: _vm.tsion ? \"all 0.5s\" : \"none\"\n    }\n  }, _vm._l(_vm.bannerImages, function (item, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"slide-item\"\n    }, [_c(\"img\", {\n      attrs: {\n        src: item.img,\n        alt: \"\"\n      }\n    }), _c(\"div\", {\n      staticClass: \"banner-content\",\n      class: \"pos-\" + item.content.position\n    }, [_c(\"h2\", {\n      staticClass: \"banner-title\",\n      domProps: {\n        innerHTML: _vm._s(item.content.title)\n      }\n    }), _c(\"p\", {\n      staticClass: \"banner-text\"\n    }, [_vm._v(_vm._s(item.content.text))]), _c(\"div\", {\n      staticClass: \"banner-actions\"\n    }, [!_vm.isLogin && item.content.secondaryLink ? _c(\"a\", {\n      staticClass: \"banner-button secondary-btn\",\n      attrs: {\n        href: \"#\"\n      },\n      on: {\n        click: function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.secondaryLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.secondaryBtnText) + \" \")]) : _vm._e(), !_vm.isLogin && item.content.primaryLink ? _c(\"a\", {\n      staticClass: \"banner-button primary-btn\",\n      attrs: {\n        href: \"#\"\n      },\n      on: {\n        click: function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.primaryLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.primaryBtnText) + \" \")]) : _vm._e(), item.content.thirdLink ? _c(\"a\", {\n      staticClass: \"banner-button secondary-btn\",\n      attrs: {\n        href: \"#\"\n      },\n      on: {\n        click: function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.thirdLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.thirdBtnText) + \" \")]) : _vm._e()])])]);\n  }), 0)]), _c(\"div\", {\n    staticClass: \"arrowhead-box\"\n  }, [_c(\"span\", {\n    staticClass: \"nav-arrow left\",\n    on: {\n      click: _vm.last\n    }\n  }, [_c(\"img\", {\n    staticClass: \"arrow-icon rotated\",\n    attrs: {\n      src: require(\"../../assets/images/index/right-arrow.png\"),\n      alt: \"\"\n    }\n  })]), _c(\"span\", {\n    staticClass: \"nav-arrow right\",\n    on: {\n      click: _vm.next\n    }\n  }, [_c(\"img\", {\n    staticClass: \"arrow-icon\",\n    attrs: {\n      src: require(\"../../assets/images/index/right-arrow.png\"),\n      alt: \"\"\n    }\n  })])]), _c(\"div\", {\n    ref: \"swiperPagination\",\n    staticClass: \"swiper-pagination\"\n  }, _vm._l(_vm.bannerImages, function (item, index) {\n    return _c(\"span\", {\n      key: index,\n      class: {\n        active: _vm.translateX === index\n      }\n    });\n  }), 0)])])]), _c(\"section\", {\n    staticClass: \"section gpu-section\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"gpu-card-grid\"\n  }, _vm._l(_vm.gpus, function (gpu, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"gpu-card\",\n      class: {\n        recommended: gpu.recommended\n      },\n      on: {\n        click: function ($event) {\n          return _vm.navigateTo(\"/product\");\n        }\n      }\n    }, [_c(\"div\", {\n      staticClass: \"gpu-card-header\"\n    }, [_c(\"h3\", {\n      staticClass: \"gpu-name\"\n    }, [_vm._v(_vm._s(gpu.name))]), gpu.recommended ? _c(\"span\", {\n      staticClass: \"recommendation-tag\"\n    }, [_vm._v(\"推荐\")]) : _vm._e(), gpu.isNew ? _c(\"span\", {\n      staticClass: \"new-tag\"\n    }, [_vm._v(\"NEW\")]) : _vm._e()]), _c(\"div\", {\n      staticClass: \"gpu-specs-pricing\"\n    }, [_c(\"div\", {\n      staticClass: \"specs-section\"\n    }, [_c(\"div\", {\n      staticClass: \"spec-item\"\n    }, [_c(\"span\", {\n      staticClass: \"spec-label\"\n    }, [_vm._v(\"单精度:\")]), _c(\"span\", {\n      staticClass: \"spec-value\"\n    }, [_vm._v(_vm._s(gpu.singlePrecision) + \" TFLOPS\")])]), _c(\"div\", {\n      staticClass: \"spec-item\"\n    }, [_c(\"span\", {\n      staticClass: \"spec-label\"\n    }, [_vm._v(\"半精度:\")]), _c(\"span\", {\n      staticClass: \"spec-value\"\n    }, [_vm._v(_vm._s(gpu.halfPrecision) + \" Tensor TFL\")])])]), _c(\"div\", {\n      staticClass: \"price-section\"\n    }, [_c(\"div\", {\n      staticClass: \"gpu-pricing\"\n    }, [gpu.originalPrice ? _c(\"span\", {\n      staticClass: \"original-price\"\n    }, [_vm._v(\"¥\" + _vm._s(gpu.originalPrice) + \"/时\")]) : _vm._e(), _c(\"span\", {\n      staticClass: \"current-price\"\n    }, [_vm._v(\"¥\"), _c(\"span\", {\n      staticClass: \"price-value\"\n    }, [_vm._v(_vm._s(gpu.price))]), _vm._v(\"/时\")])])])])]);\n  }), 0)])]), _c(\"GpuComparison\"), _c(\"section\", {\n    staticClass: \"section services-section\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_vm._m(1), _c(\"div\", {\n    staticClass: \"services-grid\"\n  }, _vm._l(_vm.serviceList, function (service, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"service-item\"\n    }, [_c(\"div\", {\n      staticClass: \"service-card\"\n    }, [_c(\"i\", {\n      staticClass: \"service-icon\",\n      class: service.icon\n    }), _c(\"h3\", {\n      staticClass: \"service-title\"\n    }, [_vm._v(_vm._s(service.title))]), _c(\"div\", {\n      staticClass: \"service-text\"\n    }, [_c(\"p\", [_vm._v(_vm._s(service.desc))])])])]);\n  }), 0)])]), _c(\"section\", {\n    staticClass: \"appsec-section\"\n  }, [_vm._m(2), _c(\"div\", {\n    staticClass: \"appsec-container\"\n  }, [_c(\"div\", {\n    staticClass: \"appsec-grid\"\n  }, [_c(\"div\", {\n    staticClass: \"appsec-item appsec-wide\"\n  }, [_c(\"div\", {\n    staticClass: \"appsec-card\",\n    on: {\n      mouseover: function ($event) {\n        _vm.firstRowWide.hover = true;\n      },\n      mouseleave: function ($event) {\n        _vm.firstRowWide.hover = false;\n      }\n    }\n  }, [_c(\"div\", {\n    staticClass: \"appsec-image\",\n    class: {\n      \"appsec-hover\": _vm.firstRowWide.hover\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: _vm.firstRowWide.image,\n      alt: _vm.firstRowWide.title\n    }\n  })]), _c(\"div\", {\n    staticClass: \"appsec-cardtitle\",\n    class: {\n      \"appsec-hover\": _vm.firstRowWide.hover\n    }\n  }, [_vm._v(_vm._s(_vm.firstRowWide.title))])])]), _vm._l(_vm.firstRowTallApps, function (app, index) {\n    return _c(\"div\", {\n      key: \"tall-\" + index,\n      staticClass: \"appsec-item appsec-tall\"\n    }, [_c(\"div\", {\n      staticClass: \"appsec-card\",\n      on: {\n        mouseover: function ($event) {\n          app.hover = true;\n        },\n        mouseleave: function ($event) {\n          app.hover = false;\n        }\n      }\n    }, [_c(\"div\", {\n      staticClass: \"appsec-image\",\n      class: {\n        \"appsec-hover\": app.hover\n      }\n    }, [_c(\"img\", {\n      attrs: {\n        src: app.image,\n        alt: app.title\n      }\n    })]), _c(\"div\", {\n      staticClass: \"appsec-cardtitle\",\n      class: {\n        \"appsec-hover\": app.hover\n      }\n    }, [_vm._v(_vm._s(app.title))])])]);\n  }), _vm._l(_vm.secondRowApps, function (app, index) {\n    return _c(\"div\", {\n      key: \"small-\" + index,\n      staticClass: \"appsec-item appsec-small\"\n    }, [_c(\"div\", {\n      staticClass: \"appsec-card\",\n      on: {\n        mouseover: function ($event) {\n          app.hover = true;\n        },\n        mouseleave: function ($event) {\n          app.hover = false;\n        }\n      }\n    }, [_c(\"div\", {\n      staticClass: \"appsec-image\",\n      class: {\n        \"appsec-hover\": app.hover\n      }\n    }, [_c(\"img\", {\n      attrs: {\n        src: app.image,\n        alt: app.title\n      }\n    })]), _c(\"div\", {\n      staticClass: \"appsec-cardtitle\",\n      class: {\n        \"appsec-hover\": app.hover\n      }\n    }, [_vm._v(_vm._s(app.title))])])]);\n  }), _vm._l(_vm.thirdRowSmallApps, function (app, index) {\n    return _c(\"div\", {\n      key: \"third-small-\" + index,\n      staticClass: \"appsec-item appsec-small\"\n    }, [_c(\"div\", {\n      staticClass: \"appsec-card\",\n      on: {\n        mouseover: function ($event) {\n          app.hover = true;\n        },\n        mouseleave: function ($event) {\n          app.hover = false;\n        }\n      }\n    }, [_c(\"div\", {\n      staticClass: \"appsec-image\",\n      class: {\n        \"appsec-hover\": app.hover\n      }\n    }, [_c(\"img\", {\n      attrs: {\n        src: app.image,\n        alt: app.title\n      }\n    })]), _c(\"div\", {\n      staticClass: \"appsec-cardtitle\",\n      class: {\n        \"appsec-hover\": app.hover\n      }\n    }, [_vm._v(_vm._s(app.title))])])]);\n  }), _c(\"div\", {\n    staticClass: \"appsec-item appsec-wide\"\n  }, [_c(\"div\", {\n    staticClass: \"appsec-card\",\n    on: {\n      mouseover: function ($event) {\n        _vm.thirdRowWide.hover = true;\n      },\n      mouseleave: function ($event) {\n        _vm.thirdRowWide.hover = false;\n      }\n    }\n  }, [_c(\"div\", {\n    staticClass: \"appsec-image\",\n    class: {\n      \"appsec-hover\": _vm.thirdRowWide.hover\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: _vm.thirdRowWide.image,\n      alt: _vm.thirdRowWide.title\n    }\n  })]), _c(\"div\", {\n    staticClass: \"appsec-cardtitle\",\n    class: {\n      \"appsec-hover\": _vm.thirdRowWide.hover\n    }\n  }, [_vm._v(_vm._s(_vm.thirdRowWide.title))])])])], 2), _c(\"chat-ai\")], 1)]), _c(\"div\", {\n    staticClass: \"recommendation-tag1\"\n  }, [_c(\"div\", {\n    staticClass: \"card1\"\n  }, [_c(\"h1\", {\n    staticClass: \"banner-text1\"\n  }, [_vm._v(\"为AI+千行百业，提供高性能算力服务\")]), _c(\"button\", {\n    staticClass: \"consult-button1\",\n    on: {\n      click: _vm.openContactModal\n    }\n  }, [_vm._v(\"立即咨询\")])])]), _c(\"transition\", {\n    attrs: {\n      name: \"fade\"\n    }\n  }, [_vm.showContactModal ? _c(\"div\", {\n    staticClass: \"contact-modal-overlay\",\n    on: {\n      click: function ($event) {\n        if ($event.target !== $event.currentTarget) return null;\n        return _vm.closeContactModal.apply(null, arguments);\n      }\n    }\n  }, [_c(\"div\", {\n    staticClass: \"contact-modal\"\n  }, [_c(\"button\", {\n    staticClass: \"close-modal\",\n    on: {\n      click: _vm.closeContactModal\n    }\n  }, [_vm._v(\" × \")]), _c(\"div\", {\n    staticClass: \"contact-content\"\n  }, [_c(\"div\", {\n    staticClass: \"contact-item\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-user\"\n  }), _c(\"span\", [_vm._v(_vm._s(_vm.contactInfo.name))])]), _c(\"div\", {\n    staticClass: \"contact-item\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-phone\"\n  }), _c(\"span\", [_vm._v(_vm._s(_vm.contactInfo.phone))])])]), _c(\"div\", {\n    staticClass: \"contact-note\"\n  }, [_c(\"p\", [_vm._v(\"欢迎随时来电咨询\")])])])]) : _vm._e()])], 1) : _c(\"div\", {\n    staticClass: \"mobile-layout\"\n  }, [_c(\"div\", {\n    staticClass: \"mobile-banner\",\n    on: {\n      touchstart: _vm.handleTouchStart,\n      touchmove: _vm.handleTouchMove,\n      touchend: _vm.handleTouchEnd\n    }\n  }, [_c(\"div\", {\n    staticClass: \"mobile-banner-slider\",\n    style: {\n      transform: `translateX(${-_vm.mobileCurrentSlide * 100}%)`\n    }\n  }, _vm._l(_vm.bannerImages, function (item, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"mobile-slide\"\n    }, [_c(\"div\", {\n      staticClass: \"mobile-slide-inner\"\n    }, [_c(\"img\", {\n      staticClass: \"mobile-slide-img\",\n      attrs: {\n        src: item.img,\n        alt: \"\"\n      }\n    }), _c(\"div\", {\n      staticClass: \"mobile-banner-content\",\n      class: \"pos-\" + item.content.position\n    }, [_c(\"h2\", {\n      staticClass: \"mobile-banner-title\",\n      domProps: {\n        innerHTML: _vm._s(item.content.title)\n      }\n    }), _c(\"p\", {\n      staticClass: \"mobile-banner-text\"\n    }, [_vm._v(_vm._s(item.content.text))]), _c(\"div\", {\n      staticClass: \"mobile-banner-actions\"\n    }, [!_vm.isLogin && item.content.secondaryLink ? _c(\"a\", {\n      staticClass: \"mobile-banner-button secondary-btn\",\n      attrs: {\n        href: \"#\"\n      },\n      on: {\n        click: function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.secondaryLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.secondaryBtnText) + \" \")]) : _vm._e(), !_vm.isLogin && item.content.primaryLink ? _c(\"a\", {\n      staticClass: \"mobile-banner-button primary-btn\",\n      attrs: {\n        href: \"#\"\n      },\n      on: {\n        click: function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.primaryLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.primaryBtnText) + \" \")]) : _vm._e(), item.content.thirdLink ? _c(\"a\", {\n      staticClass: \"banner-button secondary-btn\",\n      attrs: {\n        href: \"#\"\n      },\n      on: {\n        click: function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.thirdLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.thirdBtnText) + \" \")]) : _vm._e()])])])]);\n  }), 0), _c(\"div\", {\n    staticClass: \"mobile-banner-pagination\"\n  }, _vm._l(_vm.bannerImages, function (item, index) {\n    return _c(\"span\", {\n      key: index,\n      class: {\n        active: _vm.mobileCurrentSlide === index\n      },\n      on: {\n        click: function ($event) {\n          return _vm.goToSlide(index);\n        }\n      }\n    });\n  }), 0)]), _c(\"section\", {\n    staticClass: \"mobile-section mobile-gpu-section\"\n  }, [_vm._m(3), _c(\"div\", {\n    staticClass: \"mobile-gpu-list\"\n  }, _vm._l(_vm.gpus, function (gpu, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"mobile-gpu-card\",\n      class: {\n        recommended: gpu.recommended\n      },\n      on: {\n        click: function ($event) {\n          return _vm.navigateTo(\"/product\");\n        }\n      }\n    }, [_c(\"div\", {\n      staticClass: \"mobile-gpu-header\"\n    }, [_c(\"h3\", {\n      staticClass: \"mobile-gpu-name\"\n    }, [_vm._v(_vm._s(gpu.name))]), _c(\"div\", {\n      staticClass: \"mobile-gpu-tags\"\n    }, [gpu.recommended ? _c(\"span\", {\n      staticClass: \"mobile-recommend-tag\"\n    }, [_vm._v(\"推荐\")]) : _vm._e(), gpu.isNew ? _c(\"span\", {\n      staticClass: \"mobile-new-tag\"\n    }, [_vm._v(\"NEW\")]) : _vm._e()])]), _c(\"div\", {\n      staticClass: \"mobile-gpu-specs\"\n    }, [_c(\"div\", {\n      staticClass: \"mobile-spec-item\"\n    }, [_c(\"span\", {\n      staticClass: \"mobile-spec-label\"\n    }, [_vm._v(\"单精度:\")]), _c(\"span\", {\n      staticClass: \"mobile-spec-value\"\n    }, [_vm._v(_vm._s(gpu.singlePrecision) + \" TFLOPS\")])]), _c(\"div\", {\n      staticClass: \"mobile-spec-item\"\n    }, [_c(\"span\", {\n      staticClass: \"mobile-spec-label\"\n    }, [_vm._v(\"半精度:\")]), _c(\"span\", {\n      staticClass: \"mobile-spec-value\"\n    }, [_vm._v(_vm._s(gpu.halfPrecision) + \" Tensor TFL\")])])]), _c(\"div\", {\n      staticClass: \"mobile-gpu-price\"\n    }, [gpu.originalPrice ? _c(\"span\", {\n      staticClass: \"mobile-original-price\"\n    }, [_vm._v(\"¥\" + _vm._s(gpu.originalPrice) + \"/时\")]) : _vm._e(), _c(\"span\", {\n      staticClass: \"mobile-current-price\"\n    }, [_vm._v(\"¥\" + _vm._s(gpu.price) + \"/时\")])])]);\n  }), 0)]), _c(\"section\", {\n    staticClass: \"mobile-section mobile-comparison-section\"\n  }, [_vm._m(4), _c(\"div\", {\n    staticClass: \"mobile-comparison-container\"\n  }, [_c(\"div\", {\n    staticClass: \"mobile-comparison-scroll\"\n  }, [_c(\"table\", {\n    staticClass: \"mobile-comparison-table\"\n  }, [_c(\"thead\", [_c(\"tr\", [_c(\"th\", [_vm._v(\"GPU型号\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"th\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.name))]);\n  })], 2)]), _c(\"tbody\", [_c(\"tr\", [_c(\"td\", [_vm._v(\"架构\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.architecture))]);\n  })], 2), _c(\"tr\", [_c(\"td\", [_vm._v(\"FP16性能\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.fp16Performance))]);\n  })], 2), _c(\"tr\", [_c(\"td\", [_vm._v(\"FP32性能\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.fp32Performance))]);\n  })], 2), _c(\"tr\", [_c(\"td\", [_vm._v(\"显存\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.memory))]);\n  })], 2), _c(\"tr\", [_c(\"td\", [_vm._v(\"显存类型\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.memoryType))]);\n  })], 2), _c(\"tr\", [_c(\"td\", [_vm._v(\"带宽\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.bandwidth))]);\n  })], 2)])])])])]), _c(\"section\", {\n    staticClass: \"mobile-section mobile-services-section\"\n  }, [_vm._m(5), _c(\"div\", {\n    staticClass: \"mobile-services-list\"\n  }, _vm._l(_vm.serviceList, function (service, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"mobile-service-card\"\n    }, [_c(\"div\", {\n      staticClass: \"mobile-service-icon\"\n    }, [_c(\"i\", {\n      class: service.icon\n    })]), _c(\"h3\", {\n      staticClass: \"mobile-service-title\"\n    }, [_vm._v(_vm._s(service.title))]), _c(\"p\", {\n      staticClass: \"mobile-service-desc\"\n    }, [_vm._v(_vm._s(service.desc))])]);\n  }), 0)]), _c(\"section\", {\n    staticClass: \"mobile-section mobile-applications-section\"\n  }, [_vm._m(6), _c(\"div\", {\n    staticClass: \"mobile-applications-grid\"\n  }, _vm._l(_vm.mobileApplications, function (app, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"mobile-app-item\"\n    }, [_c(\"div\", {\n      staticClass: \"mobile-app-image\"\n    }, [_c(\"img\", {\n      attrs: {\n        src: app.image,\n        alt: app.title\n      }\n    })]), _c(\"h3\", {\n      staticClass: \"mobile-app-title\"\n    }, [_vm._v(_vm._s(app.title))])]);\n  }), 0)]), _c(\"div\", {\n    staticClass: \"mobile-consult-section\"\n  }, [_c(\"h3\", {\n    staticClass: \"mobile-consult-title\"\n  }, [_vm._v(\"为AI+千行百业，提供高性能算力服务\")]), _c(\"button\", {\n    staticClass: \"mobile-consult-button\",\n    on: {\n      click: _vm.openContactModal\n    }\n  }, [_vm._v(\"立即咨询\")])]), _c(\"transition\", {\n    attrs: {\n      name: \"mobile-fade\"\n    }\n  }, [_vm.showContactModal ? _c(\"div\", {\n    staticClass: \"mobile-contact-overlay\",\n    on: {\n      click: function ($event) {\n        if ($event.target !== $event.currentTarget) return null;\n        return _vm.closeContactModal.apply(null, arguments);\n      }\n    }\n  }, [_c(\"div\", {\n    staticClass: \"mobile-contact-modal\"\n  }, [_c(\"button\", {\n    staticClass: \"mobile-close-modal\",\n    on: {\n      click: _vm.closeContactModal\n    }\n  }, [_vm._v(\" × \")]), _c(\"div\", {\n    staticClass: \"mobile-contact-content\"\n  }, [_c(\"div\", {\n    staticClass: \"mobile-contact-item\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-user\"\n  }), _c(\"span\", [_vm._v(_vm._s(_vm.contactInfo.name))])]), _c(\"div\", {\n    staticClass: \"mobile-contact-item\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-phone\"\n  }), _c(\"span\", [_vm._v(_vm._s(_vm.contactInfo.phone))])])]), _c(\"div\", {\n    staticClass: \"mobile-contact-note\"\n  }, [_c(\"p\", [_vm._v(\"欢迎随时来电咨询\")])])])]) : _vm._e()])], 1), !_vm.isMobile ? _c(\"Mider\") : _vm._e(), _c(\"Footer\"), _c(\"chatAi\")], 1);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"section-header\"\n  }, [_c(\"h2\", {\n    staticClass: \"section-title\"\n  }, [_vm._v(\"为您推荐\")]), _c(\"p\", {\n    staticClass: \"section-description\"\n  }, [_vm._v(\" 专注于提供高性能、稳定可靠的 GPU 算力服务，兼具灵活配置与优质性价比。 \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"section-header\"\n  }, [_c(\"h2\", {\n    staticClass: \"section-title\"\n  }, [_vm._v(\"核心优势\")]), _c(\"p\", {\n    staticClass: \"section-description\"\n  }, [_vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴。 \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"section-header\"\n  }, [_c(\"h2\", {\n    staticClass: \"section-title\"\n  }, [_vm._v(\"行业应用\")]), _c(\"p\", {\n    staticClass: \"section-description\"\n  }, [_vm._v(\" Applications \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"mobile-section-header\"\n  }, [_c(\"h2\", {\n    staticClass: \"mobile-section-title\"\n  }, [_vm._v(\"为您推荐\")]), _c(\"p\", {\n    staticClass: \"mobile-section-description\"\n  }, [_vm._v(\" 专注于提供高性能、稳定可靠的 GPU 算力服务 \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"mobile-section-header\"\n  }, [_c(\"h2\", {\n    staticClass: \"mobile-section-title\"\n  }, [_vm._v(\"GPU性能对比\")]), _c(\"p\", {\n    staticClass: \"mobile-section-description\"\n  }, [_vm._v(\" 专业GPU性能详细对比 \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"mobile-section-header\"\n  }, [_c(\"h2\", {\n    staticClass: \"mobile-section-title\"\n  }, [_vm._v(\"核心优势\")]), _c(\"p\", {\n    staticClass: \"mobile-section-description\"\n  }, [_vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴 \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"mobile-section-header\"\n  }, [_c(\"h2\", {\n    staticClass: \"mobile-section-title\"\n  }, [_vm._v(\"行业应用\")]), _c(\"p\", {\n    staticClass: \"mobile-section-description\"\n  }, [_vm._v(\" Applications \")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "isMobile", "staticClass", "style", "transform", "translate", "transition", "tsion", "_l", "bannerImages", "item", "index", "key", "attrs", "src", "img", "alt", "class", "content", "position", "domProps", "innerHTML", "_s", "title", "_v", "text", "is<PERSON>ogin", "secondaryLink", "href", "on", "click", "$event", "preventDefault", "navigateTo", "secondaryBtnText", "_e", "primaryLink", "primaryBtnText", "thirdLink", "thirdBtnText", "last", "require", "next", "ref", "active", "translateX", "_m", "gpus", "gpu", "recommended", "name", "isNew", "singlePrecision", "halfPrecision", "originalPrice", "price", "serviceList", "service", "icon", "desc", "mouseover", "firstRowWide", "hover", "mouseleave", "image", "firstRowTallApps", "app", "secondRowApps", "thirdRowSmallApps", "thirdRowWide", "openContactModal", "showContactModal", "target", "currentTarget", "closeContactModal", "apply", "arguments", "contactInfo", "phone", "touchstart", "handleTouchStart", "touchmove", "handleTouchMove", "touchend", "handleTouchEnd", "mobileCurrentSlide", "goToSlide", "comparisonGpus", "architecture", "fp16Performance", "fp32Performance", "memory", "memoryType", "bandwidth", "mobileApplications", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/views/Index/IndexView.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    [\n      !_vm.isMobile\n        ? _c(\n            \"div\",\n            { staticClass: \"desktop-layout\" },\n            [\n              _c(\"div\", { staticClass: \"banner-section\" }, [\n                _c(\"div\", { staticClass: \"banner-container\" }, [\n                  _c(\"div\", { staticClass: \"big-box\" }, [\n                    _c(\"div\", { staticClass: \"img-box\" }, [\n                      _c(\n                        \"div\",\n                        {\n                          staticClass: \"show-box\",\n                          style: {\n                            transform: \"translateX(\" + _vm.translate + \")\",\n                            transition: _vm.tsion ? \"all 0.5s\" : \"none\",\n                          },\n                        },\n                        _vm._l(_vm.bannerImages, function (item, index) {\n                          return _c(\n                            \"div\",\n                            { key: index, staticClass: \"slide-item\" },\n                            [\n                              _c(\"img\", { attrs: { src: item.img, alt: \"\" } }),\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"banner-content\",\n                                  class: \"pos-\" + item.content.position,\n                                },\n                                [\n                                  _c(\"h2\", {\n                                    staticClass: \"banner-title\",\n                                    domProps: {\n                                      innerHTML: _vm._s(item.content.title),\n                                    },\n                                  }),\n                                  _c(\"p\", { staticClass: \"banner-text\" }, [\n                                    _vm._v(_vm._s(item.content.text)),\n                                  ]),\n                                  _c(\"div\", { staticClass: \"banner-actions\" }, [\n                                    !_vm.isLogin && item.content.secondaryLink\n                                      ? _c(\n                                          \"a\",\n                                          {\n                                            staticClass:\n                                              \"banner-button secondary-btn\",\n                                            attrs: { href: \"#\" },\n                                            on: {\n                                              click: function ($event) {\n                                                $event.preventDefault()\n                                                return _vm.navigateTo(\n                                                  item.content.secondaryLink\n                                                )\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _vm._v(\n                                              \" \" +\n                                                _vm._s(\n                                                  item.content.secondaryBtnText\n                                                ) +\n                                                \" \"\n                                            ),\n                                          ]\n                                        )\n                                      : _vm._e(),\n                                    !_vm.isLogin && item.content.primaryLink\n                                      ? _c(\n                                          \"a\",\n                                          {\n                                            staticClass:\n                                              \"banner-button primary-btn\",\n                                            attrs: { href: \"#\" },\n                                            on: {\n                                              click: function ($event) {\n                                                $event.preventDefault()\n                                                return _vm.navigateTo(\n                                                  item.content.primaryLink\n                                                )\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _vm._v(\n                                              \" \" +\n                                                _vm._s(\n                                                  item.content.primaryBtnText\n                                                ) +\n                                                \" \"\n                                            ),\n                                          ]\n                                        )\n                                      : _vm._e(),\n                                    item.content.thirdLink\n                                      ? _c(\n                                          \"a\",\n                                          {\n                                            staticClass:\n                                              \"banner-button secondary-btn\",\n                                            attrs: { href: \"#\" },\n                                            on: {\n                                              click: function ($event) {\n                                                $event.preventDefault()\n                                                return _vm.navigateTo(\n                                                  item.content.thirdLink\n                                                )\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _vm._v(\n                                              \" \" +\n                                                _vm._s(\n                                                  item.content.thirdBtnText\n                                                ) +\n                                                \" \"\n                                            ),\n                                          ]\n                                        )\n                                      : _vm._e(),\n                                  ]),\n                                ]\n                              ),\n                            ]\n                          )\n                        }),\n                        0\n                      ),\n                    ]),\n                    _c(\"div\", { staticClass: \"arrowhead-box\" }, [\n                      _c(\n                        \"span\",\n                        {\n                          staticClass: \"nav-arrow left\",\n                          on: { click: _vm.last },\n                        },\n                        [\n                          _c(\"img\", {\n                            staticClass: \"arrow-icon rotated\",\n                            attrs: {\n                              src: require(\"../../assets/images/index/right-arrow.png\"),\n                              alt: \"\",\n                            },\n                          }),\n                        ]\n                      ),\n                      _c(\n                        \"span\",\n                        {\n                          staticClass: \"nav-arrow right\",\n                          on: { click: _vm.next },\n                        },\n                        [\n                          _c(\"img\", {\n                            staticClass: \"arrow-icon\",\n                            attrs: {\n                              src: require(\"../../assets/images/index/right-arrow.png\"),\n                              alt: \"\",\n                            },\n                          }),\n                        ]\n                      ),\n                    ]),\n                    _c(\n                      \"div\",\n                      {\n                        ref: \"swiperPagination\",\n                        staticClass: \"swiper-pagination\",\n                      },\n                      _vm._l(_vm.bannerImages, function (item, index) {\n                        return _c(\"span\", {\n                          key: index,\n                          class: { active: _vm.translateX === index },\n                        })\n                      }),\n                      0\n                    ),\n                  ]),\n                ]),\n              ]),\n              _c(\"section\", { staticClass: \"section gpu-section\" }, [\n                _c(\"div\", { staticClass: \"container\" }, [\n                  _vm._m(0),\n                  _c(\n                    \"div\",\n                    { staticClass: \"gpu-card-grid\" },\n                    _vm._l(_vm.gpus, function (gpu, index) {\n                      return _c(\n                        \"div\",\n                        {\n                          key: index,\n                          staticClass: \"gpu-card\",\n                          class: { recommended: gpu.recommended },\n                          on: {\n                            click: function ($event) {\n                              return _vm.navigateTo(\"/product\")\n                            },\n                          },\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"gpu-card-header\" }, [\n                            _c(\"h3\", { staticClass: \"gpu-name\" }, [\n                              _vm._v(_vm._s(gpu.name)),\n                            ]),\n                            gpu.recommended\n                              ? _c(\n                                  \"span\",\n                                  { staticClass: \"recommendation-tag\" },\n                                  [_vm._v(\"推荐\")]\n                                )\n                              : _vm._e(),\n                            gpu.isNew\n                              ? _c(\"span\", { staticClass: \"new-tag\" }, [\n                                  _vm._v(\"NEW\"),\n                                ])\n                              : _vm._e(),\n                          ]),\n                          _c(\"div\", { staticClass: \"gpu-specs-pricing\" }, [\n                            _c(\"div\", { staticClass: \"specs-section\" }, [\n                              _c(\"div\", { staticClass: \"spec-item\" }, [\n                                _c(\"span\", { staticClass: \"spec-label\" }, [\n                                  _vm._v(\"单精度:\"),\n                                ]),\n                                _c(\"span\", { staticClass: \"spec-value\" }, [\n                                  _vm._v(\n                                    _vm._s(gpu.singlePrecision) + \" TFLOPS\"\n                                  ),\n                                ]),\n                              ]),\n                              _c(\"div\", { staticClass: \"spec-item\" }, [\n                                _c(\"span\", { staticClass: \"spec-label\" }, [\n                                  _vm._v(\"半精度:\"),\n                                ]),\n                                _c(\"span\", { staticClass: \"spec-value\" }, [\n                                  _vm._v(\n                                    _vm._s(gpu.halfPrecision) + \" Tensor TFL\"\n                                  ),\n                                ]),\n                              ]),\n                            ]),\n                            _c(\"div\", { staticClass: \"price-section\" }, [\n                              _c(\"div\", { staticClass: \"gpu-pricing\" }, [\n                                gpu.originalPrice\n                                  ? _c(\n                                      \"span\",\n                                      { staticClass: \"original-price\" },\n                                      [\n                                        _vm._v(\n                                          \"¥\" +\n                                            _vm._s(gpu.originalPrice) +\n                                            \"/时\"\n                                        ),\n                                      ]\n                                    )\n                                  : _vm._e(),\n                                _c(\"span\", { staticClass: \"current-price\" }, [\n                                  _vm._v(\"¥\"),\n                                  _c(\"span\", { staticClass: \"price-value\" }, [\n                                    _vm._v(_vm._s(gpu.price)),\n                                  ]),\n                                  _vm._v(\"/时\"),\n                                ]),\n                              ]),\n                            ]),\n                          ]),\n                        ]\n                      )\n                    }),\n                    0\n                  ),\n                ]),\n              ]),\n              _c(\"GpuComparison\"),\n              _c(\"section\", { staticClass: \"section services-section\" }, [\n                _c(\"div\", { staticClass: \"container\" }, [\n                  _vm._m(1),\n                  _c(\n                    \"div\",\n                    { staticClass: \"services-grid\" },\n                    _vm._l(_vm.serviceList, function (service, index) {\n                      return _c(\n                        \"div\",\n                        { key: index, staticClass: \"service-item\" },\n                        [\n                          _c(\"div\", { staticClass: \"service-card\" }, [\n                            _c(\"i\", {\n                              staticClass: \"service-icon\",\n                              class: service.icon,\n                            }),\n                            _c(\"h3\", { staticClass: \"service-title\" }, [\n                              _vm._v(_vm._s(service.title)),\n                            ]),\n                            _c(\"div\", { staticClass: \"service-text\" }, [\n                              _c(\"p\", [_vm._v(_vm._s(service.desc))]),\n                            ]),\n                          ]),\n                        ]\n                      )\n                    }),\n                    0\n                  ),\n                ]),\n              ]),\n              _c(\"section\", { staticClass: \"appsec-section\" }, [\n                _vm._m(2),\n                _c(\n                  \"div\",\n                  { staticClass: \"appsec-container\" },\n                  [\n                    _c(\n                      \"div\",\n                      { staticClass: \"appsec-grid\" },\n                      [\n                        _c(\"div\", { staticClass: \"appsec-item appsec-wide\" }, [\n                          _c(\n                            \"div\",\n                            {\n                              staticClass: \"appsec-card\",\n                              on: {\n                                mouseover: function ($event) {\n                                  _vm.firstRowWide.hover = true\n                                },\n                                mouseleave: function ($event) {\n                                  _vm.firstRowWide.hover = false\n                                },\n                              },\n                            },\n                            [\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"appsec-image\",\n                                  class: {\n                                    \"appsec-hover\": _vm.firstRowWide.hover,\n                                  },\n                                },\n                                [\n                                  _c(\"img\", {\n                                    attrs: {\n                                      src: _vm.firstRowWide.image,\n                                      alt: _vm.firstRowWide.title,\n                                    },\n                                  }),\n                                ]\n                              ),\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"appsec-cardtitle\",\n                                  class: {\n                                    \"appsec-hover\": _vm.firstRowWide.hover,\n                                  },\n                                },\n                                [_vm._v(_vm._s(_vm.firstRowWide.title))]\n                              ),\n                            ]\n                          ),\n                        ]),\n                        _vm._l(_vm.firstRowTallApps, function (app, index) {\n                          return _c(\n                            \"div\",\n                            {\n                              key: \"tall-\" + index,\n                              staticClass: \"appsec-item appsec-tall\",\n                            },\n                            [\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"appsec-card\",\n                                  on: {\n                                    mouseover: function ($event) {\n                                      app.hover = true\n                                    },\n                                    mouseleave: function ($event) {\n                                      app.hover = false\n                                    },\n                                  },\n                                },\n                                [\n                                  _c(\n                                    \"div\",\n                                    {\n                                      staticClass: \"appsec-image\",\n                                      class: { \"appsec-hover\": app.hover },\n                                    },\n                                    [\n                                      _c(\"img\", {\n                                        attrs: {\n                                          src: app.image,\n                                          alt: app.title,\n                                        },\n                                      }),\n                                    ]\n                                  ),\n                                  _c(\n                                    \"div\",\n                                    {\n                                      staticClass: \"appsec-cardtitle\",\n                                      class: { \"appsec-hover\": app.hover },\n                                    },\n                                    [_vm._v(_vm._s(app.title))]\n                                  ),\n                                ]\n                              ),\n                            ]\n                          )\n                        }),\n                        _vm._l(_vm.secondRowApps, function (app, index) {\n                          return _c(\n                            \"div\",\n                            {\n                              key: \"small-\" + index,\n                              staticClass: \"appsec-item appsec-small\",\n                            },\n                            [\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"appsec-card\",\n                                  on: {\n                                    mouseover: function ($event) {\n                                      app.hover = true\n                                    },\n                                    mouseleave: function ($event) {\n                                      app.hover = false\n                                    },\n                                  },\n                                },\n                                [\n                                  _c(\n                                    \"div\",\n                                    {\n                                      staticClass: \"appsec-image\",\n                                      class: { \"appsec-hover\": app.hover },\n                                    },\n                                    [\n                                      _c(\"img\", {\n                                        attrs: {\n                                          src: app.image,\n                                          alt: app.title,\n                                        },\n                                      }),\n                                    ]\n                                  ),\n                                  _c(\n                                    \"div\",\n                                    {\n                                      staticClass: \"appsec-cardtitle\",\n                                      class: { \"appsec-hover\": app.hover },\n                                    },\n                                    [_vm._v(_vm._s(app.title))]\n                                  ),\n                                ]\n                              ),\n                            ]\n                          )\n                        }),\n                        _vm._l(_vm.thirdRowSmallApps, function (app, index) {\n                          return _c(\n                            \"div\",\n                            {\n                              key: \"third-small-\" + index,\n                              staticClass: \"appsec-item appsec-small\",\n                            },\n                            [\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"appsec-card\",\n                                  on: {\n                                    mouseover: function ($event) {\n                                      app.hover = true\n                                    },\n                                    mouseleave: function ($event) {\n                                      app.hover = false\n                                    },\n                                  },\n                                },\n                                [\n                                  _c(\n                                    \"div\",\n                                    {\n                                      staticClass: \"appsec-image\",\n                                      class: { \"appsec-hover\": app.hover },\n                                    },\n                                    [\n                                      _c(\"img\", {\n                                        attrs: {\n                                          src: app.image,\n                                          alt: app.title,\n                                        },\n                                      }),\n                                    ]\n                                  ),\n                                  _c(\n                                    \"div\",\n                                    {\n                                      staticClass: \"appsec-cardtitle\",\n                                      class: { \"appsec-hover\": app.hover },\n                                    },\n                                    [_vm._v(_vm._s(app.title))]\n                                  ),\n                                ]\n                              ),\n                            ]\n                          )\n                        }),\n                        _c(\"div\", { staticClass: \"appsec-item appsec-wide\" }, [\n                          _c(\n                            \"div\",\n                            {\n                              staticClass: \"appsec-card\",\n                              on: {\n                                mouseover: function ($event) {\n                                  _vm.thirdRowWide.hover = true\n                                },\n                                mouseleave: function ($event) {\n                                  _vm.thirdRowWide.hover = false\n                                },\n                              },\n                            },\n                            [\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"appsec-image\",\n                                  class: {\n                                    \"appsec-hover\": _vm.thirdRowWide.hover,\n                                  },\n                                },\n                                [\n                                  _c(\"img\", {\n                                    attrs: {\n                                      src: _vm.thirdRowWide.image,\n                                      alt: _vm.thirdRowWide.title,\n                                    },\n                                  }),\n                                ]\n                              ),\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"appsec-cardtitle\",\n                                  class: {\n                                    \"appsec-hover\": _vm.thirdRowWide.hover,\n                                  },\n                                },\n                                [_vm._v(_vm._s(_vm.thirdRowWide.title))]\n                              ),\n                            ]\n                          ),\n                        ]),\n                      ],\n                      2\n                    ),\n                    _c(\"chat-ai\"),\n                  ],\n                  1\n                ),\n              ]),\n              _c(\"div\", { staticClass: \"recommendation-tag1\" }, [\n                _c(\"div\", { staticClass: \"card1\" }, [\n                  _c(\"h1\", { staticClass: \"banner-text1\" }, [\n                    _vm._v(\"为AI+千行百业，提供高性能算力服务\"),\n                  ]),\n                  _c(\n                    \"button\",\n                    {\n                      staticClass: \"consult-button1\",\n                      on: { click: _vm.openContactModal },\n                    },\n                    [_vm._v(\"立即咨询\")]\n                  ),\n                ]),\n              ]),\n              _c(\"transition\", { attrs: { name: \"fade\" } }, [\n                _vm.showContactModal\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"contact-modal-overlay\",\n                        on: {\n                          click: function ($event) {\n                            if ($event.target !== $event.currentTarget)\n                              return null\n                            return _vm.closeContactModal.apply(null, arguments)\n                          },\n                        },\n                      },\n                      [\n                        _c(\"div\", { staticClass: \"contact-modal\" }, [\n                          _c(\n                            \"button\",\n                            {\n                              staticClass: \"close-modal\",\n                              on: { click: _vm.closeContactModal },\n                            },\n                            [_vm._v(\" × \")]\n                          ),\n                          _c(\"div\", { staticClass: \"contact-content\" }, [\n                            _c(\"div\", { staticClass: \"contact-item\" }, [\n                              _c(\"i\", { staticClass: \"am-icon-user\" }),\n                              _c(\"span\", [\n                                _vm._v(_vm._s(_vm.contactInfo.name)),\n                              ]),\n                            ]),\n                            _c(\"div\", { staticClass: \"contact-item\" }, [\n                              _c(\"i\", { staticClass: \"am-icon-phone\" }),\n                              _c(\"span\", [\n                                _vm._v(_vm._s(_vm.contactInfo.phone)),\n                              ]),\n                            ]),\n                          ]),\n                          _c(\"div\", { staticClass: \"contact-note\" }, [\n                            _c(\"p\", [_vm._v(\"欢迎随时来电咨询\")]),\n                          ]),\n                        ]),\n                      ]\n                    )\n                  : _vm._e(),\n              ]),\n            ],\n            1\n          )\n        : _c(\n            \"div\",\n            { staticClass: \"mobile-layout\" },\n            [\n              _c(\n                \"div\",\n                {\n                  staticClass: \"mobile-banner\",\n                  on: {\n                    touchstart: _vm.handleTouchStart,\n                    touchmove: _vm.handleTouchMove,\n                    touchend: _vm.handleTouchEnd,\n                  },\n                },\n                [\n                  _c(\n                    \"div\",\n                    {\n                      staticClass: \"mobile-banner-slider\",\n                      style: {\n                        transform: `translateX(${\n                          -_vm.mobileCurrentSlide * 100\n                        }%)`,\n                      },\n                    },\n                    _vm._l(_vm.bannerImages, function (item, index) {\n                      return _c(\n                        \"div\",\n                        { key: index, staticClass: \"mobile-slide\" },\n                        [\n                          _c(\"div\", { staticClass: \"mobile-slide-inner\" }, [\n                            _c(\"img\", {\n                              staticClass: \"mobile-slide-img\",\n                              attrs: { src: item.img, alt: \"\" },\n                            }),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"mobile-banner-content\",\n                                class: \"pos-\" + item.content.position,\n                              },\n                              [\n                                _c(\"h2\", {\n                                  staticClass: \"mobile-banner-title\",\n                                  domProps: {\n                                    innerHTML: _vm._s(item.content.title),\n                                  },\n                                }),\n                                _c(\"p\", { staticClass: \"mobile-banner-text\" }, [\n                                  _vm._v(_vm._s(item.content.text)),\n                                ]),\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"mobile-banner-actions\" },\n                                  [\n                                    !_vm.isLogin && item.content.secondaryLink\n                                      ? _c(\n                                          \"a\",\n                                          {\n                                            staticClass:\n                                              \"mobile-banner-button secondary-btn\",\n                                            attrs: { href: \"#\" },\n                                            on: {\n                                              click: function ($event) {\n                                                $event.preventDefault()\n                                                return _vm.navigateTo(\n                                                  item.content.secondaryLink\n                                                )\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _vm._v(\n                                              \" \" +\n                                                _vm._s(\n                                                  item.content.secondaryBtnText\n                                                ) +\n                                                \" \"\n                                            ),\n                                          ]\n                                        )\n                                      : _vm._e(),\n                                    !_vm.isLogin && item.content.primaryLink\n                                      ? _c(\n                                          \"a\",\n                                          {\n                                            staticClass:\n                                              \"mobile-banner-button primary-btn\",\n                                            attrs: { href: \"#\" },\n                                            on: {\n                                              click: function ($event) {\n                                                $event.preventDefault()\n                                                return _vm.navigateTo(\n                                                  item.content.primaryLink\n                                                )\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _vm._v(\n                                              \" \" +\n                                                _vm._s(\n                                                  item.content.primaryBtnText\n                                                ) +\n                                                \" \"\n                                            ),\n                                          ]\n                                        )\n                                      : _vm._e(),\n                                    item.content.thirdLink\n                                      ? _c(\n                                          \"a\",\n                                          {\n                                            staticClass:\n                                              \"banner-button secondary-btn\",\n                                            attrs: { href: \"#\" },\n                                            on: {\n                                              click: function ($event) {\n                                                $event.preventDefault()\n                                                return _vm.navigateTo(\n                                                  item.content.thirdLink\n                                                )\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _vm._v(\n                                              \" \" +\n                                                _vm._s(\n                                                  item.content.thirdBtnText\n                                                ) +\n                                                \" \"\n                                            ),\n                                          ]\n                                        )\n                                      : _vm._e(),\n                                  ]\n                                ),\n                              ]\n                            ),\n                          ]),\n                        ]\n                      )\n                    }),\n                    0\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"mobile-banner-pagination\" },\n                    _vm._l(_vm.bannerImages, function (item, index) {\n                      return _c(\"span\", {\n                        key: index,\n                        class: { active: _vm.mobileCurrentSlide === index },\n                        on: {\n                          click: function ($event) {\n                            return _vm.goToSlide(index)\n                          },\n                        },\n                      })\n                    }),\n                    0\n                  ),\n                ]\n              ),\n              _c(\n                \"section\",\n                { staticClass: \"mobile-section mobile-gpu-section\" },\n                [\n                  _vm._m(3),\n                  _c(\n                    \"div\",\n                    { staticClass: \"mobile-gpu-list\" },\n                    _vm._l(_vm.gpus, function (gpu, index) {\n                      return _c(\n                        \"div\",\n                        {\n                          key: index,\n                          staticClass: \"mobile-gpu-card\",\n                          class: { recommended: gpu.recommended },\n                          on: {\n                            click: function ($event) {\n                              return _vm.navigateTo(\"/product\")\n                            },\n                          },\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"mobile-gpu-header\" }, [\n                            _c(\"h3\", { staticClass: \"mobile-gpu-name\" }, [\n                              _vm._v(_vm._s(gpu.name)),\n                            ]),\n                            _c(\"div\", { staticClass: \"mobile-gpu-tags\" }, [\n                              gpu.recommended\n                                ? _c(\n                                    \"span\",\n                                    { staticClass: \"mobile-recommend-tag\" },\n                                    [_vm._v(\"推荐\")]\n                                  )\n                                : _vm._e(),\n                              gpu.isNew\n                                ? _c(\n                                    \"span\",\n                                    { staticClass: \"mobile-new-tag\" },\n                                    [_vm._v(\"NEW\")]\n                                  )\n                                : _vm._e(),\n                            ]),\n                          ]),\n                          _c(\"div\", { staticClass: \"mobile-gpu-specs\" }, [\n                            _c(\"div\", { staticClass: \"mobile-spec-item\" }, [\n                              _c(\"span\", { staticClass: \"mobile-spec-label\" }, [\n                                _vm._v(\"单精度:\"),\n                              ]),\n                              _c(\"span\", { staticClass: \"mobile-spec-value\" }, [\n                                _vm._v(_vm._s(gpu.singlePrecision) + \" TFLOPS\"),\n                              ]),\n                            ]),\n                            _c(\"div\", { staticClass: \"mobile-spec-item\" }, [\n                              _c(\"span\", { staticClass: \"mobile-spec-label\" }, [\n                                _vm._v(\"半精度:\"),\n                              ]),\n                              _c(\"span\", { staticClass: \"mobile-spec-value\" }, [\n                                _vm._v(\n                                  _vm._s(gpu.halfPrecision) + \" Tensor TFL\"\n                                ),\n                              ]),\n                            ]),\n                          ]),\n                          _c(\"div\", { staticClass: \"mobile-gpu-price\" }, [\n                            gpu.originalPrice\n                              ? _c(\n                                  \"span\",\n                                  { staticClass: \"mobile-original-price\" },\n                                  [\n                                    _vm._v(\n                                      \"¥\" + _vm._s(gpu.originalPrice) + \"/时\"\n                                    ),\n                                  ]\n                                )\n                              : _vm._e(),\n                            _c(\n                              \"span\",\n                              { staticClass: \"mobile-current-price\" },\n                              [_vm._v(\"¥\" + _vm._s(gpu.price) + \"/时\")]\n                            ),\n                          ]),\n                        ]\n                      )\n                    }),\n                    0\n                  ),\n                ]\n              ),\n              _c(\n                \"section\",\n                { staticClass: \"mobile-section mobile-comparison-section\" },\n                [\n                  _vm._m(4),\n                  _c(\"div\", { staticClass: \"mobile-comparison-container\" }, [\n                    _c(\"div\", { staticClass: \"mobile-comparison-scroll\" }, [\n                      _c(\"table\", { staticClass: \"mobile-comparison-table\" }, [\n                        _c(\"thead\", [\n                          _c(\n                            \"tr\",\n                            [\n                              _c(\"th\", [_vm._v(\"GPU型号\")]),\n                              _vm._l(_vm.comparisonGpus, function (gpu) {\n                                return _c(\"th\", { key: gpu.name }, [\n                                  _vm._v(_vm._s(gpu.name)),\n                                ])\n                              }),\n                            ],\n                            2\n                          ),\n                        ]),\n                        _c(\"tbody\", [\n                          _c(\n                            \"tr\",\n                            [\n                              _c(\"td\", [_vm._v(\"架构\")]),\n                              _vm._l(_vm.comparisonGpus, function (gpu) {\n                                return _c(\"td\", { key: gpu.name }, [\n                                  _vm._v(_vm._s(gpu.architecture)),\n                                ])\n                              }),\n                            ],\n                            2\n                          ),\n                          _c(\n                            \"tr\",\n                            [\n                              _c(\"td\", [_vm._v(\"FP16性能\")]),\n                              _vm._l(_vm.comparisonGpus, function (gpu) {\n                                return _c(\"td\", { key: gpu.name }, [\n                                  _vm._v(_vm._s(gpu.fp16Performance)),\n                                ])\n                              }),\n                            ],\n                            2\n                          ),\n                          _c(\n                            \"tr\",\n                            [\n                              _c(\"td\", [_vm._v(\"FP32性能\")]),\n                              _vm._l(_vm.comparisonGpus, function (gpu) {\n                                return _c(\"td\", { key: gpu.name }, [\n                                  _vm._v(_vm._s(gpu.fp32Performance)),\n                                ])\n                              }),\n                            ],\n                            2\n                          ),\n                          _c(\n                            \"tr\",\n                            [\n                              _c(\"td\", [_vm._v(\"显存\")]),\n                              _vm._l(_vm.comparisonGpus, function (gpu) {\n                                return _c(\"td\", { key: gpu.name }, [\n                                  _vm._v(_vm._s(gpu.memory)),\n                                ])\n                              }),\n                            ],\n                            2\n                          ),\n                          _c(\n                            \"tr\",\n                            [\n                              _c(\"td\", [_vm._v(\"显存类型\")]),\n                              _vm._l(_vm.comparisonGpus, function (gpu) {\n                                return _c(\"td\", { key: gpu.name }, [\n                                  _vm._v(_vm._s(gpu.memoryType)),\n                                ])\n                              }),\n                            ],\n                            2\n                          ),\n                          _c(\n                            \"tr\",\n                            [\n                              _c(\"td\", [_vm._v(\"带宽\")]),\n                              _vm._l(_vm.comparisonGpus, function (gpu) {\n                                return _c(\"td\", { key: gpu.name }, [\n                                  _vm._v(_vm._s(gpu.bandwidth)),\n                                ])\n                              }),\n                            ],\n                            2\n                          ),\n                        ]),\n                      ]),\n                    ]),\n                  ]),\n                ]\n              ),\n              _c(\n                \"section\",\n                { staticClass: \"mobile-section mobile-services-section\" },\n                [\n                  _vm._m(5),\n                  _c(\n                    \"div\",\n                    { staticClass: \"mobile-services-list\" },\n                    _vm._l(_vm.serviceList, function (service, index) {\n                      return _c(\n                        \"div\",\n                        { key: index, staticClass: \"mobile-service-card\" },\n                        [\n                          _c(\"div\", { staticClass: \"mobile-service-icon\" }, [\n                            _c(\"i\", { class: service.icon }),\n                          ]),\n                          _c(\"h3\", { staticClass: \"mobile-service-title\" }, [\n                            _vm._v(_vm._s(service.title)),\n                          ]),\n                          _c(\"p\", { staticClass: \"mobile-service-desc\" }, [\n                            _vm._v(_vm._s(service.desc)),\n                          ]),\n                        ]\n                      )\n                    }),\n                    0\n                  ),\n                ]\n              ),\n              _c(\n                \"section\",\n                { staticClass: \"mobile-section mobile-applications-section\" },\n                [\n                  _vm._m(6),\n                  _c(\n                    \"div\",\n                    { staticClass: \"mobile-applications-grid\" },\n                    _vm._l(_vm.mobileApplications, function (app, index) {\n                      return _c(\n                        \"div\",\n                        { key: index, staticClass: \"mobile-app-item\" },\n                        [\n                          _c(\"div\", { staticClass: \"mobile-app-image\" }, [\n                            _c(\"img\", {\n                              attrs: { src: app.image, alt: app.title },\n                            }),\n                          ]),\n                          _c(\"h3\", { staticClass: \"mobile-app-title\" }, [\n                            _vm._v(_vm._s(app.title)),\n                          ]),\n                        ]\n                      )\n                    }),\n                    0\n                  ),\n                ]\n              ),\n              _c(\"div\", { staticClass: \"mobile-consult-section\" }, [\n                _c(\"h3\", { staticClass: \"mobile-consult-title\" }, [\n                  _vm._v(\"为AI+千行百业，提供高性能算力服务\"),\n                ]),\n                _c(\n                  \"button\",\n                  {\n                    staticClass: \"mobile-consult-button\",\n                    on: { click: _vm.openContactModal },\n                  },\n                  [_vm._v(\"立即咨询\")]\n                ),\n              ]),\n              _c(\"transition\", { attrs: { name: \"mobile-fade\" } }, [\n                _vm.showContactModal\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"mobile-contact-overlay\",\n                        on: {\n                          click: function ($event) {\n                            if ($event.target !== $event.currentTarget)\n                              return null\n                            return _vm.closeContactModal.apply(null, arguments)\n                          },\n                        },\n                      },\n                      [\n                        _c(\"div\", { staticClass: \"mobile-contact-modal\" }, [\n                          _c(\n                            \"button\",\n                            {\n                              staticClass: \"mobile-close-modal\",\n                              on: { click: _vm.closeContactModal },\n                            },\n                            [_vm._v(\" × \")]\n                          ),\n                          _c(\"div\", { staticClass: \"mobile-contact-content\" }, [\n                            _c(\"div\", { staticClass: \"mobile-contact-item\" }, [\n                              _c(\"i\", { staticClass: \"am-icon-user\" }),\n                              _c(\"span\", [\n                                _vm._v(_vm._s(_vm.contactInfo.name)),\n                              ]),\n                            ]),\n                            _c(\"div\", { staticClass: \"mobile-contact-item\" }, [\n                              _c(\"i\", { staticClass: \"am-icon-phone\" }),\n                              _c(\"span\", [\n                                _vm._v(_vm._s(_vm.contactInfo.phone)),\n                              ]),\n                            ]),\n                          ]),\n                          _c(\"div\", { staticClass: \"mobile-contact-note\" }, [\n                            _c(\"p\", [_vm._v(\"欢迎随时来电咨询\")]),\n                          ]),\n                        ]),\n                      ]\n                    )\n                  : _vm._e(),\n              ]),\n            ],\n            1\n          ),\n      !_vm.isMobile ? _c(\"Mider\") : _vm._e(),\n      _c(\"Footer\"),\n      _c(\"chatAi\"),\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"section-header\" }, [\n      _c(\"h2\", { staticClass: \"section-title\" }, [_vm._v(\"为您推荐\")]),\n      _c(\"p\", { staticClass: \"section-description\" }, [\n        _vm._v(\n          \" 专注于提供高性能、稳定可靠的 GPU 算力服务，兼具灵活配置与优质性价比。 \"\n        ),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"section-header\" }, [\n      _c(\"h2\", { staticClass: \"section-title\" }, [_vm._v(\"核心优势\")]),\n      _c(\"p\", { staticClass: \"section-description\" }, [\n        _vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴。 \"),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"section-header\" }, [\n      _c(\"h2\", { staticClass: \"section-title\" }, [_vm._v(\"行业应用\")]),\n      _c(\"p\", { staticClass: \"section-description\" }, [\n        _vm._v(\" Applications \"),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"mobile-section-header\" }, [\n      _c(\"h2\", { staticClass: \"mobile-section-title\" }, [_vm._v(\"为您推荐\")]),\n      _c(\"p\", { staticClass: \"mobile-section-description\" }, [\n        _vm._v(\" 专注于提供高性能、稳定可靠的 GPU 算力服务 \"),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"mobile-section-header\" }, [\n      _c(\"h2\", { staticClass: \"mobile-section-title\" }, [\n        _vm._v(\"GPU性能对比\"),\n      ]),\n      _c(\"p\", { staticClass: \"mobile-section-description\" }, [\n        _vm._v(\" 专业GPU性能详细对比 \"),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"mobile-section-header\" }, [\n      _c(\"h2\", { staticClass: \"mobile-section-title\" }, [_vm._v(\"核心优势\")]),\n      _c(\"p\", { staticClass: \"mobile-section-description\" }, [\n        _vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴 \"),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"mobile-section-header\" }, [\n      _c(\"h2\", { staticClass: \"mobile-section-title\" }, [_vm._v(\"行业应用\")]),\n      _c(\"p\", { staticClass: \"mobile-section-description\" }, [\n        _vm._v(\" Applications \"),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL,CACE,CAACD,GAAG,CAACG,QAAQ,GACTF,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCH,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,UAAU;IACvBC,KAAK,EAAE;MACLC,SAAS,EAAE,aAAa,GAAGN,GAAG,CAACO,SAAS,GAAG,GAAG;MAC9CC,UAAU,EAAER,GAAG,CAACS,KAAK,GAAG,UAAU,GAAG;IACvC;EACF,CAAC,EACDT,GAAG,CAACU,EAAE,CAACV,GAAG,CAACW,YAAY,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IAC9C,OAAOZ,EAAE,CACP,KAAK,EACL;MAAEa,GAAG,EAAED,KAAK;MAAET,WAAW,EAAE;IAAa,CAAC,EACzC,CACEH,EAAE,CAAC,KAAK,EAAE;MAAEc,KAAK,EAAE;QAAEC,GAAG,EAAEJ,IAAI,CAACK,GAAG;QAAEC,GAAG,EAAE;MAAG;IAAE,CAAC,CAAC,EAChDjB,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,gBAAgB;MAC7Be,KAAK,EAAE,MAAM,GAAGP,IAAI,CAACQ,OAAO,CAACC;IAC/B,CAAC,EACD,CACEpB,EAAE,CAAC,IAAI,EAAE;MACPG,WAAW,EAAE,cAAc;MAC3BkB,QAAQ,EAAE;QACRC,SAAS,EAAEvB,GAAG,CAACwB,EAAE,CAACZ,IAAI,CAACQ,OAAO,CAACK,KAAK;MACtC;IACF,CAAC,CAAC,EACFxB,EAAE,CAAC,GAAG,EAAE;MAAEG,WAAW,EAAE;IAAc,CAAC,EAAE,CACtCJ,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACZ,IAAI,CAACQ,OAAO,CAACO,IAAI,CAAC,CAAC,CAClC,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAiB,CAAC,EAAE,CAC3C,CAACJ,GAAG,CAAC4B,OAAO,IAAIhB,IAAI,CAACQ,OAAO,CAACS,aAAa,GACtC5B,EAAE,CACA,GAAG,EACH;MACEG,WAAW,EACT,6BAA6B;MAC/BW,KAAK,EAAE;QAAEe,IAAI,EAAE;MAAI,CAAC;MACpBC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvBA,MAAM,CAACC,cAAc,EAAE;UACvB,OAAOlC,GAAG,CAACmC,UAAU,CACnBvB,IAAI,CAACQ,OAAO,CAACS,aAAa,CAC3B;QACH;MACF;IACF,CAAC,EACD,CACE7B,GAAG,CAAC0B,EAAE,CACJ,GAAG,GACD1B,GAAG,CAACwB,EAAE,CACJZ,IAAI,CAACQ,OAAO,CAACgB,gBAAgB,CAC9B,GACD,GAAG,CACN,CACF,CACF,GACDpC,GAAG,CAACqC,EAAE,EAAE,EACZ,CAACrC,GAAG,CAAC4B,OAAO,IAAIhB,IAAI,CAACQ,OAAO,CAACkB,WAAW,GACpCrC,EAAE,CACA,GAAG,EACH;MACEG,WAAW,EACT,2BAA2B;MAC7BW,KAAK,EAAE;QAAEe,IAAI,EAAE;MAAI,CAAC;MACpBC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvBA,MAAM,CAACC,cAAc,EAAE;UACvB,OAAOlC,GAAG,CAACmC,UAAU,CACnBvB,IAAI,CAACQ,OAAO,CAACkB,WAAW,CACzB;QACH;MACF;IACF,CAAC,EACD,CACEtC,GAAG,CAAC0B,EAAE,CACJ,GAAG,GACD1B,GAAG,CAACwB,EAAE,CACJZ,IAAI,CAACQ,OAAO,CAACmB,cAAc,CAC5B,GACD,GAAG,CACN,CACF,CACF,GACDvC,GAAG,CAACqC,EAAE,EAAE,EACZzB,IAAI,CAACQ,OAAO,CAACoB,SAAS,GAClBvC,EAAE,CACA,GAAG,EACH;MACEG,WAAW,EACT,6BAA6B;MAC/BW,KAAK,EAAE;QAAEe,IAAI,EAAE;MAAI,CAAC;MACpBC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvBA,MAAM,CAACC,cAAc,EAAE;UACvB,OAAOlC,GAAG,CAACmC,UAAU,CACnBvB,IAAI,CAACQ,OAAO,CAACoB,SAAS,CACvB;QACH;MACF;IACF,CAAC,EACD,CACExC,GAAG,CAAC0B,EAAE,CACJ,GAAG,GACD1B,GAAG,CAACwB,EAAE,CACJZ,IAAI,CAACQ,OAAO,CAACqB,YAAY,CAC1B,GACD,GAAG,CACN,CACF,CACF,GACDzC,GAAG,CAACqC,EAAE,EAAE,CACb,CAAC,CACH,CACF,CACF,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CAAC,EACFpC,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CH,EAAE,CACA,MAAM,EACN;IACEG,WAAW,EAAE,gBAAgB;IAC7B2B,EAAE,EAAE;MAAEC,KAAK,EAAEhC,GAAG,CAAC0C;IAAK;EACxB,CAAC,EACD,CACEzC,EAAE,CAAC,KAAK,EAAE;IACRG,WAAW,EAAE,oBAAoB;IACjCW,KAAK,EAAE;MACLC,GAAG,EAAE2B,OAAO,CAAC,2CAA2C,CAAC;MACzDzB,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CACF,EACDjB,EAAE,CACA,MAAM,EACN;IACEG,WAAW,EAAE,iBAAiB;IAC9B2B,EAAE,EAAE;MAAEC,KAAK,EAAEhC,GAAG,CAAC4C;IAAK;EACxB,CAAC,EACD,CACE3C,EAAE,CAAC,KAAK,EAAE;IACRG,WAAW,EAAE,YAAY;IACzBW,KAAK,EAAE;MACLC,GAAG,EAAE2B,OAAO,CAAC,2CAA2C,CAAC;MACzDzB,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CACF,CACF,CAAC,EACFjB,EAAE,CACA,KAAK,EACL;IACE4C,GAAG,EAAE,kBAAkB;IACvBzC,WAAW,EAAE;EACf,CAAC,EACDJ,GAAG,CAACU,EAAE,CAACV,GAAG,CAACW,YAAY,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IAC9C,OAAOZ,EAAE,CAAC,MAAM,EAAE;MAChBa,GAAG,EAAED,KAAK;MACVM,KAAK,EAAE;QAAE2B,MAAM,EAAE9C,GAAG,CAAC+C,UAAU,KAAKlC;MAAM;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CAAC,CACF,CACF,CAAC,CACH,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,SAAS,EAAE;IAAEG,WAAW,EAAE;EAAsB,CAAC,EAAE,CACpDH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCJ,GAAG,CAACgD,EAAE,CAAC,CAAC,CAAC,EACT/C,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAgB,CAAC,EAChCJ,GAAG,CAACU,EAAE,CAACV,GAAG,CAACiD,IAAI,EAAE,UAAUC,GAAG,EAAErC,KAAK,EAAE;IACrC,OAAOZ,EAAE,CACP,KAAK,EACL;MACEa,GAAG,EAAED,KAAK;MACVT,WAAW,EAAE,UAAU;MACvBe,KAAK,EAAE;QAAEgC,WAAW,EAAED,GAAG,CAACC;MAAY,CAAC;MACvCpB,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOjC,GAAG,CAACmC,UAAU,CAAC,UAAU,CAAC;QACnC;MACF;IACF,CAAC,EACD,CACElC,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5CH,EAAE,CAAC,IAAI,EAAE;MAAEG,WAAW,EAAE;IAAW,CAAC,EAAE,CACpCJ,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACE,IAAI,CAAC,CAAC,CACzB,CAAC,EACFF,GAAG,CAACC,WAAW,GACXlD,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAqB,CAAC,EACrC,CAACJ,GAAG,CAAC0B,EAAE,CAAC,IAAI,CAAC,CAAC,CACf,GACD1B,GAAG,CAACqC,EAAE,EAAE,EACZa,GAAG,CAACG,KAAK,GACLpD,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAU,CAAC,EAAE,CACrCJ,GAAG,CAAC0B,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,GACF1B,GAAG,CAACqC,EAAE,EAAE,CACb,CAAC,EACFpC,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAoB,CAAC,EAAE,CAC9CH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAgB,CAAC,EAAE,CAC1CH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAa,CAAC,EAAE,CACxCJ,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFzB,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAa,CAAC,EAAE,CACxCJ,GAAG,CAAC0B,EAAE,CACJ1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACI,eAAe,CAAC,GAAG,SAAS,CACxC,CACF,CAAC,CACH,CAAC,EACFrD,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAa,CAAC,EAAE,CACxCJ,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFzB,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAa,CAAC,EAAE,CACxCJ,GAAG,CAAC0B,EAAE,CACJ1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACK,aAAa,CAAC,GAAG,aAAa,CAC1C,CACF,CAAC,CACH,CAAC,CACH,CAAC,EACFtD,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAgB,CAAC,EAAE,CAC1CH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAc,CAAC,EAAE,CACxC8C,GAAG,CAACM,aAAa,GACbvD,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAiB,CAAC,EACjC,CACEJ,GAAG,CAAC0B,EAAE,CACJ,GAAG,GACD1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACM,aAAa,CAAC,GACzB,IAAI,CACP,CACF,CACF,GACDxD,GAAG,CAACqC,EAAE,EAAE,EACZpC,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAgB,CAAC,EAAE,CAC3CJ,GAAG,CAAC0B,EAAE,CAAC,GAAG,CAAC,EACXzB,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAc,CAAC,EAAE,CACzCJ,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACO,KAAK,CAAC,CAAC,CAC1B,CAAC,EACFzD,GAAG,CAAC0B,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CAAC,CACH,CAAC,EACFzB,EAAE,CAAC,eAAe,CAAC,EACnBA,EAAE,CAAC,SAAS,EAAE;IAAEG,WAAW,EAAE;EAA2B,CAAC,EAAE,CACzDH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCJ,GAAG,CAACgD,EAAE,CAAC,CAAC,CAAC,EACT/C,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAgB,CAAC,EAChCJ,GAAG,CAACU,EAAE,CAACV,GAAG,CAAC0D,WAAW,EAAE,UAAUC,OAAO,EAAE9C,KAAK,EAAE;IAChD,OAAOZ,EAAE,CACP,KAAK,EACL;MAAEa,GAAG,EAAED,KAAK;MAAET,WAAW,EAAE;IAAe,CAAC,EAC3C,CACEH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCH,EAAE,CAAC,GAAG,EAAE;MACNG,WAAW,EAAE,cAAc;MAC3Be,KAAK,EAAEwC,OAAO,CAACC;IACjB,CAAC,CAAC,EACF3D,EAAE,CAAC,IAAI,EAAE;MAAEG,WAAW,EAAE;IAAgB,CAAC,EAAE,CACzCJ,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACmC,OAAO,CAAClC,KAAK,CAAC,CAAC,CAC9B,CAAC,EACFxB,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCH,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACmC,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC,CACxC,CAAC,CACH,CAAC,CACH,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CAAC,CACH,CAAC,EACF5D,EAAE,CAAC,SAAS,EAAE;IAAEG,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC/CJ,GAAG,CAACgD,EAAE,CAAC,CAAC,CAAC,EACT/C,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAmB,CAAC,EACnC,CACEH,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAA0B,CAAC,EAAE,CACpDH,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,aAAa;IAC1B2B,EAAE,EAAE;MACF+B,SAAS,EAAE,SAAAA,CAAU7B,MAAM,EAAE;QAC3BjC,GAAG,CAAC+D,YAAY,CAACC,KAAK,GAAG,IAAI;MAC/B,CAAC;MACDC,UAAU,EAAE,SAAAA,CAAUhC,MAAM,EAAE;QAC5BjC,GAAG,CAAC+D,YAAY,CAACC,KAAK,GAAG,KAAK;MAChC;IACF;EACF,CAAC,EACD,CACE/D,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,cAAc;IAC3Be,KAAK,EAAE;MACL,cAAc,EAAEnB,GAAG,CAAC+D,YAAY,CAACC;IACnC;EACF,CAAC,EACD,CACE/D,EAAE,CAAC,KAAK,EAAE;IACRc,KAAK,EAAE;MACLC,GAAG,EAAEhB,GAAG,CAAC+D,YAAY,CAACG,KAAK;MAC3BhD,GAAG,EAAElB,GAAG,CAAC+D,YAAY,CAACtC;IACxB;EACF,CAAC,CAAC,CACH,CACF,EACDxB,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,kBAAkB;IAC/Be,KAAK,EAAE;MACL,cAAc,EAAEnB,GAAG,CAAC+D,YAAY,CAACC;IACnC;EACF,CAAC,EACD,CAAChE,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAAC+D,YAAY,CAACtC,KAAK,CAAC,CAAC,CAAC,CACzC,CACF,CACF,CACF,CAAC,EACFzB,GAAG,CAACU,EAAE,CAACV,GAAG,CAACmE,gBAAgB,EAAE,UAAUC,GAAG,EAAEvD,KAAK,EAAE;IACjD,OAAOZ,EAAE,CACP,KAAK,EACL;MACEa,GAAG,EAAE,OAAO,GAAGD,KAAK;MACpBT,WAAW,EAAE;IACf,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,aAAa;MAC1B2B,EAAE,EAAE;QACF+B,SAAS,EAAE,SAAAA,CAAU7B,MAAM,EAAE;UAC3BmC,GAAG,CAACJ,KAAK,GAAG,IAAI;QAClB,CAAC;QACDC,UAAU,EAAE,SAAAA,CAAUhC,MAAM,EAAE;UAC5BmC,GAAG,CAACJ,KAAK,GAAG,KAAK;QACnB;MACF;IACF,CAAC,EACD,CACE/D,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,cAAc;MAC3Be,KAAK,EAAE;QAAE,cAAc,EAAEiD,GAAG,CAACJ;MAAM;IACrC,CAAC,EACD,CACE/D,EAAE,CAAC,KAAK,EAAE;MACRc,KAAK,EAAE;QACLC,GAAG,EAAEoD,GAAG,CAACF,KAAK;QACdhD,GAAG,EAAEkD,GAAG,CAAC3C;MACX;IACF,CAAC,CAAC,CACH,CACF,EACDxB,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,kBAAkB;MAC/Be,KAAK,EAAE;QAAE,cAAc,EAAEiD,GAAG,CAACJ;MAAM;IACrC,CAAC,EACD,CAAChE,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC4C,GAAG,CAAC3C,KAAK,CAAC,CAAC,CAAC,CAC5B,CACF,CACF,CACF,CACF;EACH,CAAC,CAAC,EACFzB,GAAG,CAACU,EAAE,CAACV,GAAG,CAACqE,aAAa,EAAE,UAAUD,GAAG,EAAEvD,KAAK,EAAE;IAC9C,OAAOZ,EAAE,CACP,KAAK,EACL;MACEa,GAAG,EAAE,QAAQ,GAAGD,KAAK;MACrBT,WAAW,EAAE;IACf,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,aAAa;MAC1B2B,EAAE,EAAE;QACF+B,SAAS,EAAE,SAAAA,CAAU7B,MAAM,EAAE;UAC3BmC,GAAG,CAACJ,KAAK,GAAG,IAAI;QAClB,CAAC;QACDC,UAAU,EAAE,SAAAA,CAAUhC,MAAM,EAAE;UAC5BmC,GAAG,CAACJ,KAAK,GAAG,KAAK;QACnB;MACF;IACF,CAAC,EACD,CACE/D,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,cAAc;MAC3Be,KAAK,EAAE;QAAE,cAAc,EAAEiD,GAAG,CAACJ;MAAM;IACrC,CAAC,EACD,CACE/D,EAAE,CAAC,KAAK,EAAE;MACRc,KAAK,EAAE;QACLC,GAAG,EAAEoD,GAAG,CAACF,KAAK;QACdhD,GAAG,EAAEkD,GAAG,CAAC3C;MACX;IACF,CAAC,CAAC,CACH,CACF,EACDxB,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,kBAAkB;MAC/Be,KAAK,EAAE;QAAE,cAAc,EAAEiD,GAAG,CAACJ;MAAM;IACrC,CAAC,EACD,CAAChE,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC4C,GAAG,CAAC3C,KAAK,CAAC,CAAC,CAAC,CAC5B,CACF,CACF,CACF,CACF;EACH,CAAC,CAAC,EACFzB,GAAG,CAACU,EAAE,CAACV,GAAG,CAACsE,iBAAiB,EAAE,UAAUF,GAAG,EAAEvD,KAAK,EAAE;IAClD,OAAOZ,EAAE,CACP,KAAK,EACL;MACEa,GAAG,EAAE,cAAc,GAAGD,KAAK;MAC3BT,WAAW,EAAE;IACf,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,aAAa;MAC1B2B,EAAE,EAAE;QACF+B,SAAS,EAAE,SAAAA,CAAU7B,MAAM,EAAE;UAC3BmC,GAAG,CAACJ,KAAK,GAAG,IAAI;QAClB,CAAC;QACDC,UAAU,EAAE,SAAAA,CAAUhC,MAAM,EAAE;UAC5BmC,GAAG,CAACJ,KAAK,GAAG,KAAK;QACnB;MACF;IACF,CAAC,EACD,CACE/D,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,cAAc;MAC3Be,KAAK,EAAE;QAAE,cAAc,EAAEiD,GAAG,CAACJ;MAAM;IACrC,CAAC,EACD,CACE/D,EAAE,CAAC,KAAK,EAAE;MACRc,KAAK,EAAE;QACLC,GAAG,EAAEoD,GAAG,CAACF,KAAK;QACdhD,GAAG,EAAEkD,GAAG,CAAC3C;MACX;IACF,CAAC,CAAC,CACH,CACF,EACDxB,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,kBAAkB;MAC/Be,KAAK,EAAE;QAAE,cAAc,EAAEiD,GAAG,CAACJ;MAAM;IACrC,CAAC,EACD,CAAChE,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC4C,GAAG,CAAC3C,KAAK,CAAC,CAAC,CAAC,CAC5B,CACF,CACF,CACF,CACF;EACH,CAAC,CAAC,EACFxB,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAA0B,CAAC,EAAE,CACpDH,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,aAAa;IAC1B2B,EAAE,EAAE;MACF+B,SAAS,EAAE,SAAAA,CAAU7B,MAAM,EAAE;QAC3BjC,GAAG,CAACuE,YAAY,CAACP,KAAK,GAAG,IAAI;MAC/B,CAAC;MACDC,UAAU,EAAE,SAAAA,CAAUhC,MAAM,EAAE;QAC5BjC,GAAG,CAACuE,YAAY,CAACP,KAAK,GAAG,KAAK;MAChC;IACF;EACF,CAAC,EACD,CACE/D,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,cAAc;IAC3Be,KAAK,EAAE;MACL,cAAc,EAAEnB,GAAG,CAACuE,YAAY,CAACP;IACnC;EACF,CAAC,EACD,CACE/D,EAAE,CAAC,KAAK,EAAE;IACRc,KAAK,EAAE;MACLC,GAAG,EAAEhB,GAAG,CAACuE,YAAY,CAACL,KAAK;MAC3BhD,GAAG,EAAElB,GAAG,CAACuE,YAAY,CAAC9C;IACxB;EACF,CAAC,CAAC,CACH,CACF,EACDxB,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,kBAAkB;IAC/Be,KAAK,EAAE;MACL,cAAc,EAAEnB,GAAG,CAACuE,YAAY,CAACP;IACnC;EACF,CAAC,EACD,CAAChE,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACuE,YAAY,CAAC9C,KAAK,CAAC,CAAC,CAAC,CACzC,CACF,CACF,CACF,CAAC,CACH,EACD,CAAC,CACF,EACDxB,EAAE,CAAC,SAAS,CAAC,CACd,EACD,CAAC,CACF,CACF,CAAC,EACFA,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCH,EAAE,CAAC,IAAI,EAAE;IAAEG,WAAW,EAAE;EAAe,CAAC,EAAE,CACxCJ,GAAG,CAAC0B,EAAE,CAAC,oBAAoB,CAAC,CAC7B,CAAC,EACFzB,EAAE,CACA,QAAQ,EACR;IACEG,WAAW,EAAE,iBAAiB;IAC9B2B,EAAE,EAAE;MAAEC,KAAK,EAAEhC,GAAG,CAACwE;IAAiB;EACpC,CAAC,EACD,CAACxE,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,CAAC,CACH,CAAC,EACFzB,EAAE,CAAC,YAAY,EAAE;IAAEc,KAAK,EAAE;MAAEqC,IAAI,EAAE;IAAO;EAAE,CAAC,EAAE,CAC5CpD,GAAG,CAACyE,gBAAgB,GAChBxE,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,uBAAuB;IACpC2B,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,IAAIA,MAAM,CAACyC,MAAM,KAAKzC,MAAM,CAAC0C,aAAa,EACxC,OAAO,IAAI;QACb,OAAO3E,GAAG,CAAC4E,iBAAiB,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACrD;IACF;EACF,CAAC,EACD,CACE7E,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CH,EAAE,CACA,QAAQ,EACR;IACEG,WAAW,EAAE,aAAa;IAC1B2B,EAAE,EAAE;MAAEC,KAAK,EAAEhC,GAAG,CAAC4E;IAAkB;EACrC,CAAC,EACD,CAAC5E,GAAG,CAAC0B,EAAE,CAAC,KAAK,CAAC,CAAC,CAChB,EACDzB,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCH,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAAC+E,WAAW,CAAC3B,IAAI,CAAC,CAAC,CACrC,CAAC,CACH,CAAC,EACFnD,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCH,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAAgB,CAAC,CAAC,EACzCH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAAC+E,WAAW,CAACC,KAAK,CAAC,CAAC,CACtC,CAAC,CACH,CAAC,CACH,CAAC,EACF/E,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCH,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAAC0B,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAC9B,CAAC,CACH,CAAC,CACH,CACF,GACD1B,GAAG,CAACqC,EAAE,EAAE,CACb,CAAC,CACH,EACD,CAAC,CACF,GACDpC,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEH,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,eAAe;IAC5B2B,EAAE,EAAE;MACFkD,UAAU,EAAEjF,GAAG,CAACkF,gBAAgB;MAChCC,SAAS,EAAEnF,GAAG,CAACoF,eAAe;MAC9BC,QAAQ,EAAErF,GAAG,CAACsF;IAChB;EACF,CAAC,EACD,CACErF,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,sBAAsB;IACnCC,KAAK,EAAE;MACLC,SAAS,EAAG,cACV,CAACN,GAAG,CAACuF,kBAAkB,GAAG,GAC3B;IACH;EACF,CAAC,EACDvF,GAAG,CAACU,EAAE,CAACV,GAAG,CAACW,YAAY,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IAC9C,OAAOZ,EAAE,CACP,KAAK,EACL;MAAEa,GAAG,EAAED,KAAK;MAAET,WAAW,EAAE;IAAe,CAAC,EAC3C,CACEH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAqB,CAAC,EAAE,CAC/CH,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,kBAAkB;MAC/BW,KAAK,EAAE;QAAEC,GAAG,EAAEJ,IAAI,CAACK,GAAG;QAAEC,GAAG,EAAE;MAAG;IAClC,CAAC,CAAC,EACFjB,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,uBAAuB;MACpCe,KAAK,EAAE,MAAM,GAAGP,IAAI,CAACQ,OAAO,CAACC;IAC/B,CAAC,EACD,CACEpB,EAAE,CAAC,IAAI,EAAE;MACPG,WAAW,EAAE,qBAAqB;MAClCkB,QAAQ,EAAE;QACRC,SAAS,EAAEvB,GAAG,CAACwB,EAAE,CAACZ,IAAI,CAACQ,OAAO,CAACK,KAAK;MACtC;IACF,CAAC,CAAC,EACFxB,EAAE,CAAC,GAAG,EAAE;MAAEG,WAAW,EAAE;IAAqB,CAAC,EAAE,CAC7CJ,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACZ,IAAI,CAACQ,OAAO,CAACO,IAAI,CAAC,CAAC,CAClC,CAAC,EACF1B,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAwB,CAAC,EACxC,CACE,CAACJ,GAAG,CAAC4B,OAAO,IAAIhB,IAAI,CAACQ,OAAO,CAACS,aAAa,GACtC5B,EAAE,CACA,GAAG,EACH;MACEG,WAAW,EACT,oCAAoC;MACtCW,KAAK,EAAE;QAAEe,IAAI,EAAE;MAAI,CAAC;MACpBC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvBA,MAAM,CAACC,cAAc,EAAE;UACvB,OAAOlC,GAAG,CAACmC,UAAU,CACnBvB,IAAI,CAACQ,OAAO,CAACS,aAAa,CAC3B;QACH;MACF;IACF,CAAC,EACD,CACE7B,GAAG,CAAC0B,EAAE,CACJ,GAAG,GACD1B,GAAG,CAACwB,EAAE,CACJZ,IAAI,CAACQ,OAAO,CAACgB,gBAAgB,CAC9B,GACD,GAAG,CACN,CACF,CACF,GACDpC,GAAG,CAACqC,EAAE,EAAE,EACZ,CAACrC,GAAG,CAAC4B,OAAO,IAAIhB,IAAI,CAACQ,OAAO,CAACkB,WAAW,GACpCrC,EAAE,CACA,GAAG,EACH;MACEG,WAAW,EACT,kCAAkC;MACpCW,KAAK,EAAE;QAAEe,IAAI,EAAE;MAAI,CAAC;MACpBC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvBA,MAAM,CAACC,cAAc,EAAE;UACvB,OAAOlC,GAAG,CAACmC,UAAU,CACnBvB,IAAI,CAACQ,OAAO,CAACkB,WAAW,CACzB;QACH;MACF;IACF,CAAC,EACD,CACEtC,GAAG,CAAC0B,EAAE,CACJ,GAAG,GACD1B,GAAG,CAACwB,EAAE,CACJZ,IAAI,CAACQ,OAAO,CAACmB,cAAc,CAC5B,GACD,GAAG,CACN,CACF,CACF,GACDvC,GAAG,CAACqC,EAAE,EAAE,EACZzB,IAAI,CAACQ,OAAO,CAACoB,SAAS,GAClBvC,EAAE,CACA,GAAG,EACH;MACEG,WAAW,EACT,6BAA6B;MAC/BW,KAAK,EAAE;QAAEe,IAAI,EAAE;MAAI,CAAC;MACpBC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvBA,MAAM,CAACC,cAAc,EAAE;UACvB,OAAOlC,GAAG,CAACmC,UAAU,CACnBvB,IAAI,CAACQ,OAAO,CAACoB,SAAS,CACvB;QACH;MACF;IACF,CAAC,EACD,CACExC,GAAG,CAAC0B,EAAE,CACJ,GAAG,GACD1B,GAAG,CAACwB,EAAE,CACJZ,IAAI,CAACQ,OAAO,CAACqB,YAAY,CAC1B,GACD,GAAG,CACN,CACF,CACF,GACDzC,GAAG,CAACqC,EAAE,EAAE,CACb,CACF,CACF,CACF,CACF,CAAC,CACH,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,EACDpC,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAA2B,CAAC,EAC3CJ,GAAG,CAACU,EAAE,CAACV,GAAG,CAACW,YAAY,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IAC9C,OAAOZ,EAAE,CAAC,MAAM,EAAE;MAChBa,GAAG,EAAED,KAAK;MACVM,KAAK,EAAE;QAAE2B,MAAM,EAAE9C,GAAG,CAACuF,kBAAkB,KAAK1E;MAAM,CAAC;MACnDkB,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOjC,GAAG,CAACwF,SAAS,CAAC3E,KAAK,CAAC;QAC7B;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CAAC,CACF,CACF,CACF,EACDZ,EAAE,CACA,SAAS,EACT;IAAEG,WAAW,EAAE;EAAoC,CAAC,EACpD,CACEJ,GAAG,CAACgD,EAAE,CAAC,CAAC,CAAC,EACT/C,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAkB,CAAC,EAClCJ,GAAG,CAACU,EAAE,CAACV,GAAG,CAACiD,IAAI,EAAE,UAAUC,GAAG,EAAErC,KAAK,EAAE;IACrC,OAAOZ,EAAE,CACP,KAAK,EACL;MACEa,GAAG,EAAED,KAAK;MACVT,WAAW,EAAE,iBAAiB;MAC9Be,KAAK,EAAE;QAAEgC,WAAW,EAAED,GAAG,CAACC;MAAY,CAAC;MACvCpB,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOjC,GAAG,CAACmC,UAAU,CAAC,UAAU,CAAC;QACnC;MACF;IACF,CAAC,EACD,CACElC,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAoB,CAAC,EAAE,CAC9CH,EAAE,CAAC,IAAI,EAAE;MAAEG,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC3CJ,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACE,IAAI,CAAC,CAAC,CACzB,CAAC,EACFnD,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5C8C,GAAG,CAACC,WAAW,GACXlD,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAuB,CAAC,EACvC,CAACJ,GAAG,CAAC0B,EAAE,CAAC,IAAI,CAAC,CAAC,CACf,GACD1B,GAAG,CAACqC,EAAE,EAAE,EACZa,GAAG,CAACG,KAAK,GACLpD,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAiB,CAAC,EACjC,CAACJ,GAAG,CAAC0B,EAAE,CAAC,KAAK,CAAC,CAAC,CAChB,GACD1B,GAAG,CAACqC,EAAE,EAAE,CACb,CAAC,CACH,CAAC,EACFpC,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7CH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7CH,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAoB,CAAC,EAAE,CAC/CJ,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFzB,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAoB,CAAC,EAAE,CAC/CJ,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACI,eAAe,CAAC,GAAG,SAAS,CAAC,CAChD,CAAC,CACH,CAAC,EACFrD,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7CH,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAoB,CAAC,EAAE,CAC/CJ,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFzB,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAoB,CAAC,EAAE,CAC/CJ,GAAG,CAAC0B,EAAE,CACJ1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACK,aAAa,CAAC,GAAG,aAAa,CAC1C,CACF,CAAC,CACH,CAAC,CACH,CAAC,EACFtD,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7C8C,GAAG,CAACM,aAAa,GACbvD,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAwB,CAAC,EACxC,CACEJ,GAAG,CAAC0B,EAAE,CACJ,GAAG,GAAG1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACM,aAAa,CAAC,GAAG,IAAI,CACvC,CACF,CACF,GACDxD,GAAG,CAACqC,EAAE,EAAE,EACZpC,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAuB,CAAC,EACvC,CAACJ,GAAG,CAAC0B,EAAE,CAAC,GAAG,GAAG1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACO,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CACzC,CACF,CAAC,CACH,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CACF,EACDxD,EAAE,CACA,SAAS,EACT;IAAEG,WAAW,EAAE;EAA2C,CAAC,EAC3D,CACEJ,GAAG,CAACgD,EAAE,CAAC,CAAC,CAAC,EACT/C,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAA8B,CAAC,EAAE,CACxDH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAA2B,CAAC,EAAE,CACrDH,EAAE,CAAC,OAAO,EAAE;IAAEG,WAAW,EAAE;EAA0B,CAAC,EAAE,CACtDH,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC0B,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC3B1B,GAAG,CAACU,EAAE,CAACV,GAAG,CAACyF,cAAc,EAAE,UAAUvC,GAAG,EAAE;IACxC,OAAOjD,EAAE,CAAC,IAAI,EAAE;MAAEa,GAAG,EAAEoC,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCpD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACE,IAAI,CAAC,CAAC,CACzB,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CAAC,EACFnD,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC0B,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACxB1B,GAAG,CAACU,EAAE,CAACV,GAAG,CAACyF,cAAc,EAAE,UAAUvC,GAAG,EAAE;IACxC,OAAOjD,EAAE,CAAC,IAAI,EAAE;MAAEa,GAAG,EAAEoC,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCpD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACwC,YAAY,CAAC,CAAC,CACjC,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACDzF,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC0B,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5B1B,GAAG,CAACU,EAAE,CAACV,GAAG,CAACyF,cAAc,EAAE,UAAUvC,GAAG,EAAE;IACxC,OAAOjD,EAAE,CAAC,IAAI,EAAE;MAAEa,GAAG,EAAEoC,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCpD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAACyC,eAAe,CAAC,CAAC,CACpC,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACD1F,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC0B,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5B1B,GAAG,CAACU,EAAE,CAACV,GAAG,CAACyF,cAAc,EAAE,UAAUvC,GAAG,EAAE;IACxC,OAAOjD,EAAE,CAAC,IAAI,EAAE;MAAEa,GAAG,EAAEoC,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCpD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAAC0C,eAAe,CAAC,CAAC,CACpC,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACD3F,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC0B,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACxB1B,GAAG,CAACU,EAAE,CAACV,GAAG,CAACyF,cAAc,EAAE,UAAUvC,GAAG,EAAE;IACxC,OAAOjD,EAAE,CAAC,IAAI,EAAE;MAAEa,GAAG,EAAEoC,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCpD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAAC2C,MAAM,CAAC,CAAC,CAC3B,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACD5F,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1B1B,GAAG,CAACU,EAAE,CAACV,GAAG,CAACyF,cAAc,EAAE,UAAUvC,GAAG,EAAE;IACxC,OAAOjD,EAAE,CAAC,IAAI,EAAE;MAAEa,GAAG,EAAEoC,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCpD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAAC4C,UAAU,CAAC,CAAC,CAC/B,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACD7F,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC0B,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACxB1B,GAAG,CAACU,EAAE,CAACV,GAAG,CAACyF,cAAc,EAAE,UAAUvC,GAAG,EAAE;IACxC,OAAOjD,EAAE,CAAC,IAAI,EAAE;MAAEa,GAAG,EAAEoC,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCpD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC0B,GAAG,CAAC6C,SAAS,CAAC,CAAC,CAC9B,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CACF,EACD9F,EAAE,CACA,SAAS,EACT;IAAEG,WAAW,EAAE;EAAyC,CAAC,EACzD,CACEJ,GAAG,CAACgD,EAAE,CAAC,CAAC,CAAC,EACT/C,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAuB,CAAC,EACvCJ,GAAG,CAACU,EAAE,CAACV,GAAG,CAAC0D,WAAW,EAAE,UAAUC,OAAO,EAAE9C,KAAK,EAAE;IAChD,OAAOZ,EAAE,CACP,KAAK,EACL;MAAEa,GAAG,EAAED,KAAK;MAAET,WAAW,EAAE;IAAsB,CAAC,EAClD,CACEH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAsB,CAAC,EAAE,CAChDH,EAAE,CAAC,GAAG,EAAE;MAAEkB,KAAK,EAAEwC,OAAO,CAACC;IAAK,CAAC,CAAC,CACjC,CAAC,EACF3D,EAAE,CAAC,IAAI,EAAE;MAAEG,WAAW,EAAE;IAAuB,CAAC,EAAE,CAChDJ,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACmC,OAAO,CAAClC,KAAK,CAAC,CAAC,CAC9B,CAAC,EACFxB,EAAE,CAAC,GAAG,EAAE;MAAEG,WAAW,EAAE;IAAsB,CAAC,EAAE,CAC9CJ,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACmC,OAAO,CAACE,IAAI,CAAC,CAAC,CAC7B,CAAC,CACH,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CACF,EACD5D,EAAE,CACA,SAAS,EACT;IAAEG,WAAW,EAAE;EAA6C,CAAC,EAC7D,CACEJ,GAAG,CAACgD,EAAE,CAAC,CAAC,CAAC,EACT/C,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAA2B,CAAC,EAC3CJ,GAAG,CAACU,EAAE,CAACV,GAAG,CAACgG,kBAAkB,EAAE,UAAU5B,GAAG,EAAEvD,KAAK,EAAE;IACnD,OAAOZ,EAAE,CACP,KAAK,EACL;MAAEa,GAAG,EAAED,KAAK;MAAET,WAAW,EAAE;IAAkB,CAAC,EAC9C,CACEH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7CH,EAAE,CAAC,KAAK,EAAE;MACRc,KAAK,EAAE;QAAEC,GAAG,EAAEoD,GAAG,CAACF,KAAK;QAAEhD,GAAG,EAAEkD,GAAG,CAAC3C;MAAM;IAC1C,CAAC,CAAC,CACH,CAAC,EACFxB,EAAE,CAAC,IAAI,EAAE;MAAEG,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC5CJ,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAAC4C,GAAG,CAAC3C,KAAK,CAAC,CAAC,CAC1B,CAAC,CACH,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CACF,EACDxB,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDH,EAAE,CAAC,IAAI,EAAE;IAAEG,WAAW,EAAE;EAAuB,CAAC,EAAE,CAChDJ,GAAG,CAAC0B,EAAE,CAAC,oBAAoB,CAAC,CAC7B,CAAC,EACFzB,EAAE,CACA,QAAQ,EACR;IACEG,WAAW,EAAE,uBAAuB;IACpC2B,EAAE,EAAE;MAAEC,KAAK,EAAEhC,GAAG,CAACwE;IAAiB;EACpC,CAAC,EACD,CAACxE,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,CAAC,EACFzB,EAAE,CAAC,YAAY,EAAE;IAAEc,KAAK,EAAE;MAAEqC,IAAI,EAAE;IAAc;EAAE,CAAC,EAAE,CACnDpD,GAAG,CAACyE,gBAAgB,GAChBxE,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,wBAAwB;IACrC2B,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,IAAIA,MAAM,CAACyC,MAAM,KAAKzC,MAAM,CAAC0C,aAAa,EACxC,OAAO,IAAI;QACb,OAAO3E,GAAG,CAAC4E,iBAAiB,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACrD;IACF;EACF,CAAC,EACD,CACE7E,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDH,EAAE,CACA,QAAQ,EACR;IACEG,WAAW,EAAE,oBAAoB;IACjC2B,EAAE,EAAE;MAAEC,KAAK,EAAEhC,GAAG,CAAC4E;IAAkB;EACrC,CAAC,EACD,CAAC5E,GAAG,CAAC0B,EAAE,CAAC,KAAK,CAAC,CAAC,CAChB,EACDzB,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDH,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAAC+E,WAAW,CAAC3B,IAAI,CAAC,CAAC,CACrC,CAAC,CACH,CAAC,EACFnD,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDH,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAAgB,CAAC,CAAC,EACzCH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAAC+E,WAAW,CAACC,KAAK,CAAC,CAAC,CACtC,CAAC,CACH,CAAC,CACH,CAAC,EACF/E,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDH,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAAC0B,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAC9B,CAAC,CACH,CAAC,CACH,CACF,GACD1B,GAAG,CAACqC,EAAE,EAAE,CACb,CAAC,CACH,EACD,CAAC,CACF,EACL,CAACrC,GAAG,CAACG,QAAQ,GAAGF,EAAE,CAAC,OAAO,CAAC,GAAGD,GAAG,CAACqC,EAAE,EAAE,EACtCpC,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,CACb,EACD,CAAC,CACF;AACH,CAAC;AACD,IAAIgG,eAAe,GAAG,CACpB,YAAY;EACV,IAAIjG,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDH,EAAE,CAAC,IAAI,EAAE;IAAEG,WAAW,EAAE;EAAgB,CAAC,EAAE,CAACJ,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5DzB,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAAsB,CAAC,EAAE,CAC9CJ,GAAG,CAAC0B,EAAE,CACJ,yCAAyC,CAC1C,CACF,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAI1B,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDH,EAAE,CAAC,IAAI,EAAE;IAAEG,WAAW,EAAE;EAAgB,CAAC,EAAE,CAACJ,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5DzB,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAAsB,CAAC,EAAE,CAC9CJ,GAAG,CAAC0B,EAAE,CAAC,4BAA4B,CAAC,CACrC,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAI1B,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDH,EAAE,CAAC,IAAI,EAAE;IAAEG,WAAW,EAAE;EAAgB,CAAC,EAAE,CAACJ,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5DzB,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAAsB,CAAC,EAAE,CAC9CJ,GAAG,CAAC0B,EAAE,CAAC,gBAAgB,CAAC,CACzB,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAI1B,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAwB,CAAC,EAAE,CACzDH,EAAE,CAAC,IAAI,EAAE;IAAEG,WAAW,EAAE;EAAuB,CAAC,EAAE,CAACJ,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnEzB,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAA6B,CAAC,EAAE,CACrDJ,GAAG,CAAC0B,EAAE,CAAC,2BAA2B,CAAC,CACpC,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAI1B,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAwB,CAAC,EAAE,CACzDH,EAAE,CAAC,IAAI,EAAE;IAAEG,WAAW,EAAE;EAAuB,CAAC,EAAE,CAChDJ,GAAG,CAAC0B,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,EACFzB,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAA6B,CAAC,EAAE,CACrDJ,GAAG,CAAC0B,EAAE,CAAC,eAAe,CAAC,CACxB,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAI1B,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAwB,CAAC,EAAE,CACzDH,EAAE,CAAC,IAAI,EAAE;IAAEG,WAAW,EAAE;EAAuB,CAAC,EAAE,CAACJ,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnEzB,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAA6B,CAAC,EAAE,CACrDJ,GAAG,CAAC0B,EAAE,CAAC,2BAA2B,CAAC,CACpC,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAI1B,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAwB,CAAC,EAAE,CACzDH,EAAE,CAAC,IAAI,EAAE;IAAEG,WAAW,EAAE;EAAuB,CAAC,EAAE,CAACJ,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnEzB,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAA6B,CAAC,EAAE,CACrDJ,GAAG,CAAC0B,EAAE,CAAC,gBAAgB,CAAC,CACzB,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACD3B,MAAM,CAACmG,aAAa,GAAG,IAAI;AAE3B,SAASnG,MAAM,EAAEkG,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}