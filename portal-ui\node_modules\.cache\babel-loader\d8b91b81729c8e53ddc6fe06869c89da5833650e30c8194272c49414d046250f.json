{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", {\n    staticClass: \"login-left-side\"\n  }, [_c(\"div\", {\n    staticClass: \"logo-container\"\n  }, [_c(\"a\", {\n    staticClass: \"logo-link\",\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/index\");\n      }\n    }\n  }, [_c(\"img\", {\n    staticClass: \"logo\",\n    attrs: {\n      src: require(\"../../assets/logo_tiangong.png\"),\n      alt: \"算力租赁\"\n    }\n  })])]), _vm._m(0), _c(\"div\", {\n    staticClass: \"visual-element\"\n  }, [_c(\"div\", {\n    staticClass: \"server-illustration\"\n  }, _vm._l(_vm.servers, function (server, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"server-unit\",\n      style: {\n        animationDelay: `${index * 0.2}s`,\n        transform: `translateY(${index * 4}px)`\n      }\n    }, [_c(\"div\", {\n      staticClass: \"server-light\"\n    })]);\n  }), 0), _c(\"div\", {\n    staticClass: \"connections\"\n  })]), _c(\"div\", {\n    staticClass: \"features\"\n  }, _vm._l(_vm.features, function (feature, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"feature-item\"\n    }, [_c(\"div\", {\n      staticClass: \"feature-text\"\n    }, [_c(\"h3\", [_vm._v(_vm._s(feature.title))]), _c(\"p\", [_vm._v(_vm._s(feature.description))])])]);\n  }), 0), _c(\"div\", {\n    staticClass: \"background-elements\"\n  }, _vm._l(20, function (i) {\n    return _c(\"div\", {\n      key: i,\n      staticClass: \"floating-particle\",\n      style: {\n        left: `${Math.random() * 100}%`,\n        top: `${Math.random() * 100}%`,\n        animationDuration: `${3 + Math.random() * 10}s`,\n        animationDelay: `${Math.random() * 5}s`\n      }\n    });\n  }), 0)]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", {\n    staticClass: \"bottom-text\"\n  }, [_c(\"h2\", {\n    staticClass: \"slogan\"\n  }, [_vm._v(\"高效算力 · 智慧未来\")]), _c(\"p\", {\n    staticClass: \"sub-slogan\"\n  }, [_vm._v(\"专业算力租赁服务，为您的业务提供强大支持\")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "_setup", "_setupProxy", "staticClass", "on", "click", "$event", "navigateTo", "attrs", "src", "require", "alt", "_m", "_l", "servers", "server", "index", "key", "style", "animationDelay", "transform", "features", "feature", "_v", "_s", "title", "description", "i", "left", "Math", "random", "top", "animationDuration", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/views/Login/backgroundlogin.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _c(\"div\", { staticClass: \"login-left-side\" }, [\n    _c(\"div\", { staticClass: \"logo-container\" }, [\n      _c(\n        \"a\",\n        {\n          staticClass: \"logo-link\",\n          on: {\n            click: function ($event) {\n              return _vm.navigateTo(\"/index\")\n            },\n          },\n        },\n        [\n          _c(\"img\", {\n            staticClass: \"logo\",\n            attrs: {\n              src: require(\"../../assets/logo_tiangong.png\"),\n              alt: \"算力租赁\",\n            },\n          }),\n        ]\n      ),\n    ]),\n    _vm._m(0),\n    _c(\"div\", { staticClass: \"visual-element\" }, [\n      _c(\n        \"div\",\n        { staticClass: \"server-illustration\" },\n        _vm._l(_vm.servers, function (server, index) {\n          return _c(\n            \"div\",\n            {\n              key: index,\n              staticClass: \"server-unit\",\n              style: {\n                animationDelay: `${index * 0.2}s`,\n                transform: `translateY(${index * 4}px)`,\n              },\n            },\n            [_c(\"div\", { staticClass: \"server-light\" })]\n          )\n        }),\n        0\n      ),\n      _c(\"div\", { staticClass: \"connections\" }),\n    ]),\n    _c(\n      \"div\",\n      { staticClass: \"features\" },\n      _vm._l(_vm.features, function (feature, index) {\n        return _c(\"div\", { key: index, staticClass: \"feature-item\" }, [\n          _c(\"div\", { staticClass: \"feature-text\" }, [\n            _c(\"h3\", [_vm._v(_vm._s(feature.title))]),\n            _c(\"p\", [_vm._v(_vm._s(feature.description))]),\n          ]),\n        ])\n      }),\n      0\n    ),\n    _c(\n      \"div\",\n      { staticClass: \"background-elements\" },\n      _vm._l(20, function (i) {\n        return _c(\"div\", {\n          key: i,\n          staticClass: \"floating-particle\",\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`,\n            animationDuration: `${3 + Math.random() * 10}s`,\n            animationDelay: `${Math.random() * 5}s`,\n          },\n        })\n      }),\n      0\n    ),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c,\n      _setup = _vm._self._setupProxy\n    return _c(\"div\", { staticClass: \"bottom-text\" }, [\n      _c(\"h2\", { staticClass: \"slogan\" }, [_vm._v(\"高效算力 · 智慧未来\")]),\n      _c(\"p\", { staticClass: \"sub-slogan\" }, [\n        _vm._v(\"专业算力租赁服务，为您的业务提供强大支持\"),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAkB,CAAC,EAAE,CACnDJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CJ,EAAE,CACA,GAAG,EACH;IACEI,WAAW,EAAE,WAAW;IACxBC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOR,GAAG,CAACS,UAAU,CAAC,QAAQ,CAAC;MACjC;IACF;EACF,CAAC,EACD,CACER,EAAE,CAAC,KAAK,EAAE;IACRI,WAAW,EAAE,MAAM;IACnBK,KAAK,EAAE;MACLC,GAAG,EAAEC,OAAO,CAAC,gCAAgC,CAAC;MAC9CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CACF,CACF,CAAC,EACFb,GAAG,CAACc,EAAE,CAAC,CAAC,CAAC,EACTb,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CJ,EAAE,CACA,KAAK,EACL;IAAEI,WAAW,EAAE;EAAsB,CAAC,EACtCL,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,OAAO,EAAE,UAAUC,MAAM,EAAEC,KAAK,EAAE;IAC3C,OAAOjB,EAAE,CACP,KAAK,EACL;MACEkB,GAAG,EAAED,KAAK;MACVb,WAAW,EAAE,aAAa;MAC1Be,KAAK,EAAE;QACLC,cAAc,EAAG,GAAEH,KAAK,GAAG,GAAI,GAAE;QACjCI,SAAS,EAAG,cAAaJ,KAAK,GAAG,CAAE;MACrC;IACF,CAAC,EACD,CAACjB,EAAE,CAAC,KAAK,EAAE;MAAEI,WAAW,EAAE;IAAe,CAAC,CAAC,CAAC,CAC7C;EACH,CAAC,CAAC,EACF,CAAC,CACF,EACDJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAc,CAAC,CAAC,CAC1C,CAAC,EACFJ,EAAE,CACA,KAAK,EACL;IAAEI,WAAW,EAAE;EAAW,CAAC,EAC3BL,GAAG,CAACe,EAAE,CAACf,GAAG,CAACuB,QAAQ,EAAE,UAAUC,OAAO,EAAEN,KAAK,EAAE;IAC7C,OAAOjB,EAAE,CAAC,KAAK,EAAE;MAAEkB,GAAG,EAAED,KAAK;MAAEb,WAAW,EAAE;IAAe,CAAC,EAAE,CAC5DJ,EAAE,CAAC,KAAK,EAAE;MAAEI,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC0B,EAAE,CAACF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,EACzC1B,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC0B,EAAE,CAACF,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,CAAC,CAC/C,CAAC,CACH,CAAC;EACJ,CAAC,CAAC,EACF,CAAC,CACF,EACD3B,EAAE,CACA,KAAK,EACL;IAAEI,WAAW,EAAE;EAAsB,CAAC,EACtCL,GAAG,CAACe,EAAE,CAAC,EAAE,EAAE,UAAUc,CAAC,EAAE;IACtB,OAAO5B,EAAE,CAAC,KAAK,EAAE;MACfkB,GAAG,EAAEU,CAAC;MACNxB,WAAW,EAAE,mBAAmB;MAChCe,KAAK,EAAE;QACLU,IAAI,EAAG,GAAEC,IAAI,CAACC,MAAM,EAAE,GAAG,GAAI,GAAE;QAC/BC,GAAG,EAAG,GAAEF,IAAI,CAACC,MAAM,EAAE,GAAG,GAAI,GAAE;QAC9BE,iBAAiB,EAAG,GAAE,CAAC,GAAGH,IAAI,CAACC,MAAM,EAAE,GAAG,EAAG,GAAE;QAC/CX,cAAc,EAAG,GAAEU,IAAI,CAACC,MAAM,EAAE,GAAG,CAAE;MACvC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CAAC,CACF,CACF,CAAC;AACJ,CAAC;AACD,IAAIG,eAAe,GAAG,CACpB,YAAY;EACV,IAAInC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAc,CAAC,EAAE,CAC/CJ,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CAACL,GAAG,CAACyB,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAC5DxB,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAa,CAAC,EAAE,CACrCL,GAAG,CAACyB,EAAE,CAAC,sBAAsB,CAAC,CAC/B,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACD1B,MAAM,CAACqC,aAAa,GAAG,IAAI;AAE3B,SAASrC,MAAM,EAAEoC,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}