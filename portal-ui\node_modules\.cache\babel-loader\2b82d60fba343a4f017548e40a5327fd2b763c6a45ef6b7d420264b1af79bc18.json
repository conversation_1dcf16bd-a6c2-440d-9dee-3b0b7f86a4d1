{"ast": null, "code": "export default {\n  name: 'OrderDetail',\n  props: {\n    visible: {\n      type: Boolean,\n      default: true\n    },\n    server: {\n      type: Object,\n      default: () => ({})\n    },\n    selectedBillingMethod: {\n      type: String\n    }\n  },\n  data() {\n    return {\n      defaultDiskSize: 50,\n      selectedBillingMethod: 'priceDay',\n      selectedDuration: 1,\n      needExtraDisk: false,\n      showConfirmation: false,\n      billingOptions: [{\n        label: '按量计费',\n        value: 'priceHour',\n        unit: '/小时'\n      }, {\n        label: '包日',\n        value: 'priceDay',\n        unit: '/日'\n      }, {\n        label: '包月',\n        value: 'priceMouth',\n        unit: '/月'\n      }, {\n        label: '包年',\n        value: 'priceYear',\n        unit: '/年'\n      }],\n      durationOptionsHour: [{\n        label: '1小时',\n        value: 1\n      }, {\n        label: '2小时',\n        value: 2\n      }, {\n        label: '4小时',\n        value: 4\n      }, {\n        label: '8小时',\n        value: 8\n      }, {\n        label: '12小时',\n        value: 12\n      }, {\n        label: '24小时',\n        value: 24\n      }],\n      durationOptionsDay: [{\n        label: '1天',\n        value: 1\n      }, {\n        label: '2天',\n        value: 2\n      }, {\n        label: '3天',\n        value: 3\n      }, {\n        label: '4天',\n        value: 4\n      }, {\n        label: '5天',\n        value: 5\n      }, {\n        label: '6天',\n        value: 6\n      }],\n      durationOptionsWeek: [{\n        label: '1月',\n        value: 1\n      }, {\n        label: '2月',\n        value: 2\n      }, {\n        label: '3月',\n        value: 3\n      }, {\n        label: '4月',\n        value: 4\n      }, {\n        label: '5月',\n        value: 5\n      }, {\n        label: '6月',\n        value: 6\n      }, {\n        label: '7月',\n        value: 7\n      }, {\n        label: '8月',\n        value: 8\n      }],\n      durationOptionsMonth: [{\n        label: '1年',\n        value: 1\n      }, {\n        label: '2年',\n        value: 2\n      }, {\n        label: '3年',\n        value: 3\n      }]\n    };\n  },\n  computed: {\n    currentDurationOptions() {\n      switch (this.selectedBillingMethod) {\n        case 'priceHour':\n          this.totalPrice = this.server['priceHour'];\n          return this.durationOptionsHour;\n        case 'priceDay':\n          this.totalPrice = this.server['priceDay'];\n          return this.durationOptionsDay;\n        case 'priceMouth':\n          this.totalPrice = this.server['priceMouth'];\n          return this.durationOptionsWeek;\n        case 'priceYear':\n          this.totalPrice = this.server['priceYear'];\n          return this.durationOptionsMonth;\n        default:\n          return this.durationOptionsDay;\n      }\n    },\n    totalPrice() {\n      const price = this.server[this.selectedBillingMethod] || 0;\n      return (price * this.selectedDuration).toFixed(2);\n    },\n    priceUnit() {\n      switch (this.selectedBillingMethod) {\n        case 'priceHour':\n          return '/小时';\n        case 'priceDay':\n          return '/日';\n        case 'priceMouth':\n          return '/月';\n        case 'priceYear':\n          return '/年';\n        default:\n          return '';\n      }\n    },\n    totalTime() {\n      switch (this.selectedBillingMethod) {\n        case 'priceHour':\n          return '小时';\n        case 'priceDay':\n          return '天';\n        case 'priceMouth':\n          return '月';\n        case 'priceYear':\n          return '年';\n        default:\n          return '小时';\n      }\n    }\n  },\n  watch: {\n    selectedBillingMethod() {\n      this.selectedDuration = this.currentDurationOptions[0]?.value || 1;\n    },\n    selectedDuration() {\n      this.totalPrice = this.server[this.selectedBillingMethod] * this.selectedDuration;\n    },\n    selectedDuration: {\n      handler(newval) {\n        this.$emit('time-updated', newval + this.totalTime);\n      },\n      immediate: true\n    },\n    totalPrice: {\n      handler(newval) {\n        this.$emit('price-updated', newval);\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    closeModal() {\n      this.$emit('close');\n    },\n    showConfirmDialog() {\n      this.showConfirmation = true;\n    },\n    confirmOrder() {\n      this.showConfirmation = false;\n      this.$emit(\"orderSubmitted\");\n      const order = {\n        serverId: this.server.id,\n        serverName: this.server.name,\n        billingMethod: this.selectedBillingMethod,\n        duration: this.selectedDuration,\n        needExtraDisk: this.needExtraDisk,\n        price: this.totalPrice,\n        specs: {\n          gpuModel: this.server.name,\n          vcpu: this.server.vcpu,\n          systemDisk: this.server.systemDisk,\n          cloudDisk: this.server.cloudDisk || 0,\n          memory: this.server.memory,\n          videoMemory: this.server.videoMemory\n        }\n      };\n      this.$emit('order-success', order);\n      this.closeModal();\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "visible", "type", "Boolean", "default", "server", "Object", "selectedBillingMethod", "String", "data", "defaultDiskSize", "selectedDuration", "needExtraDisk", "showConfirmation", "billingOptions", "label", "value", "unit", "durationOptionsHour", "durationOptionsDay", "durationOptionsWeek", "durationOptionsMonth", "computed", "currentDurationOptions", "totalPrice", "price", "toFixed", "priceUnit", "totalTime", "watch", "handler", "newval", "$emit", "immediate", "methods", "closeModal", "showConfirmDialog", "confirmOrder", "order", "serverId", "id", "serverName", "billingMethod", "duration", "specs", "gpuModel", "vcpu", "systemDisk", "cloudDisk", "memory", "videoMemory"], "sources": ["src/views/Product/OrderDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"modal-overlay\" v-if=\"visible\">\r\n    <div class=\"modal-container\">\r\n      <div class=\"modal-header1\">\r\n        <h2>订单确认</h2>\r\n      </div>\r\n\r\n      <div class=\"modal-body\">\r\n        <!-- 计费方式 -->\r\n        <div class=\"section-title\">计费方式</div>\r\n        <div class=\"billing-tabs\">\r\n          <div\r\n              v-for=\"(option, index) in billingOptions\"\r\n              :key=\"index\"\r\n              class=\"billing-tab\"\r\n              :class=\"{ 'active': selectedBillingMethod === option.value }\"\r\n              @click=\"selectedBillingMethod = option.value\"\r\n          >\r\n            {{ option.label }}\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"section-title\">选择主机</div>\r\n        <div class=\"specs-example-table\">\r\n          <div class=\"specs-example-row\">\r\n            <div class=\"specs-example-cell\">GPU型号</div>\r\n            <div class=\"specs-example-cell\">GPU</div>\r\n            <div class=\"specs-example-cell\">显存</div>\r\n            <div class=\"specs-example-cell\">vCPU</div>\r\n            <div class=\"specs-example-cell\">内存</div>\r\n            <div class=\"specs-example-cell\">系统盘</div>\r\n          </div>\r\n          <div class=\"specs-example-row\">\r\n            <div class=\"specs-example-cell\">{{ server.name }}</div>\r\n            <div class=\"specs-example-cell\">{{ server.graphicsCardNumber }}卡</div>\r\n            <div class=\"specs-example-cell\">{{ server.videoMemory }}GB</div>\r\n            <div class=\"specs-example-cell\">{{ server.gpuNuclearNumber }}核</div>\r\n            <div class=\"specs-example-cell\">{{ server.internalMemory }}GB</div>\r\n            <div class=\"specs-example-cell\">{{ server.systemDisk }}GB</div>\r\n          </div>\r\n          <div class=\"server-card-footer\">\r\n            <div class=\"server-price\">¥ {{ server[selectedBillingMethod] }}<span class=\"spec-label\"> {{ priceUnit }}</span></div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 服务器配置规格展示 -->\r\n        <div class=\"section-title\">实例规格</div>\r\n        <div class=\"specs-example-table\">\r\n          <div class=\"specs-example-row\">\r\n            <div class=\"specs-example-cell\">GPU型号</div>\r\n            <div class=\"specs-example-cell\">vCPU</div>\r\n            <div class=\"specs-example-cell\">内存</div>\r\n            <div class=\"specs-example-cell\">系统盘</div>\r\n            <div class=\"specs-example-cell\">数据盘</div>\r\n          </div>\r\n          <div class=\"specs-example-row\">\r\n            <div class=\"specs-example-cell\">{{ server.name }}</div>\r\n            <div class=\"specs-example-cell\">{{ server.gpuNuclearNumber }}核心</div>\r\n            <div class=\"specs-example-cell\">{{ server.internalMemory }}GB</div>\r\n            <div class=\"specs-example-cell\">{{ server.systemDisk }}GB</div>\r\n            <div class=\"specs-example-cell\">免费{{ server.dataDisk }}GB SSD</div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 租用时长 -->\r\n        <div class=\"rental-duration\">\r\n          <div class=\"duration-label\">租用时长：</div>\r\n          <div class=\"duration-selector\">\r\n            <select v-model=\"selectedDuration\"  class=\"duration-select\">\r\n              <option\r\n                  v-for=\"(option, index) in currentDurationOptions\"\r\n                  :key=\"index\"\r\n                  :value=\"option.value\"\r\n              >\r\n                {{ option.label }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n          <div class=\"duration-hint\" v-if=\"selectedBillingMethod === 'priceHour'\"></div>\r\n          <div class=\"duration-hint\" v-else-if=\"selectedBillingMethod === 'priceDay'\"></div>\r\n          <div class=\"duration-hint\" v-else-if=\"selectedBillingMethod === 'priceWeek'\"></div>\r\n          <div class=\"duration-hint\" v-else></div>\r\n        </div>\r\n\r\n        <!-- 配置费用 -->\r\n        <div class=\"price-summary\">\r\n          <div class=\"price-label\">配置费用：</div>\r\n          <div class=\"price-value\">¥ {{ totalPrice }} 元 </div>\r\n          <div class=\"details-link\" @click=\"showPriceDetails = true\">费用明细</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"modal-footer\">\r\n        <button class=\"cancel-button\" @click=\"closeModal\">取消</button>\r\n        <button\r\n            class=\"confirm-button\"\r\n            @click=\"showConfirmDialog\"\r\n        >\r\n          立即租赁\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'OrderDetail',\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    server: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    selectedBillingMethod: {\r\n      type:String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      defaultDiskSize: 50,\r\n      selectedBillingMethod: 'priceDay',\r\n      selectedDuration: 1,\r\n      needExtraDisk: false,\r\n      showPriceDetails: false,\r\n      showConfirmation: false, // 控制二次确认弹窗显示\r\n      billingOptions: [\r\n        { label: '按量计费', value: 'priceHour', unit: '/小时' },\r\n        { label: '包日', value: 'priceDay', unit: '/日' },\r\n        { label: '包月', value: 'priceMouth', unit: '/月' },\r\n        { label: '包年', value: 'priceYear', unit: '/年' }\r\n      ],\r\n      durationOptionsHour: [\r\n        { label: '1小时', value: 1 },\r\n        { label: '2小时', value: 2 },\r\n        { label: '4小时', value: 4 },\r\n        { label: '8小时', value: 8 },\r\n        { label: '12小时', value: 12 },\r\n        { label: '24小时', value: 24 }\r\n      ],\r\n      durationOptionsDay: [\r\n        { label: '1天', value: 1 },\r\n        { label: '2天', value: 2 },\r\n        { label: '3天', value: 3 },\r\n        { label: '4天', value: 4 },\r\n        { label: '5天', value: 5 },\r\n        { label: '6天', value: 6 }\r\n      ],\r\n      durationOptionsWeek: [\r\n        { label: '1月', value: 1 },\r\n        { label: '2月', value: 2 },\r\n        { label: '3月', value: 3 },\r\n        { label: '4月', value: 4 },\r\n        { label: '5月', value: 5 },\r\n        { label: '6月', value: 6 },\r\n        { label: '7月', value: 7 },\r\n        { label: '8月', value: 8 }\r\n      ],\r\n      durationOptionsMonth: [\r\n        { label: '1年', value: 1 },\r\n        { label: '2年', value: 2 },\r\n        { label: '3年', value: 3 }\r\n      ]\r\n    };\r\n  },\r\n  created() {\r\n  },\r\n  computed: {\r\n    currentDurationOptions() {\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour':\r\n          this.totalPrice = this.server['priceHour']\r\n          return this.durationOptionsHour;\r\n        case 'priceDay':\r\n          this.totalPrice = this.server['priceDay']\r\n          return this.durationOptionsDay;\r\n        case 'priceMouth':\r\n          this.totalPrice = this.server['priceMouth']\r\n          return this.durationOptionsWeek;\r\n        case 'priceYear':\r\n          this.totalPrice = this.server['priceYear']\r\n          return this.durationOptionsMonth;\r\n        default:\r\n          return this.durationOptionsDay;\r\n      }\r\n    },\r\n    totalPrice() {\r\n      const price = this.server[this.selectedBillingMethod] || 0;\r\n      return (price * this.selectedDuration).toFixed(2);\r\n    },\r\n    priceUnit() {\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour': return '/小时';\r\n        case 'priceDay': return '/日';\r\n        case 'priceMouth': return '/月';\r\n        case 'priceYear': return '/年';\r\n        default: return '';\r\n      }\r\n    },\r\n    totalTime(){\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour':\r\n          return '小时';\r\n        case 'priceDay':\r\n          return '天'\r\n        case 'priceMouth':\r\n          return '月'\r\n        case 'priceYear':\r\n          return '年'\r\n        default:\r\n          return '小时'\r\n      }\r\n    },\r\n  },\r\n  watch: {\r\n    selectedBillingMethod() {\r\n      this.selectedDuration = this.currentDurationOptions[0]?.value || 1;\r\n    },\r\n    selectedDuration(){\r\n      this.totalPrice =this.server[this.selectedBillingMethod] * this.selectedDuration\r\n    },\r\n    selectedDuration:{\r\n      handler(newval){\r\n        this.$emit('time-updated',newval+this.totalTime)\r\n      },\r\n      immediate:true\r\n    },\r\n    totalPrice:{\r\n      handler(newval){\r\n        this.$emit('price-updated',newval);\r\n      },\r\n      immediate:true\r\n    },\r\n  },\r\n  methods: {\r\n    closeModal() {\r\n      this.$emit('close');\r\n    },\r\n    selectServer() {\r\n      // 保留方法，但不需要实际操作，因为只有一个服务器\r\n    },\r\n    showConfirmDialog() {\r\n      this.showConfirmation = true;\r\n    },\r\n    confirmOrder() {\r\n      this.showConfirmation = false;\r\n      this.$emit(\"orderSubmitted\");\r\n\r\n      const order = {\r\n        serverId: this.server.id,\r\n        serverName: this.server.name,\r\n        billingMethod: this.selectedBillingMethod,\r\n        duration: this.selectedDuration,\r\n        needExtraDisk: this.needExtraDisk,\r\n        price: this.totalPrice,\r\n        specs: {\r\n          gpuModel: this.server.name,\r\n          vcpu: this.server.vcpu,\r\n          systemDisk: this.server.systemDisk,\r\n          cloudDisk: this.server.cloudDisk || 0,\r\n          memory: this.server.memory,\r\n          videoMemory: this.server.videoMemory\r\n        }\r\n      };\r\n\r\n      this.$emit('order-success', order);\r\n      this.closeModal();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  width: 1020px;\r\n  max-width: 95%;\r\n  max-height: 90vh;\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.modal-header1 {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 24px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.modal-header1 h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.close-button {\r\n  background: none;\r\n  width: 30px;\r\n  height: 55px;\r\n  border: none;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: #999;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.close-button:hover {\r\n  color: #333;\r\n}\r\n\r\n.modal-body {\r\n  margin-top: -4vh;\r\n  padding: 20px 24px;\r\n  flex-grow: 1;\r\n}\r\n\r\n.section-title {\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-top: 24px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.billing-tabs {\r\n  display: flex;\r\n  background-color: #f7f7f9;\r\n  border-radius: 8px;\r\n  font-size: 1.8vh;\r\n  padding: 4px;\r\n  width:50vh;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.billing-tab {\r\n  flex: 1;\r\n  padding: 12px 16px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-weight: 500;\r\n  color: #666;\r\n  border-radius: 6px;\r\n}\r\n\r\n.billing-tab:hover {\r\n  color: #2196f3;\r\n  background-color: rgba(99, 102, 241, 0.05);\r\n}\r\n\r\n.billing-tab.active {\r\n  color: #fff;\r\n  background-color: #2196f3;\r\n  font-weight: 600;\r\n  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);\r\n}\r\n\r\n.server-card {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.server-card:hover {\r\n  border-color: #2196f3;\r\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.server-card-header {\r\n  padding: 16px;\r\n  background-color: #f9f9f9;\r\n  display: flex;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.server-radio {\r\n  margin-right: 12px;\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #2196f3;\r\n}\r\n\r\n.server-name {\r\n  font-weight: 600;\r\n  font-size: 15px;\r\n  color: #333;\r\n}\r\n\r\n.server-card-body {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  background-color: #fff;\r\n}\r\n\r\n.server-spec {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1 0 18%;\r\n  min-width: 120px;\r\n}\r\n\r\n.spec-label {\r\n  color: #666;\r\n  margin-right: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.spec-value {\r\n  font-weight: 500;\r\n  color: #333;\r\n  font-size: 14px;\r\n}\r\n\r\n.server-card-footer {\r\n  padding: 12px 16px;\r\n  height: 6vh;\r\n  background-color: #f9f9f9;\r\n  border-top: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.server-price {\r\n  font-weight: 600;\r\n  color: #f43f5e;\r\n  font-size: 16px;\r\n}\r\n\r\n.specs-example {\r\n  background-color: #f9f9f9;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.specs-example-table {\r\n  width: 100%;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #eaeaea;\r\n}\r\n\r\n.specs-example-row {\r\n  display: flex;\r\n}\r\n\r\n.specs-example-row:first-child {\r\n  background-color: #f3f4f6;\r\n  font-weight: 500;\r\n}\r\n\r\n.specs-example-row:last-child {\r\n  background-color: #fff;\r\n}\r\n\r\n.specs-example-cell {\r\n  padding: 10px 12px;\r\n  flex: 1;\r\n  border-right: 1px solid #eaeaea;\r\n  font-size: 13px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n}\r\n\r\n.specs-example-cell:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.rental-duration {\r\n  margin-top: 2vh;\r\n  display: flex;\r\n  margin-left: 2vh;\r\n  align-items: center;\r\n  margin-bottom: -2vh;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.duration-label {\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-right: 12px;\r\n}\r\n\r\n.duration-selector {\r\n  position: relative;\r\n}\r\n\r\n.duration-select {\r\n  padding: 10px 32px 10px 12px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 6px;\r\n  appearance: none;\r\n  background-color: white;\r\n  font-size: 14px;\r\n  color: #333;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  min-width: 120px;\r\n}\r\n\r\n.duration-select:hover {\r\n  border-color: #2196f3;\r\n}\r\n\r\n.duration-select:focus {\r\n  outline: none;\r\n  border-color: #2196f3;\r\n  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);\r\n}\r\n\r\n.duration-selector::after {\r\n  content: \"▼\";\r\n  position: absolute;\r\n  right: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #666;\r\n  pointer-events: none;\r\n  font-size: 10px;\r\n}\r\n\r\n.duration-hint {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-left: 8px;\r\n}\r\n\r\n.price-summary {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 24px;\r\n  padding: 16px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 8px;\r\n}\r\n\r\n.price-label {\r\n  font-weight: 500;\r\n  color: #333;\r\n  height: 4vh;\r\n  margin-right: 8px;\r\n}\r\n\r\n.price-value {\r\n  font-weight: 700;\r\n  color: #f43f5e;\r\n  font-size: 18px;\r\n  margin-right: auto;\r\n}\r\n\r\n.details-link {\r\n  color: #2196f3;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  text-decoration: underline;\r\n}\r\n\r\n.details-link:hover {\r\n  color: #2196f3;\r\n}\r\n\r\n.modal-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 16px 24px;\r\n  border-top: 1px solid #f0f0f0;\r\n  gap: 12px;\r\n}\r\n\r\n.cancel-button {\r\n  padding: 10px 20px;\r\n  border: 1px solid #ddd;\r\n  background-color: white;\r\n  color: #666;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.cancel-button:hover {\r\n  background-color: #f3f4f6;\r\n  border-color: #ccc;\r\n}\r\n\r\n.confirm-button {\r\n  padding: 10px 24px;\r\n  border: none;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s;\r\n  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);\r\n}\r\n\r\n.confirm-button:hover {\r\n  background-color: #2196f3;\r\n  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);\r\n}\r\n\r\n/* 二次确认弹窗样式 */\r\n.confirm-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 2000;\r\n}\r\n\r\n.confirm-dialog {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  width: 400px;\r\n  max-width: 95%;\r\n  overflow: hidden;\r\n}\r\n\r\n.confirm-header {\r\n  padding: 16px 24px;\r\n  background-color: #f7f7f9;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.confirm-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  color: #333;\r\n}\r\n\r\n.confirm-body {\r\n  padding: 20px 24px;\r\n}\r\n\r\n.confirm-body p {\r\n  margin: 0 0 16px 0;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.confirm-details {\r\n  background-color: #f9f9f9;\r\n  padding: 12px;\r\n  border-radius: 6px;\r\n  font-size: 13px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.confirm-details div {\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.confirm-details div:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.confirm-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 16px 24px;\r\n  border-top: 1px solid #eee;\r\n  gap: 12px;\r\n}\r\n\r\n.confirm-cancel {\r\n  padding: 8px 16px;\r\n  border: 1px solid #ddd;\r\n  background-color: white;\r\n  color: #666;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.confirm-cancel:hover {\r\n  background-color: #f3f4f6;\r\n  border-color: #ccc;\r\n}\r\n\r\n.confirm-ok {\r\n  padding: 8px 16px;\r\n  border: none;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.confirm-ok:hover {\r\n  background-color: #2196f3;\r\n}\r\n</style>\r\n\r\n<template>\r\n  <div>\r\n    <div class=\"modal-overlay\" v-if=\"visible\">\r\n      <div class=\"modal-container\">\r\n        <div class=\"modal-header1\">\r\n          <h2>订单确认</h2>\r\n        </div>\r\n\r\n        <div class=\"modal-body\">\r\n          <!-- 计费方式 -->\r\n          <div class=\"section-title\">计费方式</div>\r\n          <div class=\"billing-tabs\">\r\n            <div\r\n                v-for=\"(option, index) in billingOptions\"\r\n                :key=\"index\"\r\n                class=\"billing-tab\"\r\n                :class=\"{ 'active': selectedBillingMethod === option.value }\"\r\n                @click=\"selectedBillingMethod = option.value\"\r\n            >\r\n              {{ option.label }}\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"section-title\">选择主机</div>\r\n          <div class=\"specs-example-table\">\r\n            <div class=\"specs-example-row\">\r\n              <div class=\"specs-example-cell\">GPU型号</div>\r\n              <div class=\"specs-example-cell\">GPU</div>\r\n              <div class=\"specs-example-cell\">显存</div>\r\n              <div class=\"specs-example-cell\">vCPU</div>\r\n              <div class=\"specs-example-cell\">内存</div>\r\n              <div class=\"specs-example-cell\">系统盘</div>\r\n            </div>\r\n            <div class=\"specs-example-row\">\r\n              <div class=\"specs-example-cell\">{{ server.name }}</div>\r\n              <div class=\"specs-example-cell\">{{ server.graphicsCardNumber }}卡</div>\r\n              <div class=\"specs-example-cell\">{{ server.videoMemory }}GB</div>\r\n              <div class=\"specs-example-cell\">{{ server.gpuNuclearNumber }}核</div>\r\n              <div class=\"specs-example-cell\">{{ server.internalMemory }}GB</div>\r\n              <div class=\"specs-example-cell\">{{ server.systemDisk }}GB</div>\r\n            </div>\r\n            <div class=\"server-card-footer\">\r\n              <div class=\"server-price\">¥ {{ server[selectedBillingMethod] }}<span class=\"spec-label\"> {{ priceUnit }}</span></div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 服务器配置规格展示 -->\r\n          <div class=\"section-title\">实例规格</div>\r\n          <div class=\"specs-example-table\">\r\n            <div class=\"specs-example-row\">\r\n              <div class=\"specs-example-cell\">GPU型号</div>\r\n              <div class=\"specs-example-cell\">vCPU</div>\r\n              <div class=\"specs-example-cell\">内存</div>\r\n              <div class=\"specs-example-cell\">系统盘</div>\r\n              <div class=\"specs-example-cell\">数据盘</div>\r\n            </div>\r\n            <div class=\"specs-example-row\">\r\n              <div class=\"specs-example-cell\">{{ server.name }}</div>\r\n              <div class=\"specs-example-cell\">{{ server.gpuNuclearNumber }}核心</div>\r\n              <div class=\"specs-example-cell\">{{ server.internalMemory }}GB</div>\r\n              <div class=\"specs-example-cell\">{{ server.systemDisk }}GB</div>\r\n              <div class=\"specs-example-cell\">免费{{ server.dataDisk }}GB SSD</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 租用时长 -->\r\n          <div class=\"rental-duration\">\r\n            <div class=\"duration-label\">租用时长：</div>\r\n            <div class=\"duration-selector\">\r\n              <select v-model=\"selectedDuration\"  class=\"duration-select\">\r\n                <option\r\n                    v-for=\"(option, index) in currentDurationOptions\"\r\n                    :key=\"index\"\r\n                    :value=\"option.value\"\r\n                >\r\n                  {{ option.label }}\r\n                </option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 配置费用 -->\r\n          <div class=\"price-summary\">\r\n            <div class=\"price-label\">配置费用：</div>\r\n            <div class=\"price-value\">¥ {{ totalPrice }} 元 </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"modal-footer\">\r\n          <button class=\"cancel-button\" @click=\"closeModal\">取消</button>\r\n          <button\r\n              class=\"confirm-button\"\r\n              @click=\"showConfirmDialog\"\r\n          >\r\n            立即租赁\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 简化的二次确认弹窗 -->\r\n    <div class=\"confirm-overlay\" v-if=\"showConfirmation\">\r\n      <div class=\"confirm-dialog\">\r\n        <div class=\"confirm-message\">是否确认订单？</div>\r\n        <div class=\"confirm-footer\">\r\n          <button class=\"confirm-cancel\" @click=\"showConfirmation = false\">取消</button>\r\n          <button class=\"confirm-ok\" @click=\"confirmOrder\">确认</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'OrderDetail',\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    server: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    selectedBillingMethod: {\r\n      type:String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      defaultDiskSize: 50,\r\n      selectedBillingMethod: 'priceDay',\r\n      selectedDuration: 1,\r\n      needExtraDisk: false,\r\n      showConfirmation: false,\r\n      billingOptions: [\r\n        { label: '按量计费', value: 'priceHour', unit: '/小时' },\r\n        { label: '包日', value: 'priceDay', unit: '/日' },\r\n        { label: '包月', value: 'priceMouth', unit: '/月' },\r\n        { label: '包年', value: 'priceYear', unit: '/年' }\r\n      ],\r\n      durationOptionsHour: [\r\n        { label: '1小时', value: 1 },\r\n        { label: '2小时', value: 2 },\r\n        { label: '4小时', value: 4 },\r\n        { label: '8小时', value: 8 },\r\n        { label: '12小时', value: 12 },\r\n        { label: '24小时', value: 24 }\r\n      ],\r\n      durationOptionsDay: [\r\n        { label: '1天', value: 1 },\r\n        { label: '2天', value: 2 },\r\n        { label: '3天', value: 3 },\r\n        { label: '4天', value: 4 },\r\n        { label: '5天', value: 5 },\r\n        { label: '6天', value: 6 }\r\n      ],\r\n      durationOptionsWeek: [\r\n        { label: '1月', value: 1 },\r\n        { label: '2月', value: 2 },\r\n        { label: '3月', value: 3 },\r\n        { label: '4月', value: 4 },\r\n        { label: '5月', value: 5 },\r\n        { label: '6月', value: 6 },\r\n        { label: '7月', value: 7 },\r\n        { label: '8月', value: 8 }\r\n      ],\r\n      durationOptionsMonth: [\r\n        { label: '1年', value: 1 },\r\n        { label: '2年', value: 2 },\r\n        { label: '3年', value: 3 }\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    currentDurationOptions() {\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour':\r\n          this.totalPrice = this.server['priceHour']\r\n          return this.durationOptionsHour;\r\n        case 'priceDay':\r\n          this.totalPrice = this.server['priceDay']\r\n          return this.durationOptionsDay;\r\n        case 'priceMouth':\r\n          this.totalPrice = this.server['priceMouth']\r\n          return this.durationOptionsWeek;\r\n        case 'priceYear':\r\n          this.totalPrice = this.server['priceYear']\r\n          return this.durationOptionsMonth;\r\n        default:\r\n          return this.durationOptionsDay;\r\n      }\r\n    },\r\n    totalPrice() {\r\n      const price = this.server[this.selectedBillingMethod] || 0;\r\n      return (price * this.selectedDuration).toFixed(2);\r\n    },\r\n    priceUnit() {\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour': return '/小时';\r\n        case 'priceDay': return '/日';\r\n        case 'priceMouth': return '/月';\r\n        case 'priceYear': return '/年';\r\n        default: return '';\r\n      }\r\n    },\r\n    totalTime(){\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour':\r\n          return '小时';\r\n        case 'priceDay':\r\n          return '天'\r\n        case 'priceMouth':\r\n          return '月'\r\n        case 'priceYear':\r\n          return '年'\r\n        default:\r\n          return '小时'\r\n      }\r\n    },\r\n  },\r\n  watch: {\r\n    selectedBillingMethod() {\r\n      this.selectedDuration = this.currentDurationOptions[0]?.value || 1;\r\n    },\r\n    selectedDuration(){\r\n      this.totalPrice =this.server[this.selectedBillingMethod] * this.selectedDuration\r\n    },\r\n    selectedDuration:{\r\n      handler(newval){\r\n        this.$emit('time-updated',newval+this.totalTime)\r\n      },\r\n      immediate:true\r\n    },\r\n    totalPrice:{\r\n      handler(newval){\r\n        this.$emit('price-updated',newval);\r\n      },\r\n      immediate:true\r\n    },\r\n  },\r\n  methods: {\r\n    closeModal() {\r\n      this.$emit('close');\r\n    },\r\n    showConfirmDialog() {\r\n      this.showConfirmation = true;\r\n    },\r\n    confirmOrder() {\r\n      this.showConfirmation = false;\r\n      this.$emit(\"orderSubmitted\");\r\n\r\n      const order = {\r\n        serverId: this.server.id,\r\n        serverName: this.server.name,\r\n        billingMethod: this.selectedBillingMethod,\r\n        duration: this.selectedDuration,\r\n        needExtraDisk: this.needExtraDisk,\r\n        price: this.totalPrice,\r\n        specs: {\r\n          gpuModel: this.server.name,\r\n          vcpu: this.server.vcpu,\r\n          systemDisk: this.server.systemDisk,\r\n          cloudDisk: this.server.cloudDisk || 0,\r\n          memory: this.server.memory,\r\n          videoMemory: this.server.videoMemory\r\n        }\r\n      };\r\n\r\n      this.$emit('order-success', order);\r\n      this.closeModal();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.modal-overlay {\r\n  /*margin-left: 200px;*/\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  width: 1020px;\r\n  max-width: 95%;\r\n  max-height: 90vh;\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.modal-header1 {\r\n  display: flex;\r\n  /*height: 5vh;*/\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 24px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.modal-header1 h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.close-button {\r\n  background: none;\r\n  width: 30px;\r\n  height: 55px;\r\n  border: none;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: #999;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.close-button:hover {\r\n  color: #333;\r\n}\r\n\r\n.modal-body {\r\n  margin-top: -4vh;\r\n  padding: 20px 24px;\r\n  flex-grow: 1;\r\n}\r\n\r\n.section-title {\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-top: 24px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n/* 优化的计费方式选项卡样式 */\r\n.billing-tabs {\r\n  display: flex;\r\n  background-color: #f7f7f9;\r\n  border-radius: 8px;\r\n  font-size: 1.8vh;\r\n  padding: 4px;\r\n  width:50vh;\r\n  /*higth:10px;*/\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.billing-tab {\r\n  flex: 1;\r\n  padding: 12px 16px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-weight: 500;\r\n  color: #666;\r\n  border-radius: 6px;\r\n}\r\n\r\n.billing-tab:hover {\r\n  color: #2196f3;\r\n  background-color: rgba(99, 102, 241, 0.05);\r\n}\r\n\r\n.billing-tab.active {\r\n  color: #fff;\r\n  background-color: #2196f3;\r\n  font-weight: 600;\r\n  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);\r\n}\r\n\r\n.billing-description {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-top: 8px;\r\n  margin-bottom: 20px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 服务器卡片样式 */\r\n.server-card {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.server-card:hover {\r\n  border-color: #2196f3;\r\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.server-card-header {\r\n  padding: 16px;\r\n  background-color: #f9f9f9;\r\n  display: flex;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.server-radio {\r\n  margin-right: 12px;\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #2196f3;\r\n}\r\n\r\n.server-name {\r\n  font-weight: 600;\r\n  font-size: 15px;\r\n  color: #333;\r\n}\r\n\r\n.server-card-body {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  background-color: #fff;\r\n}\r\n\r\n.server-spec {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1 0 18%;\r\n  min-width: 120px;\r\n}\r\n\r\n.spec-label {\r\n  color: #666;\r\n  margin-right: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.spec-value {\r\n  font-weight: 500;\r\n  color: #333;\r\n  font-size: 14px;\r\n}\r\n\r\n.server-card-footer {\r\n  padding: 12px 16px;\r\n  height: 6vh;\r\n  background-color: #f9f9f9;\r\n  border-top: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.server-price {\r\n  font-weight: 600;\r\n  color: #f43f5e;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 数据盘选择样式 */\r\n.disk-options {\r\n  display: flex;\r\n  gap: 24px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.disk-option {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.disk-option input[type=\"checkbox\"] {\r\n  margin-right: 8px;\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #2196f3;\r\n}\r\n\r\n/* 规格展示表格样式 */\r\n.specs-example {\r\n  background-color: #f9f9f9;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.specs-example-title {\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n  color: #333;\r\n  font-size: 14px;\r\n}\r\n\r\n.specs-example-table {\r\n  width: 100%;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #eaeaea;\r\n}\r\n\r\n.specs-example-row {\r\n  display: flex;\r\n}\r\n\r\n.specs-example-row:first-child {\r\n  background-color: #f3f4f6;\r\n  font-weight: 500;\r\n}\r\n\r\n.specs-example-row:last-child {\r\n  background-color: #fff;\r\n}\r\n\r\n.specs-example-cell {\r\n  padding: 10px 12px;\r\n  flex: 1;\r\n  border-right: 1px solid #eaeaea;\r\n  font-size: 13px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n}\r\n\r\n.specs-example-cell:last-child {\r\n  border-right: none;\r\n}\r\n\r\n/* 租用时长样式 */\r\n.rental-duration {\r\n  margin-top: 2vh;\r\n  display: flex;\r\n  margin-left: 2vh;\r\n  align-items: center;\r\n  margin-bottom: -2vh;\r\n  /*margin-bottom: 24px;*/\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.duration-label {\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-right: 12px;\r\n}\r\n\r\n.duration-selector {\r\n  position: relative;\r\n}\r\n\r\n.duration-select {\r\n  padding: 10px 32px 10px 12px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 6px;\r\n  appearance: none;\r\n  background-color: white;\r\n  font-size: 14px;\r\n  color: #333;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  min-width: 120px;\r\n}\r\n\r\n.duration-select:hover {\r\n  border-color: #2196f3;\r\n}\r\n\r\n.duration-select:focus {\r\n  outline: none;\r\n  border-color: #2196f3;\r\n  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);\r\n}\r\n\r\n.duration-selector::after {\r\n  content: \"▼\";\r\n  position: absolute;\r\n  right: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #666;\r\n  pointer-events: none;\r\n  font-size: 10px;\r\n}\r\n\r\n.duration-hint {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-left: 8px;\r\n}\r\n\r\n/* 价格总结样式 */\r\n.price-summary {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 24px;\r\n  padding: 16px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 8px;\r\n}\r\n\r\n.price-label {\r\n  font-weight: 500;\r\n  color: #333;\r\n  height: 4vh;\r\n  margin-right: 8px;\r\n}\r\n\r\n.price-value {\r\n  font-weight: 700;\r\n  color: #f43f5e;\r\n  font-size: 18px;\r\n  margin-right: auto;\r\n}\r\n\r\n.details-link {\r\n  color: #2196f3;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  text-decoration: underline;\r\n}\r\n\r\n.details-link:hover {\r\n  color: #2196f3;\r\n}\r\n\r\n/* 底部按钮样式 */\r\n.modal-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 16px 24px;\r\n  border-top: 1px solid #f0f0f0;\r\n  gap: 12px;\r\n}\r\n\r\n.cancel-button {\r\n  padding: 10px 20px;\r\n  border: 1px solid #ddd;\r\n  background-color: white;\r\n  color: #666;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.cancel-button:hover {\r\n  background-color: #f3f4f6;\r\n  border-color: #ccc;\r\n}\r\n\r\n.confirm-button {\r\n  padding: 10px 24px;\r\n  border: none;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s;\r\n  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);\r\n}\r\n\r\n.confirm-button:hover {\r\n  background-color: #2196f3;\r\n  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);\r\n}\r\n\r\n/* 简化后的二次确认弹窗样式 */\r\n.confirm-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 2000;\r\n}\r\n\r\n.confirm-dialog {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  width: 300px;\r\n  padding: 20px;\r\n}\r\n\r\n.confirm-message {\r\n  margin-bottom: 20px;\r\n  text-align: center;\r\n  font-size: 16px;\r\n}\r\n\r\n.confirm-footer {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 16px;\r\n}\r\n\r\n.confirm-cancel {\r\n  padding: 8px 16px;\r\n  border: 1px solid #ddd;\r\n  background-color: white;\r\n  color: #666;\r\n  width: 100px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.confirm-cancel:hover {\r\n  background-color: #f3f4f6;\r\n  border-color: #ccc;\r\n}\r\n\r\n.confirm-ok {\r\n  padding: 8px 16px;\r\n  border: none;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border-radius: 4px;\r\n  width: 100px;\r\n\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.confirm-ok:hover {\r\n  background-color: #2196f3;\r\n}\r\n</style>"], "mappings": "AAq1BA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,EAAAA,CAAA;IACA;IACAG,qBAAA;MACAL,IAAA,EAAAM;IACA;EACA;EACAC,KAAA;IACA;MACAC,eAAA;MACAH,qBAAA;MACAI,gBAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,cAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,EACA;MACAC,mBAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAG,kBAAA,GACA;QAAAJ,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAI,mBAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAK,oBAAA,GACA;QAAAN,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAM,QAAA;IACAC,uBAAA;MACA,aAAAhB,qBAAA;QACA;UACA,KAAAiB,UAAA,QAAAnB,MAAA;UACA,YAAAa,mBAAA;QACA;UACA,KAAAM,UAAA,QAAAnB,MAAA;UACA,YAAAc,kBAAA;QACA;UACA,KAAAK,UAAA,QAAAnB,MAAA;UACA,YAAAe,mBAAA;QACA;UACA,KAAAI,UAAA,QAAAnB,MAAA;UACA,YAAAgB,oBAAA;QACA;UACA,YAAAF,kBAAA;MAAA;IAEA;IACAK,WAAA;MACA,MAAAC,KAAA,QAAApB,MAAA,MAAAE,qBAAA;MACA,QAAAkB,KAAA,QAAAd,gBAAA,EAAAe,OAAA;IACA;IACAC,UAAA;MACA,aAAApB,qBAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IACAqB,UAAA;MACA,aAAArB,qBAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;EACA;EACAsB,KAAA;IACAtB,sBAAA;MACA,KAAAI,gBAAA,QAAAY,sBAAA,KAAAP,KAAA;IACA;IACAL,iBAAA;MACA,KAAAa,UAAA,QAAAnB,MAAA,MAAAE,qBAAA,SAAAI,gBAAA;IACA;IACAA,gBAAA;MACAmB,QAAAC,MAAA;QACA,KAAAC,KAAA,iBAAAD,MAAA,QAAAH,SAAA;MACA;MACAK,SAAA;IACA;IACAT,UAAA;MACAM,QAAAC,MAAA;QACA,KAAAC,KAAA,kBAAAD,MAAA;MACA;MACAE,SAAA;IACA;EACA;EACAC,OAAA;IACAC,WAAA;MACA,KAAAH,KAAA;IACA;IACAI,kBAAA;MACA,KAAAvB,gBAAA;IACA;IACAwB,aAAA;MACA,KAAAxB,gBAAA;MACA,KAAAmB,KAAA;MAEA,MAAAM,KAAA;QACAC,QAAA,OAAAlC,MAAA,CAAAmC,EAAA;QACAC,UAAA,OAAApC,MAAA,CAAAN,IAAA;QACA2C,aAAA,OAAAnC,qBAAA;QACAoC,QAAA,OAAAhC,gBAAA;QACAC,aAAA,OAAAA,aAAA;QACAa,KAAA,OAAAD,UAAA;QACAoB,KAAA;UACAC,QAAA,OAAAxC,MAAA,CAAAN,IAAA;UACA+C,IAAA,OAAAzC,MAAA,CAAAyC,IAAA;UACAC,UAAA,OAAA1C,MAAA,CAAA0C,UAAA;UACAC,SAAA,OAAA3C,MAAA,CAAA2C,SAAA;UACAC,MAAA,OAAA5C,MAAA,CAAA4C,MAAA;UACAC,WAAA,OAAA7C,MAAA,CAAA6C;QACA;MACA;MAEA,KAAAlB,KAAA,kBAAAM,KAAA;MACA,KAAAH,UAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}