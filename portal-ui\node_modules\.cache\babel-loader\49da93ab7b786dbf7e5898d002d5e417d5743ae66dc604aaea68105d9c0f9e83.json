{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    attrs: {\n      id: \"app\"\n    }\n  }, [_vm.showLayout ? _c(\"Layout\", {\n    key: _vm.headerKey\n  }) : _vm._e(), _c(\"router-view\", {\n    key: _vm.$route.fullPath,\n    on: {\n      \"refresh-header\": _vm.refreshHeader,\n      \"hiden-layout\": _vm.hidenLayout\n    }\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "id", "showLayout", "key", "<PERSON><PERSON><PERSON>", "_e", "$route", "fullPath", "on", "refreshHeader", "hidenLayout", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/App.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { attrs: { id: \"app\" } },\n    [\n      _vm.showLayout ? _c(\"Layout\", { key: _vm.headerKey }) : _vm._e(),\n      _c(\"router-view\", {\n        key: _vm.$route.fullPath,\n        on: {\n          \"refresh-header\": _vm.refreshHeader,\n          \"hiden-layout\": _vm.hidenLayout,\n        },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAM;EAAE,CAAC,EACxB,CACEJ,GAAG,CAACK,UAAU,GAAGJ,EAAE,CAAC,QAAQ,EAAE;IAAEK,GAAG,EAAEN,GAAG,CAACO;EAAU,CAAC,CAAC,GAAGP,GAAG,CAACQ,EAAE,EAAE,EAChEP,EAAE,CAAC,aAAa,EAAE;IAChBK,GAAG,EAAEN,GAAG,CAACS,MAAM,CAACC,QAAQ;IACxBC,EAAE,EAAE;MACF,gBAAgB,EAAEX,GAAG,CAACY,aAAa;MACnC,cAAc,EAAEZ,GAAG,CAACa;IACtB;EACF,CAAC,CAAC,CACH,EACD,CAAC,CACF;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBf,MAAM,CAACgB,aAAa,GAAG,IAAI;AAE3B,SAAShB,MAAM,EAAEe,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}