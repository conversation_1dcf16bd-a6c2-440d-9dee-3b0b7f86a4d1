{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-content\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-column\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-title\"\n  }, [_vm._v(\"售前咨询热线\")]), _c(\"div\", {\n    staticClass: \"footer-phone\"\n  }, [_vm._v(\"***********\")]), _c(\"div\", {\n    staticClass: \"footer-links\"\n  }, [_c(\"a\", {\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/index\");\n      }\n    }\n  }, [_c(\"div\", {\n    staticClass: \"footer-link\"\n  }, [_vm._v(\"首页\")])]), _c(\"a\", {\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/product\");\n      }\n    }\n  }, [_c(\"div\", {\n    staticClass: \"footer-link\"\n  }, [_vm._v(\"AI算力市场\")])])])]), _c(\"div\", {\n    staticClass: \"footer-column\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-title\"\n  }, [_vm._v(\"支持与服务\")]), _c(\"div\", {\n    staticClass: \"footer-links\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-link\"\n  }, [_vm._v(\"联系我们\")]), _c(\"a\", {\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/help\");\n      }\n    }\n  }, [_c(\"div\", {\n    staticClass: \"footer-link\"\n  }, [_vm._v(\"帮助文档\")])]), _c(\"div\", {\n    staticClass: \"footer-link\"\n  }, [_vm._v(\"公告\")]), _c(\"div\", {\n    staticClass: \"footer-link\"\n  }, [_vm._v(\"提交建议\")])])]), _vm._m(0), _vm._m(1), _vm._m(2)]), _vm._m(3)]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer-column\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-title\"\n  }, [_vm._v(\"关注天工开物\")]), _c(\"div\", {\n    staticClass: \"footer-links\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-link\"\n  }, [_vm._v(\"关注天工开物\")]), _c(\"div\", {\n    staticClass: \"footer-link\"\n  }, [_vm._v(\"天工开物公众号\")]), _c(\"div\", {\n    staticClass: \"footer-link\"\n  }, [_vm._v(\"天工开物微博\")]), _c(\"div\", {\n    staticClass: \"footer-link\"\n  }, [_vm._v(\"天工开物支持与服务\")])])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer-column\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-title\"\n  }, [_vm._v(\"联系专属客服\")]), _c(\"div\", {\n    staticClass: \"footer-qrcode\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/footer/wechat.jpg\"),\n      alt: \"联系专属客服二维码\"\n    }\n  })])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer-column\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-title\"\n  }, [_vm._v(\"官方公众号\")]), _c(\"div\", {\n    staticClass: \"footer-qrcode\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/footer/wechat.jpg\"),\n      alt: \"官方公众号二维码\"\n    }\n  })])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer-bottom\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-copyright\"\n  }, [_c(\"a\", {\n    staticStyle: {\n      color: \"inherit\",\n      \"text-decoration\": \"none\"\n    },\n    attrs: {\n      href: \"https://beian.miit.gov.cn\",\n      target: \"_blank\"\n    }\n  }, [_vm._v(\" 苏ICP备2025171841号-1 \")]), _vm._v(\" 天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. \")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "on", "click", "$event", "navigateTo", "_m", "staticRenderFns", "attrs", "src", "require", "alt", "staticStyle", "color", "href", "target", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/components/common/footer/Footer.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"footer\" }, [\n    _c(\"div\", { staticClass: \"footer-content\" }, [\n      _c(\"div\", { staticClass: \"footer-column\" }, [\n        _c(\"div\", { staticClass: \"footer-title\" }, [_vm._v(\"售前咨询热线\")]),\n        _c(\"div\", { staticClass: \"footer-phone\" }, [_vm._v(\"***********\")]),\n        _c(\"div\", { staticClass: \"footer-links\" }, [\n          _c(\n            \"a\",\n            {\n              on: {\n                click: function ($event) {\n                  return _vm.navigateTo(\"/index\")\n                },\n              },\n            },\n            [_c(\"div\", { staticClass: \"footer-link\" }, [_vm._v(\"首页\")])]\n          ),\n          _c(\n            \"a\",\n            {\n              on: {\n                click: function ($event) {\n                  return _vm.navigateTo(\"/product\")\n                },\n              },\n            },\n            [_c(\"div\", { staticClass: \"footer-link\" }, [_vm._v(\"AI算力市场\")])]\n          ),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"footer-column\" }, [\n        _c(\"div\", { staticClass: \"footer-title\" }, [_vm._v(\"支持与服务\")]),\n        _c(\"div\", { staticClass: \"footer-links\" }, [\n          _c(\"div\", { staticClass: \"footer-link\" }, [_vm._v(\"联系我们\")]),\n          _c(\n            \"a\",\n            {\n              on: {\n                click: function ($event) {\n                  return _vm.navigateTo(\"/help\")\n                },\n              },\n            },\n            [_c(\"div\", { staticClass: \"footer-link\" }, [_vm._v(\"帮助文档\")])]\n          ),\n          _c(\"div\", { staticClass: \"footer-link\" }, [_vm._v(\"公告\")]),\n          _c(\"div\", { staticClass: \"footer-link\" }, [_vm._v(\"提交建议\")]),\n        ]),\n      ]),\n      _vm._m(0),\n      _vm._m(1),\n      _vm._m(2),\n    ]),\n    _vm._m(3),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"footer-column\" }, [\n      _c(\"div\", { staticClass: \"footer-title\" }, [_vm._v(\"关注天工开物\")]),\n      _c(\"div\", { staticClass: \"footer-links\" }, [\n        _c(\"div\", { staticClass: \"footer-link\" }, [_vm._v(\"关注天工开物\")]),\n        _c(\"div\", { staticClass: \"footer-link\" }, [_vm._v(\"天工开物公众号\")]),\n        _c(\"div\", { staticClass: \"footer-link\" }, [_vm._v(\"天工开物微博\")]),\n        _c(\"div\", { staticClass: \"footer-link\" }, [\n          _vm._v(\"天工开物支持与服务\"),\n        ]),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"footer-column\" }, [\n      _c(\"div\", { staticClass: \"footer-title\" }, [_vm._v(\"联系专属客服\")]),\n      _c(\"div\", { staticClass: \"footer-qrcode\" }, [\n        _c(\"img\", {\n          attrs: {\n            src: require(\"@/assets/images/footer/wechat.jpg\"),\n            alt: \"联系专属客服二维码\",\n          },\n        }),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"footer-column\" }, [\n      _c(\"div\", { staticClass: \"footer-title\" }, [_vm._v(\"官方公众号\")]),\n      _c(\"div\", { staticClass: \"footer-qrcode\" }, [\n        _c(\"img\", {\n          attrs: {\n            src: require(\"@/assets/images/footer/wechat.jpg\"),\n            alt: \"官方公众号二维码\",\n          },\n        }),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"footer-bottom\" }, [\n      _c(\"div\", { staticClass: \"footer-copyright\" }, [\n        _c(\n          \"a\",\n          {\n            staticStyle: { color: \"inherit\", \"text-decoration\": \"none\" },\n            attrs: { href: \"https://beian.miit.gov.cn\", target: \"_blank\" },\n          },\n          [_vm._v(\" 苏ICP备2025171841号-1 \")]\n        ),\n        _vm._v(\" 天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. \"),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAS,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC9DH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EACnEH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CACA,GAAG,EACH;IACEI,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOP,GAAG,CAACQ,UAAU,CAAC,QAAQ,CAAC;MACjC;IACF;EACF,CAAC,EACD,CAACP,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAC5D,EACDH,EAAE,CACA,GAAG,EACH;IACEI,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOP,GAAG,CAACQ,UAAU,CAAC,UAAU,CAAC;MACnC;IACF;EACF,CAAC,EACD,CAACP,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAChE,CACF,CAAC,CACH,CAAC,EACFH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC7DH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC3DH,EAAE,CACA,GAAG,EACH;IACEI,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOP,GAAG,CAACQ,UAAU,CAAC,OAAO,CAAC;MAChC;IACF;EACF,CAAC,EACD,CAACP,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC9D,EACDH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACzDH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC5D,CAAC,CACH,CAAC,EACFJ,GAAG,CAACS,EAAE,CAAC,CAAC,CAAC,EACTT,GAAG,CAACS,EAAE,CAAC,CAAC,CAAC,EACTT,GAAG,CAACS,EAAE,CAAC,CAAC,CAAC,CACV,CAAC,EACFT,GAAG,CAACS,EAAE,CAAC,CAAC,CAAC,CACV,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIV,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC9DH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC7DH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAC9DH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC7DH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCH,GAAG,CAACI,EAAE,CAAC,WAAW,CAAC,CACpB,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIJ,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC9DH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IACRU,KAAK,EAAE;MACLC,GAAG,EAAEC,OAAO,CAAC,mCAAmC,CAAC;MACjDC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAId,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC7DH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IACRU,KAAK,EAAE;MACLC,GAAG,EAAEC,OAAO,CAAC,mCAAmC,CAAC;MACjDC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAId,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CACA,GAAG,EACH;IACEc,WAAW,EAAE;MAAEC,KAAK,EAAE,SAAS;MAAE,iBAAiB,EAAE;IAAO,CAAC;IAC5DL,KAAK,EAAE;MAAEM,IAAI,EAAE,2BAA2B;MAAEC,MAAM,EAAE;IAAS;EAC/D,CAAC,EACD,CAAClB,GAAG,CAACI,EAAE,CAAC,sBAAsB,CAAC,CAAC,CACjC,EACDJ,GAAG,CAACI,EAAE,CAAC,yCAAyC,CAAC,CAClD,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDL,MAAM,CAACoB,aAAa,GAAG,IAAI;AAE3B,SAASpB,MAAM,EAAEW,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}