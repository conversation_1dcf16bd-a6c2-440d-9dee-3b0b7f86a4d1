{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"main\", {\n    staticClass: \"page-wrapper\"\n  }, [_c(\"Header\"), _c(\"div\", {\n    staticClass: \"main-content\"\n  }, [_vm._t(\"default\")], 2), _c(\"Footer\")], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_t", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/components/common/layout-fee.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"main\",\n    { staticClass: \"page-wrapper\" },\n    [\n      _c(\"Header\"),\n      _c(\"div\", { staticClass: \"main-content\" }, [_vm._t(\"default\")], 2),\n      _c(\"Footer\"),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,MAAM,EACN;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAClEH,EAAE,CAAC,QAAQ,CAAC,CACb,EACD,CAAC,CACF;AACH,CAAC;AACD,IAAII,eAAe,GAAG,EAAE;AACxBN,MAAM,CAACO,aAAa,GAAG,IAAI;AAE3B,SAASP,MAAM,EAAEM,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}