{"ast": null, "code": "import Cookies from 'js-cookie';\nconst TokenKey = 'Admin-Token';\nexport function getToken() {\n  return Cookies.get(TokenKey);\n}\nexport function setToken(token) {\n  return Cookies.set(To<PERSON><PERSON><PERSON>, token);\n}\nexport function removeToken() {\n  return Cookies.remove(TokenKey);\n}", "map": {"version": 3, "names": ["Cookies", "TokenKey", "getToken", "get", "setToken", "token", "set", "removeToken", "remove"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/utils/auth.js"], "sourcesContent": ["import Cookies from 'js-cookie'\r\n\r\nconst TokenKey = 'Admin-Token'\r\n\r\nexport function getToken() {\r\n    return Cookies.get(TokenKey)\r\n}\r\n\r\nexport function setToken(token) {\r\n    return Cookies.set(To<PERSON><PERSON><PERSON>, token)\r\n}\r\n\r\nexport function removeToken() {\r\n    return Cookies.remove(TokenKey)\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAE/B,MAAMC,QAAQ,GAAG,aAAa;AAE9B,OAAO,SAASC,QAAQA,CAAA,EAAG;EACvB,OAAOF,OAAO,CAACG,GAAG,CAACF,QAAQ,CAAC;AAChC;AAEA,OAAO,SAASG,QAAQA,CAACC,KAAK,EAAE;EAC5B,OAAOL,OAAO,CAACM,GAAG,CAACL,QAAQ,EAAEI,KAAK,CAAC;AACvC;AAEA,OAAO,SAASE,WAAWA,CAAA,EAAG;EAC1B,OAAOP,OAAO,CAACQ,MAAM,CAACP,QAAQ,CAAC;AACnC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}