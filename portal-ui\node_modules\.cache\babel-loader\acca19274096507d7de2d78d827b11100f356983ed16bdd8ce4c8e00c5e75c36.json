{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as THREE from 'three';\nexport default {\n  name: 'Globe3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      globe: null,\n      nodes: [],\n      animationId: null,\n      rotationSpeed: 0.005,\n      // 固定旋转速度\n      isLoading: true,\n      // 全球算力节点数据\n      computeNodes: [{\n        name: '北京节点',\n        lat: 39.9042,\n        lng: 116.4074,\n        description: '华北地区主要算力中心',\n        gpuCount: '2000+',\n        computePower: '500 PFLOPS'\n      }, {\n        name: '上海节点',\n        lat: 31.2304,\n        lng: 121.4737,\n        description: '华东地区核心算力枢纽',\n        gpuCount: '1800+',\n        computePower: '450 PFLOPS'\n      }, {\n        name: '深圳节点',\n        lat: 22.3193,\n        lng: 114.1694,\n        description: '华南地区智算中心',\n        gpuCount: '1500+',\n        computePower: '380 PFLOPS'\n      }, {\n        name: '成都节点',\n        lat: 30.5728,\n        lng: 104.0668,\n        description: '西南地区算力基地',\n        gpuCount: '1200+',\n        computePower: '300 PFLOPS'\n      }, {\n        name: '杭州节点',\n        lat: 30.2741,\n        lng: 120.1551,\n        description: '长三角算力集群',\n        gpuCount: '1000+',\n        computePower: '250 PFLOPS'\n      }, {\n        name: '新加坡节点',\n        lat: 1.3521,\n        lng: 103.8198,\n        description: '东南亚算力中心',\n        gpuCount: '800+',\n        computePower: '200 PFLOPS'\n      }]\n    };\n  },\n  mounted() {\n    this.initThree();\n    this.createGlobe();\n    this.createNodes();\n    this.createStars();\n    this.animate();\n    this.addEventListeners();\n\n    // 延迟隐藏加载指示器\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1500);\n  },\n  beforeDestroy() {\n    this.cleanup();\n  },\n  methods: {\n    initThree() {\n      // 创建场景\n      this.scene = new THREE.Scene();\n\n      // 创建相机\n      const container = this.$refs.canvasContainer;\n      const width = container.clientWidth;\n      const height = container.clientHeight;\n      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);\n      this.camera.position.z = 4;\n\n      // 创建渲染器\n      this.renderer = new THREE.WebGLRenderer({\n        antialias: true,\n        alpha: false\n      });\n      this.renderer.setSize(width, height);\n      this.renderer.setClearColor(0x000000, 1); // 黑色夜幕背景\n      container.appendChild(this.renderer.domElement);\n\n      // 添加环境光\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.6);\n      this.scene.add(ambientLight);\n\n      // 添加方向光\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n      directionalLight.position.set(1, 1, 1);\n      this.scene.add(directionalLight);\n    },\n    createGlobe() {\n      // 创建地球几何体 - 增大尺寸\n      const geometry = new THREE.SphereGeometry(1.5, 64, 64);\n\n      // 创建地球材质 - 使用更真实的地球效果\n      const material = new THREE.MeshPhongMaterial({\n        color: 0x2E86AB,\n        transparent: true,\n        opacity: 0.9,\n        shininess: 100,\n        wireframe: false\n      });\n      this.globe = new THREE.Mesh(geometry, material);\n      this.scene.add(this.globe);\n\n      // 添加地球轮廓线 - 经纬线效果\n      const wireframeGeometry = new THREE.SphereGeometry(1.508, 32, 16);\n      const wireframeMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        wireframe: true,\n        transparent: true,\n        opacity: 0.4\n      });\n      const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial);\n      this.scene.add(wireframe);\n\n      // 添加大气层效果\n      const atmosphereGeometry = new THREE.SphereGeometry(1.65, 32, 32);\n      const atmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4A90FF,\n        transparent: true,\n        opacity: 0.1,\n        side: THREE.BackSide\n      });\n      const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);\n      this.scene.add(atmosphere);\n    },\n    createNodes() {\n      this.computeNodes.forEach(nodeData => {\n        const node = this.createNode(nodeData);\n        this.nodes.push(node);\n        this.scene.add(node.mesh);\n      });\n\n      // 创建节点间的连接线\n      this.createConnections();\n    },\n    createNode(nodeData) {\n      // 将经纬度转换为3D坐标 - 调整节点位置以适应更大的地球\n      const phi = (90 - nodeData.lat) * (Math.PI / 180);\n      const theta = (nodeData.lng + 180) * (Math.PI / 180);\n      const x = -(1.58 * Math.sin(phi) * Math.cos(theta));\n      const y = 1.58 * Math.cos(phi);\n      const z = 1.58 * Math.sin(phi) * Math.sin(theta);\n\n      // 创建节点几何体\n      const geometry = new THREE.SphereGeometry(0.02, 16, 16);\n      const material = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true\n      });\n      const mesh = new THREE.Mesh(geometry, material);\n      mesh.position.set(x, y, z);\n\n      // 创建光环效果\n      const ringGeometry = new THREE.RingGeometry(0.03, 0.05, 16);\n      const ringMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        transparent: true,\n        opacity: 0.6,\n        side: THREE.DoubleSide\n      });\n      const ring = new THREE.Mesh(ringGeometry, ringMaterial);\n      ring.position.copy(mesh.position);\n      ring.lookAt(new THREE.Vector3(0, 0, 0));\n      this.scene.add(ring);\n      return {\n        mesh,\n        ring,\n        data: nodeData,\n        position: {\n          x,\n          y,\n          z\n        }\n      };\n    },\n    createConnections() {\n      // 创建主要节点间的连接线\n      const connections = [[0, 1],\n      // 北京-上海\n      [1, 2],\n      // 上海-深圳\n      [0, 3],\n      // 北京-成都\n      [1, 4],\n      // 上海-杭州\n      [2, 5] // 深圳-新加坡\n      ];\n\n      connections.forEach(([startIdx, endIdx]) => {\n        const startNode = this.nodes[startIdx];\n        const endNode = this.nodes[endIdx];\n        if (startNode && endNode) {\n          const curve = new THREE.QuadraticBezierCurve3(new THREE.Vector3(startNode.position.x, startNode.position.y, startNode.position.z), new THREE.Vector3(0, 0, 0),\n          // 控制点在地球中心上方\n          new THREE.Vector3(endNode.position.x, endNode.position.y, endNode.position.z));\n          const points = curve.getPoints(50);\n          const geometry = new THREE.BufferGeometry().setFromPoints(points);\n          const material = new THREE.LineBasicMaterial({\n            color: 0x4A90FF,\n            transparent: true,\n            opacity: 0.6\n          });\n          const line = new THREE.Line(geometry, material);\n          this.scene.add(line);\n        }\n      });\n    },\n    createStars() {\n      // 创建星空背景\n      const starsGeometry = new THREE.BufferGeometry();\n      const starsMaterial = new THREE.PointsMaterial({\n        color: 0xFFFFFF,\n        size: 2,\n        transparent: true,\n        opacity: 0.8\n      });\n      const starsVertices = [];\n      for (let i = 0; i < 1000; i++) {\n        const x = (Math.random() - 0.5) * 2000;\n        const y = (Math.random() - 0.5) * 2000;\n        const z = (Math.random() - 0.5) * 2000;\n        starsVertices.push(x, y, z);\n      }\n      starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));\n      const stars = new THREE.Points(starsGeometry, starsMaterial);\n      this.scene.add(stars);\n    },\n    animate() {\n      this.animationId = requestAnimationFrame(this.animate);\n\n      // 旋转地球 - 简化为固定旋转\n      if (this.globe) {\n        this.globe.rotation.y += this.rotationSpeed;\n      }\n\n      // 节点脉冲动画\n      this.nodes.forEach((node, index) => {\n        const time = Date.now() * 0.001;\n        const scale = 1 + Math.sin(time * 2 + index) * 0.3;\n        node.mesh.scale.setScalar(scale);\n\n        // 光环旋转\n        if (node.ring) {\n          node.ring.rotation.z += 0.02;\n        }\n      });\n      this.renderer.render(this.scene, this.camera);\n    },\n    addEventListeners() {\n      window.addEventListener('resize', this.onWindowResize);\n      // 移除所有交互事件监听器\n    },\n\n    onWindowResize() {\n      const container = this.$refs.canvasContainer;\n      const width = container.clientWidth;\n      const height = container.clientHeight;\n      this.camera.aspect = width / height;\n      this.camera.updateProjectionMatrix();\n      this.renderer.setSize(width, height);\n    },\n    onMouseMove(event) {\n      this.mousePosition.x = event.clientX;\n      this.mousePosition.y = event.clientY;\n\n      // 处理拖拽旋转\n      if (this.isDragging) {\n        const deltaX = event.clientX - this.previousMousePosition.x;\n        const deltaY = event.clientY - this.previousMousePosition.y;\n        this.rotationSpeed.y = deltaX * 0.01;\n        this.rotationSpeed.x = -deltaY * 0.01;\n        this.previousMousePosition.x = event.clientX;\n        this.previousMousePosition.y = event.clientY;\n        return;\n      }\n\n      // 射线检测\n      const mouse = new THREE.Vector2();\n      const rect = this.renderer.domElement.getBoundingClientRect();\n      mouse.x = (event.clientX - rect.left) / rect.width * 2 - 1;\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;\n      const raycaster = new THREE.Raycaster();\n      raycaster.setFromCamera(mouse, this.camera);\n      const intersects = raycaster.intersectObjects(this.nodes.map(n => n.mesh));\n      if (intersects.length > 0) {\n        const intersectedNode = this.nodes.find(n => n.mesh === intersects[0].object);\n        this.selectedNode = intersectedNode ? intersectedNode.data : null;\n        this.renderer.domElement.style.cursor = 'pointer';\n      } else {\n        this.selectedNode = null;\n        this.renderer.domElement.style.cursor = this.isDragging ? 'grabbing' : 'grab';\n      }\n    },\n    onMouseClick() {\n      if (this.selectedNode && !this.isDragging) {\n        this.$emit('node-selected', this.selectedNode);\n      }\n    },\n    onMouseDown(event) {\n      this.isDragging = true;\n      this.previousMousePosition.x = event.clientX;\n      this.previousMousePosition.y = event.clientY;\n      this.renderer.domElement.style.cursor = 'grabbing';\n    },\n    onMouseUp() {\n      this.isDragging = false;\n      this.renderer.domElement.style.cursor = 'grab';\n    },\n    cleanup() {\n      if (this.animationId) {\n        cancelAnimationFrame(this.animationId);\n      }\n      window.removeEventListener('resize', this.onWindowResize);\n      if (this.renderer) {\n        this.renderer.dispose();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["THREE", "name", "data", "scene", "camera", "renderer", "globe", "nodes", "animationId", "rotationSpeed", "isLoading", "computeNodes", "lat", "lng", "description", "gpuCount", "computePower", "mounted", "initThree", "createGlobe", "createNodes", "createStars", "animate", "addEventListeners", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "cleanup", "methods", "Scene", "container", "$refs", "canvasContainer", "width", "clientWidth", "height", "clientHeight", "PerspectiveCamera", "position", "z", "WebGLRenderer", "antialias", "alpha", "setSize", "setClearColor", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "add", "directionalLight", "DirectionalLight", "set", "geometry", "SphereGeometry", "material", "MeshPhongMaterial", "color", "transparent", "opacity", "shininess", "wireframe", "<PERSON><PERSON>", "wireframeGeometry", "wireframeMaterial", "MeshBasicMaterial", "atmosphereGeometry", "atmosphereMaterial", "side", "BackSide", "atmosphere", "for<PERSON>ach", "nodeData", "node", "createNode", "push", "mesh", "createConnections", "phi", "Math", "PI", "theta", "x", "sin", "cos", "y", "ringGeometry", "RingGeometry", "ringMaterial", "DoubleSide", "ring", "copy", "lookAt", "Vector3", "connections", "startIdx", "endIdx", "startNode", "endNode", "curve", "QuadraticBezierCurve3", "points", "getPoints", "BufferGeometry", "setFromPoints", "LineBasicMaterial", "line", "Line", "starsGeometry", "starsMaterial", "PointsMaterial", "size", "starsVertices", "i", "random", "setAttribute", "Float32BufferAttribute", "stars", "Points", "requestAnimationFrame", "rotation", "index", "time", "Date", "now", "scale", "setScalar", "render", "window", "addEventListener", "onWindowResize", "aspect", "updateProjectionMatrix", "onMouseMove", "event", "mousePosition", "clientX", "clientY", "isDragging", "deltaX", "previousMousePosition", "deltaY", "mouse", "Vector2", "rect", "getBoundingClientRect", "left", "top", "raycaster", "Raycaster", "setFromCamera", "intersects", "intersectObjects", "map", "n", "length", "intersectedNode", "find", "object", "selectedNode", "style", "cursor", "onMouseClick", "$emit", "onMouseDown", "onMouseUp", "cancelAnimationFrame", "removeEventListener", "dispose"], "sources": ["src/components/common/Globe3D.vue"], "sourcesContent": ["<template>\n  <div class=\"globe-container\" ref=\"globeContainer\">\n    <div class=\"globe-canvas\" ref=\"canvasContainer\"></div>\n    <div class=\"loading-indicator\" v-if=\"isLoading\">\n      <div class=\"loading-spinner\"></div>\n      <div class=\"loading-text\">正在加载全球算力网络...</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as THREE from 'three'\n\nexport default {\n  name: 'Globe3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      globe: null,\n      nodes: [],\n      animationId: null,\n      rotationSpeed: 0.005, // 固定旋转速度\n      isLoading: true,\n      // 全球算力节点数据\n      computeNodes: [\n        {\n          name: '北京节点',\n          lat: 39.9042,\n          lng: 116.4074,\n          description: '华北地区主要算力中心',\n          gpuCount: '2000+',\n          computePower: '500 PFLOPS'\n        },\n        {\n          name: '上海节点',\n          lat: 31.2304,\n          lng: 121.4737,\n          description: '华东地区核心算力枢纽',\n          gpuCount: '1800+',\n          computePower: '450 PFLOPS'\n        },\n        {\n          name: '深圳节点',\n          lat: 22.3193,\n          lng: 114.1694,\n          description: '华南地区智算中心',\n          gpuCount: '1500+',\n          computePower: '380 PFLOPS'\n        },\n        {\n          name: '成都节点',\n          lat: 30.5728,\n          lng: 104.0668,\n          description: '西南地区算力基地',\n          gpuCount: '1200+',\n          computePower: '300 PFLOPS'\n        },\n        {\n          name: '杭州节点',\n          lat: 30.2741,\n          lng: 120.1551,\n          description: '长三角算力集群',\n          gpuCount: '1000+',\n          computePower: '250 PFLOPS'\n        },\n        {\n          name: '新加坡节点',\n          lat: 1.3521,\n          lng: 103.8198,\n          description: '东南亚算力中心',\n          gpuCount: '800+',\n          computePower: '200 PFLOPS'\n        }\n      ]\n    }\n  },\n\n  mounted() {\n    this.initThree()\n    this.createGlobe()\n    this.createNodes()\n    this.createStars()\n    this.animate()\n    this.addEventListeners()\n\n    // 延迟隐藏加载指示器\n    setTimeout(() => {\n      this.isLoading = false\n    }, 1500)\n  },\n  beforeDestroy() {\n    this.cleanup()\n  },\n  methods: {\n    initThree() {\n      // 创建场景\n      this.scene = new THREE.Scene()\n      \n      // 创建相机\n      const container = this.$refs.canvasContainer\n      const width = container.clientWidth\n      const height = container.clientHeight\n      \n      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)\n      this.camera.position.z = 4\n\n      // 创建渲染器\n      this.renderer = new THREE.WebGLRenderer({\n        antialias: true,\n        alpha: false\n      })\n      this.renderer.setSize(width, height)\n      this.renderer.setClearColor(0x000000, 1) // 黑色夜幕背景\n      container.appendChild(this.renderer.domElement)\n      \n      // 添加环境光\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)\n      this.scene.add(ambientLight)\n      \n      // 添加方向光\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)\n      directionalLight.position.set(1, 1, 1)\n      this.scene.add(directionalLight)\n    },\n    \n    createGlobe() {\n      // 创建地球几何体 - 增大尺寸\n      const geometry = new THREE.SphereGeometry(1.5, 64, 64)\n\n      // 创建地球材质 - 使用更真实的地球效果\n      const material = new THREE.MeshPhongMaterial({\n        color: 0x2E86AB,\n        transparent: true,\n        opacity: 0.9,\n        shininess: 100,\n        wireframe: false\n      })\n\n      this.globe = new THREE.Mesh(geometry, material)\n      this.scene.add(this.globe)\n\n      // 添加地球轮廓线 - 经纬线效果\n      const wireframeGeometry = new THREE.SphereGeometry(1.508, 32, 16)\n      const wireframeMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        wireframe: true,\n        transparent: true,\n        opacity: 0.4\n      })\n      const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial)\n      this.scene.add(wireframe)\n\n      // 添加大气层效果\n      const atmosphereGeometry = new THREE.SphereGeometry(1.65, 32, 32)\n      const atmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4A90FF,\n        transparent: true,\n        opacity: 0.1,\n        side: THREE.BackSide\n      })\n      const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial)\n      this.scene.add(atmosphere)\n    },\n    \n    createNodes() {\n      this.computeNodes.forEach(nodeData => {\n        const node = this.createNode(nodeData)\n        this.nodes.push(node)\n        this.scene.add(node.mesh)\n      })\n\n      // 创建节点间的连接线\n      this.createConnections()\n    },\n    \n    createNode(nodeData) {\n      // 将经纬度转换为3D坐标 - 调整节点位置以适应更大的地球\n      const phi = (90 - nodeData.lat) * (Math.PI / 180)\n      const theta = (nodeData.lng + 180) * (Math.PI / 180)\n\n      const x = -(1.58 * Math.sin(phi) * Math.cos(theta))\n      const y = 1.58 * Math.cos(phi)\n      const z = 1.58 * Math.sin(phi) * Math.sin(theta)\n      \n      // 创建节点几何体\n      const geometry = new THREE.SphereGeometry(0.02, 16, 16)\n      const material = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true\n      })\n      \n      const mesh = new THREE.Mesh(geometry, material)\n      mesh.position.set(x, y, z)\n      \n      // 创建光环效果\n      const ringGeometry = new THREE.RingGeometry(0.03, 0.05, 16)\n      const ringMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        transparent: true,\n        opacity: 0.6,\n        side: THREE.DoubleSide\n      })\n      const ring = new THREE.Mesh(ringGeometry, ringMaterial)\n      ring.position.copy(mesh.position)\n      ring.lookAt(new THREE.Vector3(0, 0, 0))\n      this.scene.add(ring)\n      \n      return {\n        mesh,\n        ring,\n        data: nodeData,\n        position: { x, y, z }\n      }\n    },\n\n    createConnections() {\n      // 创建主要节点间的连接线\n      const connections = [\n        [0, 1], // 北京-上海\n        [1, 2], // 上海-深圳\n        [0, 3], // 北京-成都\n        [1, 4], // 上海-杭州\n        [2, 5]  // 深圳-新加坡\n      ]\n\n      connections.forEach(([startIdx, endIdx]) => {\n        const startNode = this.nodes[startIdx]\n        const endNode = this.nodes[endIdx]\n\n        if (startNode && endNode) {\n          const curve = new THREE.QuadraticBezierCurve3(\n            new THREE.Vector3(startNode.position.x, startNode.position.y, startNode.position.z),\n            new THREE.Vector3(0, 0, 0), // 控制点在地球中心上方\n            new THREE.Vector3(endNode.position.x, endNode.position.y, endNode.position.z)\n          )\n\n          const points = curve.getPoints(50)\n          const geometry = new THREE.BufferGeometry().setFromPoints(points)\n          const material = new THREE.LineBasicMaterial({\n            color: 0x4A90FF,\n            transparent: true,\n            opacity: 0.6\n          })\n\n          const line = new THREE.Line(geometry, material)\n          this.scene.add(line)\n        }\n      })\n    },\n\n    createStars() {\n      // 创建星空背景\n      const starsGeometry = new THREE.BufferGeometry()\n      const starsMaterial = new THREE.PointsMaterial({\n        color: 0xFFFFFF,\n        size: 2,\n        transparent: true,\n        opacity: 0.8\n      })\n\n      const starsVertices = []\n      for (let i = 0; i < 1000; i++) {\n        const x = (Math.random() - 0.5) * 2000\n        const y = (Math.random() - 0.5) * 2000\n        const z = (Math.random() - 0.5) * 2000\n        starsVertices.push(x, y, z)\n      }\n\n      starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3))\n      const stars = new THREE.Points(starsGeometry, starsMaterial)\n      this.scene.add(stars)\n    },\n    \n    animate() {\n      this.animationId = requestAnimationFrame(this.animate)\n\n      // 旋转地球 - 简化为固定旋转\n      if (this.globe) {\n        this.globe.rotation.y += this.rotationSpeed\n      }\n      \n      // 节点脉冲动画\n      this.nodes.forEach((node, index) => {\n        const time = Date.now() * 0.001\n        const scale = 1 + Math.sin(time * 2 + index) * 0.3\n        node.mesh.scale.setScalar(scale)\n        \n        // 光环旋转\n        if (node.ring) {\n          node.ring.rotation.z += 0.02\n        }\n      })\n      \n      this.renderer.render(this.scene, this.camera)\n    },\n    \n    addEventListeners() {\n      window.addEventListener('resize', this.onWindowResize)\n      // 移除所有交互事件监听器\n    },\n    \n    onWindowResize() {\n      const container = this.$refs.canvasContainer\n      const width = container.clientWidth\n      const height = container.clientHeight\n      \n      this.camera.aspect = width / height\n      this.camera.updateProjectionMatrix()\n      this.renderer.setSize(width, height)\n    },\n    \n    onMouseMove(event) {\n      this.mousePosition.x = event.clientX\n      this.mousePosition.y = event.clientY\n\n      // 处理拖拽旋转\n      if (this.isDragging) {\n        const deltaX = event.clientX - this.previousMousePosition.x\n        const deltaY = event.clientY - this.previousMousePosition.y\n\n        this.rotationSpeed.y = deltaX * 0.01\n        this.rotationSpeed.x = -deltaY * 0.01\n\n        this.previousMousePosition.x = event.clientX\n        this.previousMousePosition.y = event.clientY\n        return\n      }\n\n      // 射线检测\n      const mouse = new THREE.Vector2()\n      const rect = this.renderer.domElement.getBoundingClientRect()\n      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1\n\n      const raycaster = new THREE.Raycaster()\n      raycaster.setFromCamera(mouse, this.camera)\n\n      const intersects = raycaster.intersectObjects(this.nodes.map(n => n.mesh))\n\n      if (intersects.length > 0) {\n        const intersectedNode = this.nodes.find(n => n.mesh === intersects[0].object)\n        this.selectedNode = intersectedNode ? intersectedNode.data : null\n        this.renderer.domElement.style.cursor = 'pointer'\n      } else {\n        this.selectedNode = null\n        this.renderer.domElement.style.cursor = this.isDragging ? 'grabbing' : 'grab'\n      }\n    },\n    \n    onMouseClick() {\n      if (this.selectedNode && !this.isDragging) {\n        this.$emit('node-selected', this.selectedNode)\n      }\n    },\n\n    onMouseDown(event) {\n      this.isDragging = true\n      this.previousMousePosition.x = event.clientX\n      this.previousMousePosition.y = event.clientY\n      this.renderer.domElement.style.cursor = 'grabbing'\n    },\n\n    onMouseUp() {\n      this.isDragging = false\n      this.renderer.domElement.style.cursor = 'grab'\n    },\n    \n    cleanup() {\n      if (this.animationId) {\n        cancelAnimationFrame(this.animationId)\n      }\n      \n      window.removeEventListener('resize', this.onWindowResize)\n      \n      if (this.renderer) {\n        this.renderer.dispose()\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.globe-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n\n.globe-canvas {\n  width: 100%;\n  height: 100%;\n  cursor: grab;\n}\n\n.globe-canvas:active {\n  cursor: grabbing;\n}\n\n.globe-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n  color: white;\n}\n\n.globe-title {\n  font-size: 32px;\n  font-weight: 600;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);\n}\n\n.globe-subtitle {\n  font-size: 18px;\n  opacity: 0.9;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n  margin-bottom: 20px;\n}\n\n.interaction-hint {\n  font-size: 14px;\n  opacity: 0.7;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 0.7; }\n  50% { opacity: 1; }\n}\n\n.loading-indicator {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  background: rgba(20, 112, 255, 0.1);\n  backdrop-filter: blur(5px);\n  color: white;\n  z-index: 10;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 3px solid rgba(255, 255, 255, 0.3);\n  border-top: 3px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 16px;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n}\n\n.node-info {\n  position: fixed;\n  background: rgba(20, 112, 255, 0.95);\n  color: white;\n  padding: 15px;\n  border-radius: 8px;\n  box-shadow: 0 4px 15px rgba(0,0,0,0.3);\n  pointer-events: none;\n  z-index: 1000;\n  min-width: 200px;\n  backdrop-filter: blur(10px);\n}\n\n.node-info h4 {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.node-info p {\n  margin: 0 0 10px 0;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.node-stats {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.node-stats span {\n  font-size: 12px;\n  opacity: 0.8;\n}\n\n@media (max-width: 768px) {\n  .globe-title {\n    font-size: 24px;\n  }\n\n  .globe-subtitle {\n    font-size: 16px;\n    margin-bottom: 15px;\n  }\n\n  .interaction-hint {\n    font-size: 12px;\n  }\n\n  .node-info {\n    min-width: 160px;\n    padding: 10px;\n    font-size: 12px;\n  }\n\n  .node-info h4 {\n    font-size: 14px;\n  }\n\n  .loading-text {\n    font-size: 14px;\n  }\n\n  .loading-spinner {\n    width: 40px;\n    height: 40px;\n    margin-bottom: 15px;\n  }\n}\n</style>\n"], "mappings": ";AAWA,YAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,KAAA;MACAC,MAAA;MACAC,QAAA;MACAC,KAAA;MACAC,KAAA;MACAC,WAAA;MACAC,aAAA;MAAA;MACAC,SAAA;MACA;MACAC,YAAA,GACA;QACAV,IAAA;QACAW,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAf,IAAA;QACAW,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAf,IAAA;QACAW,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAf,IAAA;QACAW,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAf,IAAA;QACAW,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAf,IAAA;QACAW,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA;IAEA;EACA;EAEAC,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,WAAA;IACA,KAAAC,WAAA;IACA,KAAAC,WAAA;IACA,KAAAC,OAAA;IACA,KAAAC,iBAAA;;IAEA;IACAC,UAAA;MACA,KAAAd,SAAA;IACA;EACA;EACAe,cAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAT,UAAA;MACA;MACA,KAAAf,KAAA,OAAAH,KAAA,CAAA4B,KAAA;;MAEA;MACA,MAAAC,SAAA,QAAAC,KAAA,CAAAC,eAAA;MACA,MAAAC,KAAA,GAAAH,SAAA,CAAAI,WAAA;MACA,MAAAC,MAAA,GAAAL,SAAA,CAAAM,YAAA;MAEA,KAAA/B,MAAA,OAAAJ,KAAA,CAAAoC,iBAAA,KAAAJ,KAAA,GAAAE,MAAA;MACA,KAAA9B,MAAA,CAAAiC,QAAA,CAAAC,CAAA;;MAEA;MACA,KAAAjC,QAAA,OAAAL,KAAA,CAAAuC,aAAA;QACAC,SAAA;QACAC,KAAA;MACA;MACA,KAAApC,QAAA,CAAAqC,OAAA,CAAAV,KAAA,EAAAE,MAAA;MACA,KAAA7B,QAAA,CAAAsC,aAAA;MACAd,SAAA,CAAAe,WAAA,MAAAvC,QAAA,CAAAwC,UAAA;;MAEA;MACA,MAAAC,YAAA,OAAA9C,KAAA,CAAA+C,YAAA;MACA,KAAA5C,KAAA,CAAA6C,GAAA,CAAAF,YAAA;;MAEA;MACA,MAAAG,gBAAA,OAAAjD,KAAA,CAAAkD,gBAAA;MACAD,gBAAA,CAAAZ,QAAA,CAAAc,GAAA;MACA,KAAAhD,KAAA,CAAA6C,GAAA,CAAAC,gBAAA;IACA;IAEA9B,YAAA;MACA;MACA,MAAAiC,QAAA,OAAApD,KAAA,CAAAqD,cAAA;;MAEA;MACA,MAAAC,QAAA,OAAAtD,KAAA,CAAAuD,iBAAA;QACAC,KAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;MACA;MAEA,KAAAtD,KAAA,OAAAN,KAAA,CAAA6D,IAAA,CAAAT,QAAA,EAAAE,QAAA;MACA,KAAAnD,KAAA,CAAA6C,GAAA,MAAA1C,KAAA;;MAEA;MACA,MAAAwD,iBAAA,OAAA9D,KAAA,CAAAqD,cAAA;MACA,MAAAU,iBAAA,OAAA/D,KAAA,CAAAgE,iBAAA;QACAR,KAAA;QACAI,SAAA;QACAH,WAAA;QACAC,OAAA;MACA;MACA,MAAAE,SAAA,OAAA5D,KAAA,CAAA6D,IAAA,CAAAC,iBAAA,EAAAC,iBAAA;MACA,KAAA5D,KAAA,CAAA6C,GAAA,CAAAY,SAAA;;MAEA;MACA,MAAAK,kBAAA,OAAAjE,KAAA,CAAAqD,cAAA;MACA,MAAAa,kBAAA,OAAAlE,KAAA,CAAAgE,iBAAA;QACAR,KAAA;QACAC,WAAA;QACAC,OAAA;QACAS,IAAA,EAAAnE,KAAA,CAAAoE;MACA;MACA,MAAAC,UAAA,OAAArE,KAAA,CAAA6D,IAAA,CAAAI,kBAAA,EAAAC,kBAAA;MACA,KAAA/D,KAAA,CAAA6C,GAAA,CAAAqB,UAAA;IACA;IAEAjD,YAAA;MACA,KAAAT,YAAA,CAAA2D,OAAA,CAAAC,QAAA;QACA,MAAAC,IAAA,QAAAC,UAAA,CAAAF,QAAA;QACA,KAAAhE,KAAA,CAAAmE,IAAA,CAAAF,IAAA;QACA,KAAArE,KAAA,CAAA6C,GAAA,CAAAwB,IAAA,CAAAG,IAAA;MACA;;MAEA;MACA,KAAAC,iBAAA;IACA;IAEAH,WAAAF,QAAA;MACA;MACA,MAAAM,GAAA,SAAAN,QAAA,CAAA3D,GAAA,KAAAkE,IAAA,CAAAC,EAAA;MACA,MAAAC,KAAA,IAAAT,QAAA,CAAA1D,GAAA,WAAAiE,IAAA,CAAAC,EAAA;MAEA,MAAAE,CAAA,YAAAH,IAAA,CAAAI,GAAA,CAAAL,GAAA,IAAAC,IAAA,CAAAK,GAAA,CAAAH,KAAA;MACA,MAAAI,CAAA,UAAAN,IAAA,CAAAK,GAAA,CAAAN,GAAA;MACA,MAAAvC,CAAA,UAAAwC,IAAA,CAAAI,GAAA,CAAAL,GAAA,IAAAC,IAAA,CAAAI,GAAA,CAAAF,KAAA;;MAEA;MACA,MAAA5B,QAAA,OAAApD,KAAA,CAAAqD,cAAA;MACA,MAAAC,QAAA,OAAAtD,KAAA,CAAAgE,iBAAA;QACAR,KAAA;QACAC,WAAA;MACA;MAEA,MAAAkB,IAAA,OAAA3E,KAAA,CAAA6D,IAAA,CAAAT,QAAA,EAAAE,QAAA;MACAqB,IAAA,CAAAtC,QAAA,CAAAc,GAAA,CAAA8B,CAAA,EAAAG,CAAA,EAAA9C,CAAA;;MAEA;MACA,MAAA+C,YAAA,OAAArF,KAAA,CAAAsF,YAAA;MACA,MAAAC,YAAA,OAAAvF,KAAA,CAAAgE,iBAAA;QACAR,KAAA;QACAC,WAAA;QACAC,OAAA;QACAS,IAAA,EAAAnE,KAAA,CAAAwF;MACA;MACA,MAAAC,IAAA,OAAAzF,KAAA,CAAA6D,IAAA,CAAAwB,YAAA,EAAAE,YAAA;MACAE,IAAA,CAAApD,QAAA,CAAAqD,IAAA,CAAAf,IAAA,CAAAtC,QAAA;MACAoD,IAAA,CAAAE,MAAA,KAAA3F,KAAA,CAAA4F,OAAA;MACA,KAAAzF,KAAA,CAAA6C,GAAA,CAAAyC,IAAA;MAEA;QACAd,IAAA;QACAc,IAAA;QACAvF,IAAA,EAAAqE,QAAA;QACAlC,QAAA;UAAA4C,CAAA;UAAAG,CAAA;UAAA9C;QAAA;MACA;IACA;IAEAsC,kBAAA;MACA;MACA,MAAAiB,WAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;;MAEAA,WAAA,CAAAvB,OAAA,GAAAwB,QAAA,EAAAC,MAAA;QACA,MAAAC,SAAA,QAAAzF,KAAA,CAAAuF,QAAA;QACA,MAAAG,OAAA,QAAA1F,KAAA,CAAAwF,MAAA;QAEA,IAAAC,SAAA,IAAAC,OAAA;UACA,MAAAC,KAAA,OAAAlG,KAAA,CAAAmG,qBAAA,CACA,IAAAnG,KAAA,CAAA4F,OAAA,CAAAI,SAAA,CAAA3D,QAAA,CAAA4C,CAAA,EAAAe,SAAA,CAAA3D,QAAA,CAAA+C,CAAA,EAAAY,SAAA,CAAA3D,QAAA,CAAAC,CAAA,GACA,IAAAtC,KAAA,CAAA4F,OAAA;UAAA;UACA,IAAA5F,KAAA,CAAA4F,OAAA,CAAAK,OAAA,CAAA5D,QAAA,CAAA4C,CAAA,EAAAgB,OAAA,CAAA5D,QAAA,CAAA+C,CAAA,EAAAa,OAAA,CAAA5D,QAAA,CAAAC,CAAA,EACA;UAEA,MAAA8D,MAAA,GAAAF,KAAA,CAAAG,SAAA;UACA,MAAAjD,QAAA,OAAApD,KAAA,CAAAsG,cAAA,GAAAC,aAAA,CAAAH,MAAA;UACA,MAAA9C,QAAA,OAAAtD,KAAA,CAAAwG,iBAAA;YACAhD,KAAA;YACAC,WAAA;YACAC,OAAA;UACA;UAEA,MAAA+C,IAAA,OAAAzG,KAAA,CAAA0G,IAAA,CAAAtD,QAAA,EAAAE,QAAA;UACA,KAAAnD,KAAA,CAAA6C,GAAA,CAAAyD,IAAA;QACA;MACA;IACA;IAEApF,YAAA;MACA;MACA,MAAAsF,aAAA,OAAA3G,KAAA,CAAAsG,cAAA;MACA,MAAAM,aAAA,OAAA5G,KAAA,CAAA6G,cAAA;QACArD,KAAA;QACAsD,IAAA;QACArD,WAAA;QACAC,OAAA;MACA;MAEA,MAAAqD,aAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,SAAAA,CAAA;QACA,MAAA/B,CAAA,IAAAH,IAAA,CAAAmC,MAAA;QACA,MAAA7B,CAAA,IAAAN,IAAA,CAAAmC,MAAA;QACA,MAAA3E,CAAA,IAAAwC,IAAA,CAAAmC,MAAA;QACAF,aAAA,CAAArC,IAAA,CAAAO,CAAA,EAAAG,CAAA,EAAA9C,CAAA;MACA;MAEAqE,aAAA,CAAAO,YAAA,iBAAAlH,KAAA,CAAAmH,sBAAA,CAAAJ,aAAA;MACA,MAAAK,KAAA,OAAApH,KAAA,CAAAqH,MAAA,CAAAV,aAAA,EAAAC,aAAA;MACA,KAAAzG,KAAA,CAAA6C,GAAA,CAAAoE,KAAA;IACA;IAEA9F,QAAA;MACA,KAAAd,WAAA,GAAA8G,qBAAA,MAAAhG,OAAA;;MAEA;MACA,SAAAhB,KAAA;QACA,KAAAA,KAAA,CAAAiH,QAAA,CAAAnC,CAAA,SAAA3E,aAAA;MACA;;MAEA;MACA,KAAAF,KAAA,CAAA+D,OAAA,EAAAE,IAAA,EAAAgD,KAAA;QACA,MAAAC,IAAA,GAAAC,IAAA,CAAAC,GAAA;QACA,MAAAC,KAAA,OAAA9C,IAAA,CAAAI,GAAA,CAAAuC,IAAA,OAAAD,KAAA;QACAhD,IAAA,CAAAG,IAAA,CAAAiD,KAAA,CAAAC,SAAA,CAAAD,KAAA;;QAEA;QACA,IAAApD,IAAA,CAAAiB,IAAA;UACAjB,IAAA,CAAAiB,IAAA,CAAA8B,QAAA,CAAAjF,CAAA;QACA;MACA;MAEA,KAAAjC,QAAA,CAAAyH,MAAA,MAAA3H,KAAA,OAAAC,MAAA;IACA;IAEAmB,kBAAA;MACAwG,MAAA,CAAAC,gBAAA,gBAAAC,cAAA;MACA;IACA;;IAEAA,eAAA;MACA,MAAApG,SAAA,QAAAC,KAAA,CAAAC,eAAA;MACA,MAAAC,KAAA,GAAAH,SAAA,CAAAI,WAAA;MACA,MAAAC,MAAA,GAAAL,SAAA,CAAAM,YAAA;MAEA,KAAA/B,MAAA,CAAA8H,MAAA,GAAAlG,KAAA,GAAAE,MAAA;MACA,KAAA9B,MAAA,CAAA+H,sBAAA;MACA,KAAA9H,QAAA,CAAAqC,OAAA,CAAAV,KAAA,EAAAE,MAAA;IACA;IAEAkG,YAAAC,KAAA;MACA,KAAAC,aAAA,CAAArD,CAAA,GAAAoD,KAAA,CAAAE,OAAA;MACA,KAAAD,aAAA,CAAAlD,CAAA,GAAAiD,KAAA,CAAAG,OAAA;;MAEA;MACA,SAAAC,UAAA;QACA,MAAAC,MAAA,GAAAL,KAAA,CAAAE,OAAA,QAAAI,qBAAA,CAAA1D,CAAA;QACA,MAAA2D,MAAA,GAAAP,KAAA,CAAAG,OAAA,QAAAG,qBAAA,CAAAvD,CAAA;QAEA,KAAA3E,aAAA,CAAA2E,CAAA,GAAAsD,MAAA;QACA,KAAAjI,aAAA,CAAAwE,CAAA,IAAA2D,MAAA;QAEA,KAAAD,qBAAA,CAAA1D,CAAA,GAAAoD,KAAA,CAAAE,OAAA;QACA,KAAAI,qBAAA,CAAAvD,CAAA,GAAAiD,KAAA,CAAAG,OAAA;QACA;MACA;;MAEA;MACA,MAAAK,KAAA,OAAA7I,KAAA,CAAA8I,OAAA;MACA,MAAAC,IAAA,QAAA1I,QAAA,CAAAwC,UAAA,CAAAmG,qBAAA;MACAH,KAAA,CAAA5D,CAAA,IAAAoD,KAAA,CAAAE,OAAA,GAAAQ,IAAA,CAAAE,IAAA,IAAAF,IAAA,CAAA/G,KAAA;MACA6G,KAAA,CAAAzD,CAAA,MAAAiD,KAAA,CAAAG,OAAA,GAAAO,IAAA,CAAAG,GAAA,IAAAH,IAAA,CAAA7G,MAAA;MAEA,MAAAiH,SAAA,OAAAnJ,KAAA,CAAAoJ,SAAA;MACAD,SAAA,CAAAE,aAAA,CAAAR,KAAA,OAAAzI,MAAA;MAEA,MAAAkJ,UAAA,GAAAH,SAAA,CAAAI,gBAAA,MAAAhJ,KAAA,CAAAiJ,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAA9E,IAAA;MAEA,IAAA2E,UAAA,CAAAI,MAAA;QACA,MAAAC,eAAA,QAAApJ,KAAA,CAAAqJ,IAAA,CAAAH,CAAA,IAAAA,CAAA,CAAA9E,IAAA,KAAA2E,UAAA,IAAAO,MAAA;QACA,KAAAC,YAAA,GAAAH,eAAA,GAAAA,eAAA,CAAAzJ,IAAA;QACA,KAAAG,QAAA,CAAAwC,UAAA,CAAAkH,KAAA,CAAAC,MAAA;MACA;QACA,KAAAF,YAAA;QACA,KAAAzJ,QAAA,CAAAwC,UAAA,CAAAkH,KAAA,CAAAC,MAAA,QAAAvB,UAAA;MACA;IACA;IAEAwB,aAAA;MACA,SAAAH,YAAA,UAAArB,UAAA;QACA,KAAAyB,KAAA,uBAAAJ,YAAA;MACA;IACA;IAEAK,YAAA9B,KAAA;MACA,KAAAI,UAAA;MACA,KAAAE,qBAAA,CAAA1D,CAAA,GAAAoD,KAAA,CAAAE,OAAA;MACA,KAAAI,qBAAA,CAAAvD,CAAA,GAAAiD,KAAA,CAAAG,OAAA;MACA,KAAAnI,QAAA,CAAAwC,UAAA,CAAAkH,KAAA,CAAAC,MAAA;IACA;IAEAI,UAAA;MACA,KAAA3B,UAAA;MACA,KAAApI,QAAA,CAAAwC,UAAA,CAAAkH,KAAA,CAAAC,MAAA;IACA;IAEAtI,QAAA;MACA,SAAAlB,WAAA;QACA6J,oBAAA,MAAA7J,WAAA;MACA;MAEAuH,MAAA,CAAAuC,mBAAA,gBAAArC,cAAA;MAEA,SAAA5H,QAAA;QACA,KAAAA,QAAA,CAAAkK,OAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}