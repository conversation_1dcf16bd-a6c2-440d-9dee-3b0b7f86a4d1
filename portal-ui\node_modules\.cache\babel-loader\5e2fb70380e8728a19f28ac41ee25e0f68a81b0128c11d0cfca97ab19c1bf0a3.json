{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as THREE from 'three';\nexport default {\n  name: 'Globe3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      globe: null,\n      nodes: [],\n      animationId: null,\n      rotationSpeed: 0.0025,\n      // 降低旋转速度，使旋转更平滑\n      autoRotate: true,\n      // 自动旋转控制\n      isLoading: true,\n      // 全球算力节点数据\n      computeNodes: [{\n        name: '北京节点',\n        lat: 39.9042,\n        lng: 116.4074,\n        description: '华北地区主要算力中心',\n        gpuCount: '2000+',\n        computePower: '500 PFLOPS'\n      }, {\n        name: '上海节点',\n        lat: 31.2304,\n        lng: 121.4737,\n        description: '华东地区核心算力枢纽',\n        gpuCount: '1800+',\n        computePower: '450 PFLOPS'\n      }, {\n        name: '深圳节点',\n        lat: 22.3193,\n        lng: 114.1694,\n        description: '华南地区智算中心',\n        gpuCount: '1500+',\n        computePower: '380 PFLOPS'\n      }, {\n        name: '成都节点',\n        lat: 30.5728,\n        lng: 104.0668,\n        description: '西南地区算力基地',\n        gpuCount: '1200+',\n        computePower: '300 PFLOPS'\n      }, {\n        name: '杭州节点',\n        lat: 30.2741,\n        lng: 120.1551,\n        description: '长三角算力集群',\n        gpuCount: '1000+',\n        computePower: '250 PFLOPS'\n      }, {\n        name: '新加坡节点',\n        lat: 1.3521,\n        lng: 103.8198,\n        description: '东南亚算力中心',\n        gpuCount: '800+',\n        computePower: '200 PFLOPS'\n      }]\n    };\n  },\n  mounted() {\n    this.initThree();\n    this.createGlobe();\n    this.createNodes();\n    this.createStars();\n    this.animate();\n    this.addEventListeners();\n\n    // 延迟隐藏加载指示器\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1500);\n  },\n  beforeDestroy() {\n    this.cleanup();\n  },\n  methods: {\n    initThree() {\n      // 创建场景\n      this.scene = new THREE.Scene();\n\n      // 创建相机\n      const container = this.$refs.canvasContainer;\n      const width = container.clientWidth;\n      const height = container.clientHeight;\n      this.camera = new THREE.PerspectiveCamera(60, width / height, 0.1, 2000);\n      this.camera.position.z = 5.5; // 增加相机距离，使地球在视野中更完整\n\n      // 创建渲染器\n      this.renderer = new THREE.WebGLRenderer({\n        antialias: true,\n        alpha: true\n      });\n      this.renderer.setSize(width, height);\n      this.renderer.setClearColor(0x000814, 1); // 深蓝色背景，更符合太空感\n      this.renderer.setPixelRatio(window.devicePixelRatio); // 提高渲染质量\n      container.appendChild(this.renderer.domElement);\n\n      // 添加环境光 - 增强亮度\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.8);\n      this.scene.add(ambientLight);\n\n      // 添加方向光 - 模拟太阳光照\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2);\n      directionalLight.position.set(5, 3, 5);\n      this.scene.add(directionalLight);\n\n      // 添加第二个方向光，增强背面照明\n      const backLight = new THREE.DirectionalLight(0x2277ff, 0.3);\n      backLight.position.set(-5, -3, -5);\n      this.scene.add(backLight);\n    },\n    createGlobe() {\n      // 创建地球几何体 - 进一步增大尺寸\n      const geometry = new THREE.SphereGeometry(2.2, 96, 96); // 增大半径，提高分段数以获得更平滑的球体\n\n      // 创建地球材质 - 使用更真实的地球效果\n      const material = new THREE.MeshPhongMaterial({\n        color: 0x0077be,\n        // 更深的蓝色\n        transparent: true,\n        opacity: 0.95,\n        shininess: 25,\n        // 降低反光度，更自然\n        specular: 0x4488ff,\n        // 添加蓝色高光\n        wireframe: false\n      });\n      this.globe = new THREE.Mesh(geometry, material);\n      this.scene.add(this.globe);\n\n      // 添加地球轮廓线 - 经纬线效果，更精细\n      const wireframeGeometry = new THREE.SphereGeometry(2.21, 48, 24); // 略大于地球本身\n      const wireframeMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4a90ff,\n        wireframe: true,\n        transparent: true,\n        opacity: 0.15 // 降低不透明度，更微妙\n      });\n\n      const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial);\n      this.scene.add(wireframe);\n\n      // 添加内部发光效果\n      const glowGeometry = new THREE.SphereGeometry(2.18, 64, 64);\n      const glowMaterial = new THREE.MeshBasicMaterial({\n        color: 0x0088ff,\n        transparent: true,\n        opacity: 0.05,\n        side: THREE.FrontSide\n      });\n      const glow = new THREE.Mesh(glowGeometry, glowMaterial);\n      this.scene.add(glow);\n\n      // 添加大气层效果 - 更明显的外发光\n      const atmosphereGeometry = new THREE.SphereGeometry(2.4, 64, 64); // 增大大气层\n      const atmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4a90ff,\n        transparent: true,\n        opacity: 0.12,\n        side: THREE.BackSide\n      });\n      const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);\n      this.scene.add(atmosphere);\n\n      // 添加第二层大气层，创造渐变效果\n      const outerAtmosphereGeometry = new THREE.SphereGeometry(2.6, 64, 64);\n      const outerAtmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470ff,\n        transparent: true,\n        opacity: 0.06,\n        side: THREE.BackSide\n      });\n      const outerAtmosphere = new THREE.Mesh(outerAtmosphereGeometry, outerAtmosphereMaterial);\n      this.scene.add(outerAtmosphere);\n    },\n    createNodes() {\n      this.computeNodes.forEach(nodeData => {\n        const node = this.createNode(nodeData);\n        this.nodes.push(node);\n        this.scene.add(node.mesh);\n      });\n\n      // 创建节点间的连接线\n      this.createConnections();\n    },\n    createNode(nodeData) {\n      // 将经纬度转换为3D坐标 - 调整节点位置以适应更大的地球\n      const phi = (90 - nodeData.lat) * (Math.PI / 180);\n      const theta = (nodeData.lng + 180) * (Math.PI / 180);\n\n      // 调整坐标以匹配新的地球尺寸\n      const radius = 2.28; // 略大于地球表面\n      const x = -(radius * Math.sin(phi) * Math.cos(theta));\n      const y = radius * Math.cos(phi);\n      const z = radius * Math.sin(phi) * Math.sin(theta);\n\n      // 创建节点几何体 - 增大节点尺寸\n      const geometry = new THREE.SphereGeometry(0.035, 16, 16);\n      const material = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true,\n        opacity: 0.9\n      });\n      const mesh = new THREE.Mesh(geometry, material);\n      mesh.position.set(x, y, z);\n\n      // 创建内部发光效果\n      const glowGeometry = new THREE.SphereGeometry(0.04, 16, 16);\n      const glowMaterial = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true,\n        opacity: 0.4,\n        side: THREE.BackSide\n      });\n      const glow = new THREE.Mesh(glowGeometry, glowMaterial);\n      glow.position.copy(mesh.position);\n      this.scene.add(glow);\n\n      // 创建光环效果 - 增大光环\n      const ringGeometry = new THREE.RingGeometry(0.05, 0.08, 32);\n      const ringMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4A90FF,\n        transparent: true,\n        opacity: 0.7,\n        side: THREE.DoubleSide\n      });\n      const ring = new THREE.Mesh(ringGeometry, ringMaterial);\n      ring.position.copy(mesh.position);\n      ring.lookAt(new THREE.Vector3(0, 0, 0));\n      this.scene.add(ring);\n\n      // 创建第二个光环\n      const outerRingGeometry = new THREE.RingGeometry(0.09, 0.1, 32);\n      const outerRingMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        transparent: true,\n        opacity: 0.4,\n        side: THREE.DoubleSide\n      });\n      const outerRing = new THREE.Mesh(outerRingGeometry, outerRingMaterial);\n      outerRing.position.copy(mesh.position);\n      outerRing.lookAt(new THREE.Vector3(0, 0, 0));\n      this.scene.add(outerRing);\n      return {\n        mesh,\n        ring,\n        outerRing,\n        glow,\n        data: nodeData,\n        position: {\n          x,\n          y,\n          z\n        }\n      };\n    },\n    createConnections() {\n      // 创建主要节点间的连接线\n      const connections = [[0, 1],\n      // 北京-上海\n      [1, 2],\n      // 上海-深圳\n      [0, 3],\n      // 北京-成都\n      [1, 4],\n      // 上海-杭州\n      [2, 5],\n      // 深圳-新加坡\n      [0, 4],\n      // 北京-杭州\n      [3, 5] // 成都-新加坡\n      ];\n\n      connections.forEach(([startIdx, endIdx], index) => {\n        const startNode = this.nodes[startIdx];\n        const endNode = this.nodes[endIdx];\n        if (startNode && endNode) {\n          // 计算控制点 - 使曲线更加弯曲\n          const startVec = new THREE.Vector3(startNode.position.x, startNode.position.y, startNode.position.z);\n          const endVec = new THREE.Vector3(endNode.position.x, endNode.position.y, endNode.position.z);\n\n          // 计算中点并向外偏移\n          const midPoint = new THREE.Vector3().addVectors(startVec, endVec).multiplyScalar(0.5);\n          const distance = startVec.distanceTo(endVec);\n\n          // 计算法线方向（指向地球中心的反方向）\n          const normal = midPoint.clone().normalize();\n\n          // 控制点 - 根据连接的不同调整弯曲程度\n          const controlPoint = midPoint.clone().add(normal.multiplyScalar(distance * 0.5 + Math.random() * 0.3));\n\n          // 创建三次贝塞尔曲线以获得更平滑的弧线\n          const curve = new THREE.CubicBezierCurve3(startVec, startVec.clone().lerp(controlPoint, 0.3), endVec.clone().lerp(controlPoint, 0.3), endVec);\n          const points = curve.getPoints(100); // 增加点数以获得更平滑的曲线\n          const geometry = new THREE.BufferGeometry().setFromPoints(points);\n\n          // 使用渐变色材质\n          const colors = [];\n          const color1 = new THREE.Color(0x4A90FF); // 起始颜色\n          const color2 = new THREE.Color(0x00FFFF); // 中间颜色\n          const color3 = new THREE.Color(0x4A90FF); // 结束颜色\n\n          for (let i = 0; i < points.length; i++) {\n            const ratio = i / points.length;\n            let color;\n            if (ratio < 0.5) {\n              color = new THREE.Color().lerpColors(color1, color2, ratio * 2);\n            } else {\n              color = new THREE.Color().lerpColors(color2, color3, (ratio - 0.5) * 2);\n            }\n            colors.push(color.r, color.g, color.b);\n          }\n          geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));\n          const material = new THREE.LineBasicMaterial({\n            vertexColors: true,\n            transparent: true,\n            opacity: 0.8,\n            linewidth: 1.5\n          });\n          const line = new THREE.Line(geometry, material);\n          this.scene.add(line);\n\n          // 添加流动粒子效果\n          this.createFlowingParticles(curve, index);\n        }\n      });\n    },\n    createFlowingParticles(curve, curveIndex) {\n      // 为每条连接线创建流动粒子\n      const particleCount = 5 + Math.floor(Math.random() * 3); // 每条线上的粒子数量\n      const particles = new THREE.Group();\n      for (let i = 0; i < particleCount; i++) {\n        // 创建粒子几何体\n        const particleGeometry = new THREE.SphereGeometry(0.015, 8, 8);\n        const particleMaterial = new THREE.MeshBasicMaterial({\n          color: 0x00FFFF,\n          transparent: true,\n          opacity: 0.8\n        });\n        const particle = new THREE.Mesh(particleGeometry, particleMaterial);\n\n        // 随机初始位置\n        const initialProgress = Math.random();\n        const point = curve.getPoint(initialProgress);\n        particle.position.copy(point);\n\n        // 存储粒子数据\n        particle.userData = {\n          speed: 0.002 + Math.random() * 0.003,\n          // 随机速度\n          progress: initialProgress,\n          curve: curve\n        };\n        particles.add(particle);\n      }\n      this.scene.add(particles);\n\n      // 将粒子组添加到场景中以便在动画循环中更新\n      this.nodes.push({\n        isParticleGroup: true,\n        particles: particles.children,\n        curveIndex\n      });\n    },\n    createStars() {\n      // 创建多层星空背景，增强深度感\n      this.createStarLayer(3000, 2, 0.8, 2000); // 远景星空\n      this.createStarLayer(1000, 3, 0.9, 1000); // 中景星空\n      this.createStarLayer(500, 4, 1.0, 500); // 近景星空\n\n      // 添加一些彩色星星\n      this.createColoredStars();\n    },\n    createStarLayer(count, size, opacity, distance) {\n      const starsGeometry = new THREE.BufferGeometry();\n      const starsMaterial = new THREE.PointsMaterial({\n        color: 0xFFFFFF,\n        size: size,\n        transparent: true,\n        opacity: opacity,\n        sizeAttenuation: true // 启用大小衰减，远处的星星看起来更小\n      });\n\n      const starsVertices = [];\n      for (let i = 0; i < count; i++) {\n        const x = (Math.random() - 0.5) * distance;\n        const y = (Math.random() - 0.5) * distance;\n        const z = (Math.random() - 0.5) * distance;\n        starsVertices.push(x, y, z);\n      }\n      starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));\n      const stars = new THREE.Points(starsGeometry, starsMaterial);\n      this.scene.add(stars);\n      return stars;\n    },\n    createColoredStars() {\n      // 创建一些彩色星星，增加视觉多样性\n      const colors = [0x4A90FF, 0x44AAFF, 0x66CCFF, 0xFFFFFF, 0xFFEEDD, 0xFFDDCC];\n      colors.forEach(color => {\n        const starsGeometry = new THREE.BufferGeometry();\n        const starsMaterial = new THREE.PointsMaterial({\n          color: color,\n          size: 3 + Math.random() * 2,\n          transparent: true,\n          opacity: 0.7 + Math.random() * 0.3,\n          sizeAttenuation: true\n        });\n        const starsVertices = [];\n        const starCount = 50 + Math.floor(Math.random() * 100);\n        for (let i = 0; i < starCount; i++) {\n          const x = (Math.random() - 0.5) * 1000;\n          const y = (Math.random() - 0.5) * 1000;\n          const z = (Math.random() - 0.5) * 1000;\n          starsVertices.push(x, y, z);\n        }\n        starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));\n        const stars = new THREE.Points(starsGeometry, starsMaterial);\n        this.scene.add(stars);\n      });\n    },\n    animate() {\n      this.animationId = requestAnimationFrame(this.animate);\n      const time = Date.now() * 0.001; // 当前时间（秒）\n\n      // 旋转地球 - 平滑旋转\n      if (this.globe && this.autoRotate) {\n        // 添加轻微的上下摆动，模拟地球轴的倾斜\n        this.globe.rotation.y += this.rotationSpeed;\n        this.globe.rotation.x = Math.sin(time * 0.2) * 0.02;\n      }\n\n      // 节点和粒子动画\n      this.nodes.forEach((node, index) => {\n        // 处理粒子组\n        if (node.isParticleGroup) {\n          // 更新流动粒子\n          node.particles.forEach(particle => {\n            // 更新粒子位置\n            particle.userData.progress += particle.userData.speed;\n            if (particle.userData.progress > 1) {\n              particle.userData.progress = 0;\n            }\n\n            // 获取曲线上的新位置\n            const newPosition = particle.userData.curve.getPoint(particle.userData.progress);\n            particle.position.copy(newPosition);\n\n            // 粒子大小和不透明度脉动\n            const pulseFactor = 0.7 + 0.3 * Math.sin(time * 5 + index + particle.userData.progress * 10);\n            particle.scale.setScalar(pulseFactor);\n            particle.material.opacity = 0.6 + 0.4 * pulseFactor;\n          });\n          return;\n        }\n\n        // 处理常规节点\n        if (node.mesh) {\n          // 节点脉冲动画 - 更平滑的脉冲效果\n          const pulseSpeed = 1.5 + index * 0.2; // 不同节点有不同的脉冲速度\n          const scale = 1 + 0.3 * Math.sin(time * pulseSpeed + index);\n          node.mesh.scale.setScalar(scale);\n\n          // 光环旋转 - 不同节点有不同的旋转速度和方向\n          if (node.ring) {\n            node.ring.rotation.z += 0.01 + index * 0.002;\n          }\n\n          // 第二个光环反向旋转\n          if (node.outerRing) {\n            node.outerRing.rotation.z -= 0.015 + index * 0.001;\n          }\n\n          // 发光效果脉动\n          if (node.glow) {\n            const glowScale = 1 + 0.4 * Math.sin(time * 2 + index + Math.PI);\n            node.glow.scale.setScalar(glowScale);\n            node.glow.material.opacity = 0.3 + 0.2 * Math.sin(time * 3 + index);\n          }\n        }\n      });\n      this.renderer.render(this.scene, this.camera);\n    },\n    addEventListeners() {\n      window.addEventListener('resize', this.onWindowResize);\n      // 移除所有交互事件监听器\n    },\n\n    onWindowResize() {\n      const container = this.$refs.canvasContainer;\n      const width = container.clientWidth;\n      const height = container.clientHeight;\n      this.camera.aspect = width / height;\n      this.camera.updateProjectionMatrix();\n      this.renderer.setSize(width, height);\n    },\n    cleanup() {\n      if (this.animationId) {\n        cancelAnimationFrame(this.animationId);\n      }\n      window.removeEventListener('resize', this.onWindowResize);\n      if (this.renderer) {\n        this.renderer.dispose();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["THREE", "name", "data", "scene", "camera", "renderer", "globe", "nodes", "animationId", "rotationSpeed", "autoRotate", "isLoading", "computeNodes", "lat", "lng", "description", "gpuCount", "computePower", "mounted", "initThree", "createGlobe", "createNodes", "createStars", "animate", "addEventListeners", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "cleanup", "methods", "Scene", "container", "$refs", "canvasContainer", "width", "clientWidth", "height", "clientHeight", "PerspectiveCamera", "position", "z", "WebGLRenderer", "antialias", "alpha", "setSize", "setClearColor", "setPixelRatio", "window", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "add", "directionalLight", "DirectionalLight", "set", "backLight", "geometry", "SphereGeometry", "material", "MeshPhongMaterial", "color", "transparent", "opacity", "shininess", "specular", "wireframe", "<PERSON><PERSON>", "wireframeGeometry", "wireframeMaterial", "MeshBasicMaterial", "glowGeometry", "glowMaterial", "side", "FrontSide", "glow", "atmosphereGeometry", "atmosphereMaterial", "BackSide", "atmosphere", "outerAtmosphereGeometry", "outerAtmosphereMaterial", "outerAtmosphere", "for<PERSON>ach", "nodeData", "node", "createNode", "push", "mesh", "createConnections", "phi", "Math", "PI", "theta", "radius", "x", "sin", "cos", "y", "copy", "ringGeometry", "RingGeometry", "ringMaterial", "DoubleSide", "ring", "lookAt", "Vector3", "outerRingGeometry", "outerRingMaterial", "outerRing", "connections", "startIdx", "endIdx", "index", "startNode", "endNode", "startVec", "endVec", "midPoint", "addVectors", "multiplyScalar", "distance", "distanceTo", "normal", "clone", "normalize", "controlPoint", "random", "curve", "CubicBezierCurve3", "lerp", "points", "getPoints", "BufferGeometry", "setFromPoints", "colors", "color1", "Color", "color2", "color3", "i", "length", "ratio", "lerpColors", "r", "g", "b", "setAttribute", "Float32BufferAttribute", "LineBasicMaterial", "vertexColors", "linewidth", "line", "Line", "createFlowingParticles", "curveIndex", "particleCount", "floor", "particles", "Group", "particleGeometry", "particleMaterial", "particle", "initialProgress", "point", "getPoint", "userData", "speed", "progress", "isParticleGroup", "children", "createStarLayer", "createColoredStars", "count", "size", "starsGeometry", "starsMaterial", "PointsMaterial", "sizeAttenuation", "starsVertices", "stars", "Points", "starCount", "requestAnimationFrame", "time", "Date", "now", "rotation", "newPosition", "pulseFactor", "scale", "setScalar", "pulseSpeed", "glowScale", "render", "addEventListener", "onWindowResize", "aspect", "updateProjectionMatrix", "cancelAnimationFrame", "removeEventListener", "dispose"], "sources": ["src/components/common/Globe3D.vue"], "sourcesContent": ["<template>\n  <div class=\"globe-container\" ref=\"globeContainer\">\n    <div class=\"globe-canvas\" ref=\"canvasContainer\"></div>\n    <div class=\"loading-indicator\" v-if=\"isLoading\">\n      <div class=\"loading-spinner\"></div>\n      <div class=\"loading-text\">正在加载全球算力网络...</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as THREE from 'three'\n\nexport default {\n  name: 'Globe3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      globe: null,\n      nodes: [],\n      animationId: null,\n      rotationSpeed: 0.0025, // 降低旋转速度，使旋转更平滑\n      autoRotate: true, // 自动旋转控制\n      isLoading: true,\n      // 全球算力节点数据\n      computeNodes: [\n        {\n          name: '北京节点',\n          lat: 39.9042,\n          lng: 116.4074,\n          description: '华北地区主要算力中心',\n          gpuCount: '2000+',\n          computePower: '500 PFLOPS'\n        },\n        {\n          name: '上海节点',\n          lat: 31.2304,\n          lng: 121.4737,\n          description: '华东地区核心算力枢纽',\n          gpuCount: '1800+',\n          computePower: '450 PFLOPS'\n        },\n        {\n          name: '深圳节点',\n          lat: 22.3193,\n          lng: 114.1694,\n          description: '华南地区智算中心',\n          gpuCount: '1500+',\n          computePower: '380 PFLOPS'\n        },\n        {\n          name: '成都节点',\n          lat: 30.5728,\n          lng: 104.0668,\n          description: '西南地区算力基地',\n          gpuCount: '1200+',\n          computePower: '300 PFLOPS'\n        },\n        {\n          name: '杭州节点',\n          lat: 30.2741,\n          lng: 120.1551,\n          description: '长三角算力集群',\n          gpuCount: '1000+',\n          computePower: '250 PFLOPS'\n        },\n        {\n          name: '新加坡节点',\n          lat: 1.3521,\n          lng: 103.8198,\n          description: '东南亚算力中心',\n          gpuCount: '800+',\n          computePower: '200 PFLOPS'\n        }\n      ]\n    }\n  },\n\n  mounted() {\n    this.initThree()\n    this.createGlobe()\n    this.createNodes()\n    this.createStars()\n    this.animate()\n    this.addEventListeners()\n\n    // 延迟隐藏加载指示器\n    setTimeout(() => {\n      this.isLoading = false\n    }, 1500)\n  },\n  beforeDestroy() {\n    this.cleanup()\n  },\n  methods: {\n    initThree() {\n      // 创建场景\n      this.scene = new THREE.Scene()\n      \n      // 创建相机\n      const container = this.$refs.canvasContainer\n      const width = container.clientWidth\n      const height = container.clientHeight\n      \n      this.camera = new THREE.PerspectiveCamera(60, width / height, 0.1, 2000)\n      this.camera.position.z = 5.5 // 增加相机距离，使地球在视野中更完整\n\n      // 创建渲染器\n      this.renderer = new THREE.WebGLRenderer({\n        antialias: true,\n        alpha: true\n      })\n      this.renderer.setSize(width, height)\n      this.renderer.setClearColor(0x000814, 1) // 深蓝色背景，更符合太空感\n      this.renderer.setPixelRatio(window.devicePixelRatio) // 提高渲染质量\n      container.appendChild(this.renderer.domElement)\n      \n      // 添加环境光 - 增强亮度\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.8)\n      this.scene.add(ambientLight)\n      \n      // 添加方向光 - 模拟太阳光照\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2)\n      directionalLight.position.set(5, 3, 5)\n      this.scene.add(directionalLight)\n      \n      // 添加第二个方向光，增强背面照明\n      const backLight = new THREE.DirectionalLight(0x2277ff, 0.3)\n      backLight.position.set(-5, -3, -5)\n      this.scene.add(backLight)\n    },\n    \n    createGlobe() {\n      // 创建地球几何体 - 进一步增大尺寸\n      const geometry = new THREE.SphereGeometry(2.2, 96, 96) // 增大半径，提高分段数以获得更平滑的球体\n\n      // 创建地球材质 - 使用更真实的地球效果\n      const material = new THREE.MeshPhongMaterial({\n        color: 0x0077be, // 更深的蓝色\n        transparent: true,\n        opacity: 0.95,\n        shininess: 25, // 降低反光度，更自然\n        specular: 0x4488ff, // 添加蓝色高光\n        wireframe: false\n      })\n\n      this.globe = new THREE.Mesh(geometry, material)\n      this.scene.add(this.globe)\n\n      // 添加地球轮廓线 - 经纬线效果，更精细\n      const wireframeGeometry = new THREE.SphereGeometry(2.21, 48, 24) // 略大于地球本身\n      const wireframeMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4a90ff,\n        wireframe: true,\n        transparent: true,\n        opacity: 0.15 // 降低不透明度，更微妙\n      })\n      const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial)\n      this.scene.add(wireframe)\n\n      // 添加内部发光效果\n      const glowGeometry = new THREE.SphereGeometry(2.18, 64, 64)\n      const glowMaterial = new THREE.MeshBasicMaterial({\n        color: 0x0088ff,\n        transparent: true,\n        opacity: 0.05,\n        side: THREE.FrontSide\n      })\n      const glow = new THREE.Mesh(glowGeometry, glowMaterial)\n      this.scene.add(glow)\n\n      // 添加大气层效果 - 更明显的外发光\n      const atmosphereGeometry = new THREE.SphereGeometry(2.4, 64, 64) // 增大大气层\n      const atmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4a90ff,\n        transparent: true,\n        opacity: 0.12,\n        side: THREE.BackSide\n      })\n      const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial)\n      this.scene.add(atmosphere)\n      \n      // 添加第二层大气层，创造渐变效果\n      const outerAtmosphereGeometry = new THREE.SphereGeometry(2.6, 64, 64)\n      const outerAtmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470ff,\n        transparent: true,\n        opacity: 0.06,\n        side: THREE.BackSide\n      })\n      const outerAtmosphere = new THREE.Mesh(outerAtmosphereGeometry, outerAtmosphereMaterial)\n      this.scene.add(outerAtmosphere)\n    },\n    \n    createNodes() {\n      this.computeNodes.forEach(nodeData => {\n        const node = this.createNode(nodeData)\n        this.nodes.push(node)\n        this.scene.add(node.mesh)\n      })\n\n      // 创建节点间的连接线\n      this.createConnections()\n    },\n    \n    createNode(nodeData) {\n      // 将经纬度转换为3D坐标 - 调整节点位置以适应更大的地球\n      const phi = (90 - nodeData.lat) * (Math.PI / 180)\n      const theta = (nodeData.lng + 180) * (Math.PI / 180)\n\n      // 调整坐标以匹配新的地球尺寸\n      const radius = 2.28 // 略大于地球表面\n      const x = -(radius * Math.sin(phi) * Math.cos(theta))\n      const y = radius * Math.cos(phi)\n      const z = radius * Math.sin(phi) * Math.sin(theta)\n      \n      // 创建节点几何体 - 增大节点尺寸\n      const geometry = new THREE.SphereGeometry(0.035, 16, 16)\n      const material = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true,\n        opacity: 0.9\n      })\n      \n      const mesh = new THREE.Mesh(geometry, material)\n      mesh.position.set(x, y, z)\n      \n      // 创建内部发光效果\n      const glowGeometry = new THREE.SphereGeometry(0.04, 16, 16)\n      const glowMaterial = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true,\n        opacity: 0.4,\n        side: THREE.BackSide\n      })\n      const glow = new THREE.Mesh(glowGeometry, glowMaterial)\n      glow.position.copy(mesh.position)\n      this.scene.add(glow)\n      \n      // 创建光环效果 - 增大光环\n      const ringGeometry = new THREE.RingGeometry(0.05, 0.08, 32)\n      const ringMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4A90FF,\n        transparent: true,\n        opacity: 0.7,\n        side: THREE.DoubleSide\n      })\n      const ring = new THREE.Mesh(ringGeometry, ringMaterial)\n      ring.position.copy(mesh.position)\n      ring.lookAt(new THREE.Vector3(0, 0, 0))\n      this.scene.add(ring)\n      \n      // 创建第二个光环\n      const outerRingGeometry = new THREE.RingGeometry(0.09, 0.1, 32)\n      const outerRingMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        transparent: true,\n        opacity: 0.4,\n        side: THREE.DoubleSide\n      })\n      const outerRing = new THREE.Mesh(outerRingGeometry, outerRingMaterial)\n      outerRing.position.copy(mesh.position)\n      outerRing.lookAt(new THREE.Vector3(0, 0, 0))\n      this.scene.add(outerRing)\n      \n      return {\n        mesh,\n        ring,\n        outerRing,\n        glow,\n        data: nodeData,\n        position: { x, y, z }\n      }\n    },\n\n    createConnections() {\n      // 创建主要节点间的连接线\n      const connections = [\n        [0, 1], // 北京-上海\n        [1, 2], // 上海-深圳\n        [0, 3], // 北京-成都\n        [1, 4], // 上海-杭州\n        [2, 5], // 深圳-新加坡\n        [0, 4], // 北京-杭州\n        [3, 5]  // 成都-新加坡\n      ]\n\n      connections.forEach(([startIdx, endIdx], index) => {\n        const startNode = this.nodes[startIdx]\n        const endNode = this.nodes[endIdx]\n\n        if (startNode && endNode) {\n          // 计算控制点 - 使曲线更加弯曲\n          const startVec = new THREE.Vector3(startNode.position.x, startNode.position.y, startNode.position.z)\n          const endVec = new THREE.Vector3(endNode.position.x, endNode.position.y, endNode.position.z)\n          \n          // 计算中点并向外偏移\n          const midPoint = new THREE.Vector3().addVectors(startVec, endVec).multiplyScalar(0.5)\n          const distance = startVec.distanceTo(endVec)\n          \n          // 计算法线方向（指向地球中心的反方向）\n          const normal = midPoint.clone().normalize()\n          \n          // 控制点 - 根据连接的不同调整弯曲程度\n          const controlPoint = midPoint.clone().add(\n            normal.multiplyScalar(distance * 0.5 + Math.random() * 0.3)\n          )\n          \n          // 创建三次贝塞尔曲线以获得更平滑的弧线\n          const curve = new THREE.CubicBezierCurve3(\n            startVec,\n            startVec.clone().lerp(controlPoint, 0.3),\n            endVec.clone().lerp(controlPoint, 0.3),\n            endVec\n          )\n\n          const points = curve.getPoints(100) // 增加点数以获得更平滑的曲线\n          const geometry = new THREE.BufferGeometry().setFromPoints(points)\n          \n          // 使用渐变色材质\n          const colors = []\n          const color1 = new THREE.Color(0x4A90FF) // 起始颜色\n          const color2 = new THREE.Color(0x00FFFF) // 中间颜色\n          const color3 = new THREE.Color(0x4A90FF) // 结束颜色\n          \n          for (let i = 0; i < points.length; i++) {\n            const ratio = i / points.length\n            let color\n            \n            if (ratio < 0.5) {\n              color = new THREE.Color().lerpColors(color1, color2, ratio * 2)\n            } else {\n              color = new THREE.Color().lerpColors(color2, color3, (ratio - 0.5) * 2)\n            }\n            \n            colors.push(color.r, color.g, color.b)\n          }\n          \n          geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3))\n          \n          const material = new THREE.LineBasicMaterial({\n            vertexColors: true,\n            transparent: true,\n            opacity: 0.8,\n            linewidth: 1.5\n          })\n\n          const line = new THREE.Line(geometry, material)\n          this.scene.add(line)\n          \n          // 添加流动粒子效果\n          this.createFlowingParticles(curve, index)\n        }\n      })\n    },\n    \n    createFlowingParticles(curve, curveIndex) {\n      // 为每条连接线创建流动粒子\n      const particleCount = 5 + Math.floor(Math.random() * 3) // 每条线上的粒子数量\n      const particles = new THREE.Group()\n      \n      for (let i = 0; i < particleCount; i++) {\n        // 创建粒子几何体\n        const particleGeometry = new THREE.SphereGeometry(0.015, 8, 8)\n        const particleMaterial = new THREE.MeshBasicMaterial({\n          color: 0x00FFFF,\n          transparent: true,\n          opacity: 0.8\n        })\n        \n        const particle = new THREE.Mesh(particleGeometry, particleMaterial)\n        \n        // 随机初始位置\n        const initialProgress = Math.random()\n        const point = curve.getPoint(initialProgress)\n        particle.position.copy(point)\n        \n        // 存储粒子数据\n        particle.userData = {\n          speed: 0.002 + Math.random() * 0.003, // 随机速度\n          progress: initialProgress,\n          curve: curve\n        }\n        \n        particles.add(particle)\n      }\n      \n      this.scene.add(particles)\n      \n      // 将粒子组添加到场景中以便在动画循环中更新\n      this.nodes.push({\n        isParticleGroup: true,\n        particles: particles.children,\n        curveIndex\n      })\n    },\n\n    createStars() {\n      // 创建多层星空背景，增强深度感\n      this.createStarLayer(3000, 2, 0.8, 2000) // 远景星空\n      this.createStarLayer(1000, 3, 0.9, 1000) // 中景星空\n      this.createStarLayer(500, 4, 1.0, 500)   // 近景星空\n      \n      // 添加一些彩色星星\n      this.createColoredStars()\n    },\n    \n    createStarLayer(count, size, opacity, distance) {\n      const starsGeometry = new THREE.BufferGeometry()\n      const starsMaterial = new THREE.PointsMaterial({\n        color: 0xFFFFFF,\n        size: size,\n        transparent: true,\n        opacity: opacity,\n        sizeAttenuation: true // 启用大小衰减，远处的星星看起来更小\n      })\n\n      const starsVertices = []\n      for (let i = 0; i < count; i++) {\n        const x = (Math.random() - 0.5) * distance\n        const y = (Math.random() - 0.5) * distance\n        const z = (Math.random() - 0.5) * distance\n        starsVertices.push(x, y, z)\n      }\n\n      starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3))\n      const stars = new THREE.Points(starsGeometry, starsMaterial)\n      this.scene.add(stars)\n      \n      return stars\n    },\n    \n    createColoredStars() {\n      // 创建一些彩色星星，增加视觉多样性\n      const colors = [0x4A90FF, 0x44AAFF, 0x66CCFF, 0xFFFFFF, 0xFFEEDD, 0xFFDDCC]\n      \n      colors.forEach(color => {\n        const starsGeometry = new THREE.BufferGeometry()\n        const starsMaterial = new THREE.PointsMaterial({\n          color: color,\n          size: 3 + Math.random() * 2,\n          transparent: true,\n          opacity: 0.7 + Math.random() * 0.3,\n          sizeAttenuation: true\n        })\n\n        const starsVertices = []\n        const starCount = 50 + Math.floor(Math.random() * 100)\n        \n        for (let i = 0; i < starCount; i++) {\n          const x = (Math.random() - 0.5) * 1000\n          const y = (Math.random() - 0.5) * 1000\n          const z = (Math.random() - 0.5) * 1000\n          starsVertices.push(x, y, z)\n        }\n\n        starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3))\n        const stars = new THREE.Points(starsGeometry, starsMaterial)\n        this.scene.add(stars)\n      })\n    },\n    \n    animate() {\n      this.animationId = requestAnimationFrame(this.animate)\n\n      const time = Date.now() * 0.001 // 当前时间（秒）\n      \n      // 旋转地球 - 平滑旋转\n      if (this.globe && this.autoRotate) {\n        // 添加轻微的上下摆动，模拟地球轴的倾斜\n        this.globe.rotation.y += this.rotationSpeed\n        this.globe.rotation.x = Math.sin(time * 0.2) * 0.02\n      }\n      \n      // 节点和粒子动画\n      this.nodes.forEach((node, index) => {\n        // 处理粒子组\n        if (node.isParticleGroup) {\n          // 更新流动粒子\n          node.particles.forEach(particle => {\n            // 更新粒子位置\n            particle.userData.progress += particle.userData.speed\n            if (particle.userData.progress > 1) {\n              particle.userData.progress = 0\n            }\n            \n            // 获取曲线上的新位置\n            const newPosition = particle.userData.curve.getPoint(particle.userData.progress)\n            particle.position.copy(newPosition)\n            \n            // 粒子大小和不透明度脉动\n            const pulseFactor = 0.7 + 0.3 * Math.sin(time * 5 + index + particle.userData.progress * 10)\n            particle.scale.setScalar(pulseFactor)\n            particle.material.opacity = 0.6 + 0.4 * pulseFactor\n          })\n          return\n        }\n        \n        // 处理常规节点\n        if (node.mesh) {\n          // 节点脉冲动画 - 更平滑的脉冲效果\n          const pulseSpeed = 1.5 + index * 0.2 // 不同节点有不同的脉冲速度\n          const scale = 1 + 0.3 * Math.sin(time * pulseSpeed + index)\n          node.mesh.scale.setScalar(scale)\n          \n          // 光环旋转 - 不同节点有不同的旋转速度和方向\n          if (node.ring) {\n            node.ring.rotation.z += 0.01 + index * 0.002\n          }\n          \n          // 第二个光环反向旋转\n          if (node.outerRing) {\n            node.outerRing.rotation.z -= 0.015 + index * 0.001\n          }\n          \n          // 发光效果脉动\n          if (node.glow) {\n            const glowScale = 1 + 0.4 * Math.sin(time * 2 + index + Math.PI)\n            node.glow.scale.setScalar(glowScale)\n            node.glow.material.opacity = 0.3 + 0.2 * Math.sin(time * 3 + index)\n          }\n        }\n      })\n      \n      this.renderer.render(this.scene, this.camera)\n    },\n    \n    addEventListeners() {\n      window.addEventListener('resize', this.onWindowResize)\n      // 移除所有交互事件监听器\n    },\n    \n    onWindowResize() {\n      const container = this.$refs.canvasContainer\n      const width = container.clientWidth\n      const height = container.clientHeight\n\n      this.camera.aspect = width / height\n      this.camera.updateProjectionMatrix()\n      this.renderer.setSize(width, height)\n    },\n    \n    cleanup() {\n      if (this.animationId) {\n        cancelAnimationFrame(this.animationId)\n      }\n      \n      window.removeEventListener('resize', this.onWindowResize)\n      \n      if (this.renderer) {\n        this.renderer.dispose()\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.globe-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n\n.globe-canvas {\n  width: 100%;\n  height: 100%;\n  cursor: default;\n}\n\n.globe-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n  color: white;\n}\n\n.globe-title {\n  font-size: 32px;\n  font-weight: 600;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);\n}\n\n.globe-subtitle {\n  font-size: 18px;\n  opacity: 0.9;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n  margin-bottom: 20px;\n}\n\n.interaction-hint {\n  font-size: 14px;\n  opacity: 0.7;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 0.7; }\n  50% { opacity: 1; }\n}\n\n.loading-indicator {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  background: rgba(20, 112, 255, 0.1);\n  backdrop-filter: blur(5px);\n  color: white;\n  z-index: 10;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 3px solid rgba(255, 255, 255, 0.3);\n  border-top: 3px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 16px;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n}\n\n.node-info {\n  position: fixed;\n  background: rgba(20, 112, 255, 0.95);\n  color: white;\n  padding: 15px;\n  border-radius: 8px;\n  box-shadow: 0 4px 15px rgba(0,0,0,0.3);\n  pointer-events: none;\n  z-index: 1000;\n  min-width: 200px;\n  backdrop-filter: blur(10px);\n}\n\n.node-info h4 {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.node-info p {\n  margin: 0 0 10px 0;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.node-stats {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.node-stats span {\n  font-size: 12px;\n  opacity: 0.8;\n}\n\n@media (max-width: 768px) {\n  .globe-title {\n    font-size: 24px;\n  }\n\n  .globe-subtitle {\n    font-size: 16px;\n    margin-bottom: 15px;\n  }\n\n  .interaction-hint {\n    font-size: 12px;\n  }\n\n  .node-info {\n    min-width: 160px;\n    padding: 10px;\n    font-size: 12px;\n  }\n\n  .node-info h4 {\n    font-size: 14px;\n  }\n\n  .loading-text {\n    font-size: 14px;\n  }\n\n  .loading-spinner {\n    width: 40px;\n    height: 40px;\n    margin-bottom: 15px;\n  }\n}\n</style>\n"], "mappings": ";AAWA,YAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,KAAA;MACAC,MAAA;MACAC,QAAA;MACAC,KAAA;MACAC,KAAA;MACAC,WAAA;MACAC,aAAA;MAAA;MACAC,UAAA;MAAA;MACAC,SAAA;MACA;MACAC,YAAA,GACA;QACAX,IAAA;QACAY,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAhB,IAAA;QACAY,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAhB,IAAA;QACAY,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAhB,IAAA;QACAY,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAhB,IAAA;QACAY,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAhB,IAAA;QACAY,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA;IAEA;EACA;EAEAC,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,WAAA;IACA,KAAAC,WAAA;IACA,KAAAC,WAAA;IACA,KAAAC,OAAA;IACA,KAAAC,iBAAA;;IAEA;IACAC,UAAA;MACA,KAAAd,SAAA;IACA;EACA;EACAe,cAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAT,UAAA;MACA;MACA,KAAAhB,KAAA,OAAAH,KAAA,CAAA6B,KAAA;;MAEA;MACA,MAAAC,SAAA,QAAAC,KAAA,CAAAC,eAAA;MACA,MAAAC,KAAA,GAAAH,SAAA,CAAAI,WAAA;MACA,MAAAC,MAAA,GAAAL,SAAA,CAAAM,YAAA;MAEA,KAAAhC,MAAA,OAAAJ,KAAA,CAAAqC,iBAAA,KAAAJ,KAAA,GAAAE,MAAA;MACA,KAAA/B,MAAA,CAAAkC,QAAA,CAAAC,CAAA;;MAEA;MACA,KAAAlC,QAAA,OAAAL,KAAA,CAAAwC,aAAA;QACAC,SAAA;QACAC,KAAA;MACA;MACA,KAAArC,QAAA,CAAAsC,OAAA,CAAAV,KAAA,EAAAE,MAAA;MACA,KAAA9B,QAAA,CAAAuC,aAAA;MACA,KAAAvC,QAAA,CAAAwC,aAAA,CAAAC,MAAA,CAAAC,gBAAA;MACAjB,SAAA,CAAAkB,WAAA,MAAA3C,QAAA,CAAA4C,UAAA;;MAEA;MACA,MAAAC,YAAA,OAAAlD,KAAA,CAAAmD,YAAA;MACA,KAAAhD,KAAA,CAAAiD,GAAA,CAAAF,YAAA;;MAEA;MACA,MAAAG,gBAAA,OAAArD,KAAA,CAAAsD,gBAAA;MACAD,gBAAA,CAAAf,QAAA,CAAAiB,GAAA;MACA,KAAApD,KAAA,CAAAiD,GAAA,CAAAC,gBAAA;;MAEA;MACA,MAAAG,SAAA,OAAAxD,KAAA,CAAAsD,gBAAA;MACAE,SAAA,CAAAlB,QAAA,CAAAiB,GAAA;MACA,KAAApD,KAAA,CAAAiD,GAAA,CAAAI,SAAA;IACA;IAEApC,YAAA;MACA;MACA,MAAAqC,QAAA,OAAAzD,KAAA,CAAA0D,cAAA;;MAEA;MACA,MAAAC,QAAA,OAAA3D,KAAA,CAAA4D,iBAAA;QACAC,KAAA;QAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;QAAA;QACAC,QAAA;QAAA;QACAC,SAAA;MACA;MAEA,KAAA5D,KAAA,OAAAN,KAAA,CAAAmE,IAAA,CAAAV,QAAA,EAAAE,QAAA;MACA,KAAAxD,KAAA,CAAAiD,GAAA,MAAA9C,KAAA;;MAEA;MACA,MAAA8D,iBAAA,OAAApE,KAAA,CAAA0D,cAAA;MACA,MAAAW,iBAAA,OAAArE,KAAA,CAAAsE,iBAAA;QACAT,KAAA;QACAK,SAAA;QACAJ,WAAA;QACAC,OAAA;MACA;;MACA,MAAAG,SAAA,OAAAlE,KAAA,CAAAmE,IAAA,CAAAC,iBAAA,EAAAC,iBAAA;MACA,KAAAlE,KAAA,CAAAiD,GAAA,CAAAc,SAAA;;MAEA;MACA,MAAAK,YAAA,OAAAvE,KAAA,CAAA0D,cAAA;MACA,MAAAc,YAAA,OAAAxE,KAAA,CAAAsE,iBAAA;QACAT,KAAA;QACAC,WAAA;QACAC,OAAA;QACAU,IAAA,EAAAzE,KAAA,CAAA0E;MACA;MACA,MAAAC,IAAA,OAAA3E,KAAA,CAAAmE,IAAA,CAAAI,YAAA,EAAAC,YAAA;MACA,KAAArE,KAAA,CAAAiD,GAAA,CAAAuB,IAAA;;MAEA;MACA,MAAAC,kBAAA,OAAA5E,KAAA,CAAA0D,cAAA;MACA,MAAAmB,kBAAA,OAAA7E,KAAA,CAAAsE,iBAAA;QACAT,KAAA;QACAC,WAAA;QACAC,OAAA;QACAU,IAAA,EAAAzE,KAAA,CAAA8E;MACA;MACA,MAAAC,UAAA,OAAA/E,KAAA,CAAAmE,IAAA,CAAAS,kBAAA,EAAAC,kBAAA;MACA,KAAA1E,KAAA,CAAAiD,GAAA,CAAA2B,UAAA;;MAEA;MACA,MAAAC,uBAAA,OAAAhF,KAAA,CAAA0D,cAAA;MACA,MAAAuB,uBAAA,OAAAjF,KAAA,CAAAsE,iBAAA;QACAT,KAAA;QACAC,WAAA;QACAC,OAAA;QACAU,IAAA,EAAAzE,KAAA,CAAA8E;MACA;MACA,MAAAI,eAAA,OAAAlF,KAAA,CAAAmE,IAAA,CAAAa,uBAAA,EAAAC,uBAAA;MACA,KAAA9E,KAAA,CAAAiD,GAAA,CAAA8B,eAAA;IACA;IAEA7D,YAAA;MACA,KAAAT,YAAA,CAAAuE,OAAA,CAAAC,QAAA;QACA,MAAAC,IAAA,QAAAC,UAAA,CAAAF,QAAA;QACA,KAAA7E,KAAA,CAAAgF,IAAA,CAAAF,IAAA;QACA,KAAAlF,KAAA,CAAAiD,GAAA,CAAAiC,IAAA,CAAAG,IAAA;MACA;;MAEA;MACA,KAAAC,iBAAA;IACA;IAEAH,WAAAF,QAAA;MACA;MACA,MAAAM,GAAA,SAAAN,QAAA,CAAAvE,GAAA,KAAA8E,IAAA,CAAAC,EAAA;MACA,MAAAC,KAAA,IAAAT,QAAA,CAAAtE,GAAA,WAAA6E,IAAA,CAAAC,EAAA;;MAEA;MACA,MAAAE,MAAA;MACA,MAAAC,CAAA,KAAAD,MAAA,GAAAH,IAAA,CAAAK,GAAA,CAAAN,GAAA,IAAAC,IAAA,CAAAM,GAAA,CAAAJ,KAAA;MACA,MAAAK,CAAA,GAAAJ,MAAA,GAAAH,IAAA,CAAAM,GAAA,CAAAP,GAAA;MACA,MAAAnD,CAAA,GAAAuD,MAAA,GAAAH,IAAA,CAAAK,GAAA,CAAAN,GAAA,IAAAC,IAAA,CAAAK,GAAA,CAAAH,KAAA;;MAEA;MACA,MAAApC,QAAA,OAAAzD,KAAA,CAAA0D,cAAA;MACA,MAAAC,QAAA,OAAA3D,KAAA,CAAAsE,iBAAA;QACAT,KAAA;QACAC,WAAA;QACAC,OAAA;MACA;MAEA,MAAAyB,IAAA,OAAAxF,KAAA,CAAAmE,IAAA,CAAAV,QAAA,EAAAE,QAAA;MACA6B,IAAA,CAAAlD,QAAA,CAAAiB,GAAA,CAAAwC,CAAA,EAAAG,CAAA,EAAA3D,CAAA;;MAEA;MACA,MAAAgC,YAAA,OAAAvE,KAAA,CAAA0D,cAAA;MACA,MAAAc,YAAA,OAAAxE,KAAA,CAAAsE,iBAAA;QACAT,KAAA;QACAC,WAAA;QACAC,OAAA;QACAU,IAAA,EAAAzE,KAAA,CAAA8E;MACA;MACA,MAAAH,IAAA,OAAA3E,KAAA,CAAAmE,IAAA,CAAAI,YAAA,EAAAC,YAAA;MACAG,IAAA,CAAArC,QAAA,CAAA6D,IAAA,CAAAX,IAAA,CAAAlD,QAAA;MACA,KAAAnC,KAAA,CAAAiD,GAAA,CAAAuB,IAAA;;MAEA;MACA,MAAAyB,YAAA,OAAApG,KAAA,CAAAqG,YAAA;MACA,MAAAC,YAAA,OAAAtG,KAAA,CAAAsE,iBAAA;QACAT,KAAA;QACAC,WAAA;QACAC,OAAA;QACAU,IAAA,EAAAzE,KAAA,CAAAuG;MACA;MACA,MAAAC,IAAA,OAAAxG,KAAA,CAAAmE,IAAA,CAAAiC,YAAA,EAAAE,YAAA;MACAE,IAAA,CAAAlE,QAAA,CAAA6D,IAAA,CAAAX,IAAA,CAAAlD,QAAA;MACAkE,IAAA,CAAAC,MAAA,KAAAzG,KAAA,CAAA0G,OAAA;MACA,KAAAvG,KAAA,CAAAiD,GAAA,CAAAoD,IAAA;;MAEA;MACA,MAAAG,iBAAA,OAAA3G,KAAA,CAAAqG,YAAA;MACA,MAAAO,iBAAA,OAAA5G,KAAA,CAAAsE,iBAAA;QACAT,KAAA;QACAC,WAAA;QACAC,OAAA;QACAU,IAAA,EAAAzE,KAAA,CAAAuG;MACA;MACA,MAAAM,SAAA,OAAA7G,KAAA,CAAAmE,IAAA,CAAAwC,iBAAA,EAAAC,iBAAA;MACAC,SAAA,CAAAvE,QAAA,CAAA6D,IAAA,CAAAX,IAAA,CAAAlD,QAAA;MACAuE,SAAA,CAAAJ,MAAA,KAAAzG,KAAA,CAAA0G,OAAA;MACA,KAAAvG,KAAA,CAAAiD,GAAA,CAAAyD,SAAA;MAEA;QACArB,IAAA;QACAgB,IAAA;QACAK,SAAA;QACAlC,IAAA;QACAzE,IAAA,EAAAkF,QAAA;QACA9C,QAAA;UAAAyD,CAAA;UAAAG,CAAA;UAAA3D;QAAA;MACA;IACA;IAEAkD,kBAAA;MACA;MACA,MAAAqB,WAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;;MAEAA,WAAA,CAAA3B,OAAA,GAAA4B,QAAA,EAAAC,MAAA,GAAAC,KAAA;QACA,MAAAC,SAAA,QAAA3G,KAAA,CAAAwG,QAAA;QACA,MAAAI,OAAA,QAAA5G,KAAA,CAAAyG,MAAA;QAEA,IAAAE,SAAA,IAAAC,OAAA;UACA;UACA,MAAAC,QAAA,OAAApH,KAAA,CAAA0G,OAAA,CAAAQ,SAAA,CAAA5E,QAAA,CAAAyD,CAAA,EAAAmB,SAAA,CAAA5E,QAAA,CAAA4D,CAAA,EAAAgB,SAAA,CAAA5E,QAAA,CAAAC,CAAA;UACA,MAAA8E,MAAA,OAAArH,KAAA,CAAA0G,OAAA,CAAAS,OAAA,CAAA7E,QAAA,CAAAyD,CAAA,EAAAoB,OAAA,CAAA7E,QAAA,CAAA4D,CAAA,EAAAiB,OAAA,CAAA7E,QAAA,CAAAC,CAAA;;UAEA;UACA,MAAA+E,QAAA,OAAAtH,KAAA,CAAA0G,OAAA,GAAAa,UAAA,CAAAH,QAAA,EAAAC,MAAA,EAAAG,cAAA;UACA,MAAAC,QAAA,GAAAL,QAAA,CAAAM,UAAA,CAAAL,MAAA;;UAEA;UACA,MAAAM,MAAA,GAAAL,QAAA,CAAAM,KAAA,GAAAC,SAAA;;UAEA;UACA,MAAAC,YAAA,GAAAR,QAAA,CAAAM,KAAA,GAAAxE,GAAA,CACAuE,MAAA,CAAAH,cAAA,CAAAC,QAAA,SAAA9B,IAAA,CAAAoC,MAAA,UACA;;UAEA;UACA,MAAAC,KAAA,OAAAhI,KAAA,CAAAiI,iBAAA,CACAb,QAAA,EACAA,QAAA,CAAAQ,KAAA,GAAAM,IAAA,CAAAJ,YAAA,QACAT,MAAA,CAAAO,KAAA,GAAAM,IAAA,CAAAJ,YAAA,QACAT,MAAA,CACA;UAEA,MAAAc,MAAA,GAAAH,KAAA,CAAAI,SAAA;UACA,MAAA3E,QAAA,OAAAzD,KAAA,CAAAqI,cAAA,GAAAC,aAAA,CAAAH,MAAA;;UAEA;UACA,MAAAI,MAAA;UACA,MAAAC,MAAA,OAAAxI,KAAA,CAAAyI,KAAA;UACA,MAAAC,MAAA,OAAA1I,KAAA,CAAAyI,KAAA;UACA,MAAAE,MAAA,OAAA3I,KAAA,CAAAyI,KAAA;;UAEA,SAAAG,CAAA,MAAAA,CAAA,GAAAT,MAAA,CAAAU,MAAA,EAAAD,CAAA;YACA,MAAAE,KAAA,GAAAF,CAAA,GAAAT,MAAA,CAAAU,MAAA;YACA,IAAAhF,KAAA;YAEA,IAAAiF,KAAA;cACAjF,KAAA,OAAA7D,KAAA,CAAAyI,KAAA,GAAAM,UAAA,CAAAP,MAAA,EAAAE,MAAA,EAAAI,KAAA;YACA;cACAjF,KAAA,OAAA7D,KAAA,CAAAyI,KAAA,GAAAM,UAAA,CAAAL,MAAA,EAAAC,MAAA,GAAAG,KAAA;YACA;YAEAP,MAAA,CAAAhD,IAAA,CAAA1B,KAAA,CAAAmF,CAAA,EAAAnF,KAAA,CAAAoF,CAAA,EAAApF,KAAA,CAAAqF,CAAA;UACA;UAEAzF,QAAA,CAAA0F,YAAA,cAAAnJ,KAAA,CAAAoJ,sBAAA,CAAAb,MAAA;UAEA,MAAA5E,QAAA,OAAA3D,KAAA,CAAAqJ,iBAAA;YACAC,YAAA;YACAxF,WAAA;YACAC,OAAA;YACAwF,SAAA;UACA;UAEA,MAAAC,IAAA,OAAAxJ,KAAA,CAAAyJ,IAAA,CAAAhG,QAAA,EAAAE,QAAA;UACA,KAAAxD,KAAA,CAAAiD,GAAA,CAAAoG,IAAA;;UAEA;UACA,KAAAE,sBAAA,CAAA1B,KAAA,EAAAf,KAAA;QACA;MACA;IACA;IAEAyC,uBAAA1B,KAAA,EAAA2B,UAAA;MACA;MACA,MAAAC,aAAA,OAAAjE,IAAA,CAAAkE,KAAA,CAAAlE,IAAA,CAAAoC,MAAA;MACA,MAAA+B,SAAA,OAAA9J,KAAA,CAAA+J,KAAA;MAEA,SAAAnB,CAAA,MAAAA,CAAA,GAAAgB,aAAA,EAAAhB,CAAA;QACA;QACA,MAAAoB,gBAAA,OAAAhK,KAAA,CAAA0D,cAAA;QACA,MAAAuG,gBAAA,OAAAjK,KAAA,CAAAsE,iBAAA;UACAT,KAAA;UACAC,WAAA;UACAC,OAAA;QACA;QAEA,MAAAmG,QAAA,OAAAlK,KAAA,CAAAmE,IAAA,CAAA6F,gBAAA,EAAAC,gBAAA;;QAEA;QACA,MAAAE,eAAA,GAAAxE,IAAA,CAAAoC,MAAA;QACA,MAAAqC,KAAA,GAAApC,KAAA,CAAAqC,QAAA,CAAAF,eAAA;QACAD,QAAA,CAAA5H,QAAA,CAAA6D,IAAA,CAAAiE,KAAA;;QAEA;QACAF,QAAA,CAAAI,QAAA;UACAC,KAAA,UAAA5E,IAAA,CAAAoC,MAAA;UAAA;UACAyC,QAAA,EAAAL,eAAA;UACAnC,KAAA,EAAAA;QACA;QAEA8B,SAAA,CAAA1G,GAAA,CAAA8G,QAAA;MACA;MAEA,KAAA/J,KAAA,CAAAiD,GAAA,CAAA0G,SAAA;;MAEA;MACA,KAAAvJ,KAAA,CAAAgF,IAAA;QACAkF,eAAA;QACAX,SAAA,EAAAA,SAAA,CAAAY,QAAA;QACAf;MACA;IACA;IAEArI,YAAA;MACA;MACA,KAAAqJ,eAAA;MACA,KAAAA,eAAA;MACA,KAAAA,eAAA;;MAEA;MACA,KAAAC,kBAAA;IACA;IAEAD,gBAAAE,KAAA,EAAAC,IAAA,EAAA/G,OAAA,EAAA0D,QAAA;MACA,MAAAsD,aAAA,OAAA/K,KAAA,CAAAqI,cAAA;MACA,MAAA2C,aAAA,OAAAhL,KAAA,CAAAiL,cAAA;QACApH,KAAA;QACAiH,IAAA,EAAAA,IAAA;QACAhH,WAAA;QACAC,OAAA,EAAAA,OAAA;QACAmH,eAAA;MACA;;MAEA,MAAAC,aAAA;MACA,SAAAvC,CAAA,MAAAA,CAAA,GAAAiC,KAAA,EAAAjC,CAAA;QACA,MAAA7C,CAAA,IAAAJ,IAAA,CAAAoC,MAAA,YAAAN,QAAA;QACA,MAAAvB,CAAA,IAAAP,IAAA,CAAAoC,MAAA,YAAAN,QAAA;QACA,MAAAlF,CAAA,IAAAoD,IAAA,CAAAoC,MAAA,YAAAN,QAAA;QACA0D,aAAA,CAAA5F,IAAA,CAAAQ,CAAA,EAAAG,CAAA,EAAA3D,CAAA;MACA;MAEAwI,aAAA,CAAA5B,YAAA,iBAAAnJ,KAAA,CAAAoJ,sBAAA,CAAA+B,aAAA;MACA,MAAAC,KAAA,OAAApL,KAAA,CAAAqL,MAAA,CAAAN,aAAA,EAAAC,aAAA;MACA,KAAA7K,KAAA,CAAAiD,GAAA,CAAAgI,KAAA;MAEA,OAAAA,KAAA;IACA;IAEAR,mBAAA;MACA;MACA,MAAArC,MAAA;MAEAA,MAAA,CAAApD,OAAA,CAAAtB,KAAA;QACA,MAAAkH,aAAA,OAAA/K,KAAA,CAAAqI,cAAA;QACA,MAAA2C,aAAA,OAAAhL,KAAA,CAAAiL,cAAA;UACApH,KAAA,EAAAA,KAAA;UACAiH,IAAA,MAAAnF,IAAA,CAAAoC,MAAA;UACAjE,WAAA;UACAC,OAAA,QAAA4B,IAAA,CAAAoC,MAAA;UACAmD,eAAA;QACA;QAEA,MAAAC,aAAA;QACA,MAAAG,SAAA,QAAA3F,IAAA,CAAAkE,KAAA,CAAAlE,IAAA,CAAAoC,MAAA;QAEA,SAAAa,CAAA,MAAAA,CAAA,GAAA0C,SAAA,EAAA1C,CAAA;UACA,MAAA7C,CAAA,IAAAJ,IAAA,CAAAoC,MAAA;UACA,MAAA7B,CAAA,IAAAP,IAAA,CAAAoC,MAAA;UACA,MAAAxF,CAAA,IAAAoD,IAAA,CAAAoC,MAAA;UACAoD,aAAA,CAAA5F,IAAA,CAAAQ,CAAA,EAAAG,CAAA,EAAA3D,CAAA;QACA;QAEAwI,aAAA,CAAA5B,YAAA,iBAAAnJ,KAAA,CAAAoJ,sBAAA,CAAA+B,aAAA;QACA,MAAAC,KAAA,OAAApL,KAAA,CAAAqL,MAAA,CAAAN,aAAA,EAAAC,aAAA;QACA,KAAA7K,KAAA,CAAAiD,GAAA,CAAAgI,KAAA;MACA;IACA;IAEA7J,QAAA;MACA,KAAAf,WAAA,GAAA+K,qBAAA,MAAAhK,OAAA;MAEA,MAAAiK,IAAA,GAAAC,IAAA,CAAAC,GAAA;;MAEA;MACA,SAAApL,KAAA,SAAAI,UAAA;QACA;QACA,KAAAJ,KAAA,CAAAqL,QAAA,CAAAzF,CAAA,SAAAzF,aAAA;QACA,KAAAH,KAAA,CAAAqL,QAAA,CAAA5F,CAAA,GAAAJ,IAAA,CAAAK,GAAA,CAAAwF,IAAA;MACA;;MAEA;MACA,KAAAjL,KAAA,CAAA4E,OAAA,EAAAE,IAAA,EAAA4B,KAAA;QACA;QACA,IAAA5B,IAAA,CAAAoF,eAAA;UACA;UACApF,IAAA,CAAAyE,SAAA,CAAA3E,OAAA,CAAA+E,QAAA;YACA;YACAA,QAAA,CAAAI,QAAA,CAAAE,QAAA,IAAAN,QAAA,CAAAI,QAAA,CAAAC,KAAA;YACA,IAAAL,QAAA,CAAAI,QAAA,CAAAE,QAAA;cACAN,QAAA,CAAAI,QAAA,CAAAE,QAAA;YACA;;YAEA;YACA,MAAAoB,WAAA,GAAA1B,QAAA,CAAAI,QAAA,CAAAtC,KAAA,CAAAqC,QAAA,CAAAH,QAAA,CAAAI,QAAA,CAAAE,QAAA;YACAN,QAAA,CAAA5H,QAAA,CAAA6D,IAAA,CAAAyF,WAAA;;YAEA;YACA,MAAAC,WAAA,eAAAlG,IAAA,CAAAK,GAAA,CAAAwF,IAAA,OAAAvE,KAAA,GAAAiD,QAAA,CAAAI,QAAA,CAAAE,QAAA;YACAN,QAAA,CAAA4B,KAAA,CAAAC,SAAA,CAAAF,WAAA;YACA3B,QAAA,CAAAvG,QAAA,CAAAI,OAAA,eAAA8H,WAAA;UACA;UACA;QACA;;QAEA;QACA,IAAAxG,IAAA,CAAAG,IAAA;UACA;UACA,MAAAwG,UAAA,SAAA/E,KAAA;UACA,MAAA6E,KAAA,aAAAnG,IAAA,CAAAK,GAAA,CAAAwF,IAAA,GAAAQ,UAAA,GAAA/E,KAAA;UACA5B,IAAA,CAAAG,IAAA,CAAAsG,KAAA,CAAAC,SAAA,CAAAD,KAAA;;UAEA;UACA,IAAAzG,IAAA,CAAAmB,IAAA;YACAnB,IAAA,CAAAmB,IAAA,CAAAmF,QAAA,CAAApJ,CAAA,WAAA0E,KAAA;UACA;;UAEA;UACA,IAAA5B,IAAA,CAAAwB,SAAA;YACAxB,IAAA,CAAAwB,SAAA,CAAA8E,QAAA,CAAApJ,CAAA,YAAA0E,KAAA;UACA;;UAEA;UACA,IAAA5B,IAAA,CAAAV,IAAA;YACA,MAAAsH,SAAA,aAAAtG,IAAA,CAAAK,GAAA,CAAAwF,IAAA,OAAAvE,KAAA,GAAAtB,IAAA,CAAAC,EAAA;YACAP,IAAA,CAAAV,IAAA,CAAAmH,KAAA,CAAAC,SAAA,CAAAE,SAAA;YACA5G,IAAA,CAAAV,IAAA,CAAAhB,QAAA,CAAAI,OAAA,eAAA4B,IAAA,CAAAK,GAAA,CAAAwF,IAAA,OAAAvE,KAAA;UACA;QACA;MACA;MAEA,KAAA5G,QAAA,CAAA6L,MAAA,MAAA/L,KAAA,OAAAC,MAAA;IACA;IAEAoB,kBAAA;MACAsB,MAAA,CAAAqJ,gBAAA,gBAAAC,cAAA;MACA;IACA;;IAEAA,eAAA;MACA,MAAAtK,SAAA,QAAAC,KAAA,CAAAC,eAAA;MACA,MAAAC,KAAA,GAAAH,SAAA,CAAAI,WAAA;MACA,MAAAC,MAAA,GAAAL,SAAA,CAAAM,YAAA;MAEA,KAAAhC,MAAA,CAAAiM,MAAA,GAAApK,KAAA,GAAAE,MAAA;MACA,KAAA/B,MAAA,CAAAkM,sBAAA;MACA,KAAAjM,QAAA,CAAAsC,OAAA,CAAAV,KAAA,EAAAE,MAAA;IACA;IAEAR,QAAA;MACA,SAAAnB,WAAA;QACA+L,oBAAA,MAAA/L,WAAA;MACA;MAEAsC,MAAA,CAAA0J,mBAAA,gBAAAJ,cAAA;MAEA,SAAA/L,QAAA;QACA,KAAAA,QAAA,CAAAoM,OAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}