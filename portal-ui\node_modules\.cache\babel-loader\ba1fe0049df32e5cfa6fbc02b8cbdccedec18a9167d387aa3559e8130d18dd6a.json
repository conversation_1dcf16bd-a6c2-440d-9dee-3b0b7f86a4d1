{"ast": null, "code": "import Layout from \"@/components/common/layout-fee\";\nimport CommonPagination from \"@/views/Ordermange/CommonPagination\";\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\nimport { postAnyData, getAnyData } from \"@/api/login\";\nimport { getToken } from \"@/utils/auth\";\nexport default {\n  name: \"OrderView\",\n  components: {\n    SlideNotification,\n    Layout,\n    CommonPagination\n  },\n  data() {\n    return {\n      // General\n      currentSection: 'transactions',\n      pageSize: 6,\n      userBalance: 0,\n      showNotification: false,\n      notificationMessage: '',\n      notificationType: 'success',\n      // Orders\n      orderData: [],\n      orderSearchQuery: '',\n      currentPage: 1,\n      showOrderDetails: false,\n      selectedOrder: {},\n      orderLoading: false,\n      orderError: null,\n      // Transactions\n      transactionData: [],\n      transactionSearchQuery: '',\n      transactionDateRange: '30',\n      transactionType: '',\n      transactionPage: 1,\n      customDateStart: '',\n      customDateEnd: '',\n      transactionLoading: false,\n      transactionError: null,\n      summaryData: {\n        totalRecharge: 0,\n        totalExpense: 0,\n        balance: 0\n      },\n      // Usage\n      customUsageDateStart: '',\n      customUsageDateEnd: '',\n      usageData: [],\n      usageDateRange: '30',\n      usageFilterGpu: '',\n      usageLoading: false,\n      usageError: null,\n      usagePage: 1,\n      gpuModels: [{\n        id: 'a100',\n        name: 'NVIDIA A100'\n      }, {\n        id: 'v100',\n        name: 'NVIDIA V100'\n      }, {\n        id: 'rtx3090',\n        name: 'NVIDIA RTX 3090'\n      }],\n      // Recharge\n      rechargeAmounts: [100, 200, 500, 1000, 2000],\n      rechargeAmount: 100,\n      customRechargeAmount: null,\n      paymentMethod: 'alipay',\n      rechargeLoading: false\n    };\n  },\n  computed: {\n    // Orders\n    filteredOrderData() {\n      if (!this.orderSearchQuery) return this.orderData;\n      const query = this.orderSearchQuery.toLowerCase();\n      return this.orderData.filter(order => order.order_number.toLowerCase().includes(query));\n    },\n    paginatedOrders() {\n      const startIndex = (this.currentPage - 1) * this.pageSize;\n      const endIndex = startIndex + this.pageSize;\n      return this.filteredOrderData.slice(startIndex, endIndex);\n    },\n    currentOrderTotal() {\n      return this.filteredOrderData.length;\n    },\n    // Transactions\n    filteredTransactionData() {\n      let filtered = [...this.transactionData];\n\n      // Filter by date range\n      if (this.transactionDateRange !== 'custom') {\n        const days = parseInt(this.transactionDateRange);\n        const cutoffDate = new Date();\n        cutoffDate.setDate(cutoffDate.getDate() - days);\n        filtered = filtered.filter(transaction => {\n          const transactionDate = new Date(transaction.created_at);\n          return transactionDate >= cutoffDate;\n        });\n      } else if (this.customDateStart && this.customDateEnd) {\n        const startDate = new Date(this.customDateStart);\n        const endDate = new Date(this.customDateEnd);\n        endDate.setHours(23, 59, 59, 999); // End of the day\n\n        filtered = filtered.filter(transaction => {\n          const transactionDate = new Date(transaction.created_at);\n          return transactionDate >= startDate && transactionDate <= endDate;\n        });\n      }\n\n      // Filter by type\n      if (this.transactionType) {\n        filtered = filtered.filter(transaction => transaction.type === this.transactionType);\n      }\n\n      // Filter by search query\n      if (this.transactionSearchQuery) {\n        const query = this.transactionSearchQuery.toLowerCase();\n        filtered = filtered.filter(transaction => transaction.transaction_id.toLowerCase().includes(query));\n      }\n      return filtered;\n    },\n    paginatedTransactions() {\n      const startIndex = (this.transactionPage - 1) * this.pageSize;\n      const endIndex = startIndex + this.pageSize;\n      return this.filteredTransactionData.slice(startIndex, endIndex);\n    },\n    // usage\n    filteredUsageData() {\n      if (!this.usageData || this.usageData.length === 0) return [];\n      let filtered = [...this.usageData];\n\n      // 按日期范围过滤\n      if (this.usageDateRange !== 'custom') {\n        const days = parseInt(this.usageDateRange);\n        const cutoffDate = new Date();\n        cutoffDate.setDate(cutoffDate.getDate() - days);\n        filtered = filtered.filter(record => {\n          const recordDate = new Date(record.start_time || record.created_at);\n          return recordDate >= cutoffDate;\n        });\n      } else if (this.customUsageDateStart && this.customUsageDateEnd) {\n        const startDate = new Date(this.customUsageDateStart);\n        const endDate = new Date(this.customUsageDateEnd);\n        endDate.setHours(23, 59, 59, 999); // 设置为当天的最后一刻\n\n        filtered = filtered.filter(record => {\n          const recordDate = new Date(record.start_time || record.created_at);\n          return recordDate >= startDate && recordDate <= endDate;\n        });\n      }\n\n      // 按GPU型号过滤\n      if (this.usageFilterGpu) {\n        filtered = filtered.filter(record => record.gpu_model.toLowerCase().includes(this.usageFilterGpu.toLowerCase()));\n      }\n      return filtered;\n    },\n    paginatedUsageRecords() {\n      const startIndex = (this.usagePage - 1) * this.pageSize;\n      const endIndex = startIndex + this.pageSize;\n      return this.filteredUsageData.slice(startIndex, endIndex);\n    },\n    // Recharge\n    canRecharge() {\n      const amount = this.getRechargeAmount();\n      return amount && amount > 0 && this.paymentMethod && !this.rechargeLoading;\n    }\n  },\n  watch: {\n    $route(to) {\n      // 当路由变化时检查activeTab参数\n      const activeTab = to.query.activeTab;\n      if (activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(activeTab)) {\n        this.currentSection = activeTab;\n      }\n    },\n    currentSection(newVal) {\n      if (newVal === 'transactions') {\n        this.fetchTransactions();\n        this.fetchSummaryData();\n      } else if (newVal === 'orders') {\n        this.fetchOrders();\n      } else if (newVal === 'recharge') {\n        this.fetchUserBalance();\n      } else if (newVal === 'transactions') {\n        this.fetchTransactions();\n        // this.currentSection = 'transactions';\n        this.loadSectionData('transactions');\n      }\n    }\n  },\n  created() {\n    this.fetchUserBalance();\n    this.fetchTransactions();\n    const activeTab = this.$route.query.activeTab || 'transactions';\n    // console.log(\"activetab\",activeTab)\n    this.currentSection = activeTab;\n\n    // 根据当前激活的标签页加载对应数据\n    this.loadSectionData(activeTab);\n    this.$watch(() => this.$route.query, newQuery => {\n      if (newQuery.activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(newQuery.activeTab)) {\n        this.currentSection = newQuery.activeTab;\n      }\n    }, {\n      immediate: true\n    });\n    this.fetchOrders().then(() => {\n      // 默认按创建时间排序\n      this.sortBy('created_at');\n\n      // 检查URL中的activeTab参数\n      const activeTab = this.$route.query.activeTab;\n      if (activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(activeTab)) {\n        this.currentSection = activeTab;\n      }\n    });\n    this.fetchSummaryData();\n  },\n  beforeDestroy() {\n    this.$emit('refresh-header');\n  },\n  methods: {\n    showNotificationMessage(message, type = 'info') {\n      this.notificationMessage = message;\n      this.notificationType = type;\n      this.showNotification = true;\n    },\n    changeSection(section) {\n      // 更新当前标签页\n      this.currentSection = section;\n\n      // 更新 URL，确保刷新后仍然能恢复\n      this.$router.replace({\n        query: {\n          ...this.$route.query,\n          // 保留其他查询参数\n          activeTab: section // 更新 activeTab\n        }\n      });\n\n      // 加载对应数据\n      if (section === 'orders') {\n        this.fetchOrders();\n      } else if (section === 'transactions') {\n        this.fetchTransactions();\n        this.fetchSummaryData();\n      } else if (section === 'usage') {\n        this.fetchUsageData();\n      } else if (section === 'recharge') {\n        this.fetchUserBalance();\n      }\n    },\n    loadSectionData(section) {\n      switch (section) {\n        case 'orders':\n          this.fetchOrders();\n          break;\n        case 'transactions':\n          this.fetchTransactions();\n          this.fetchSummaryData();\n          break;\n        case 'usage':\n          this.fetchUsageData();\n          break;\n        case 'recharge':\n          this.fetchUserBalance();\n          break;\n      }\n    },\n    // 获取用户余额\n    async fetchUserBalance() {\n      try {\n        const response = await postAnyData(\"/logout/cilent/getInfo\");\n        if (response.data.code === 200) {\n          this.userBalance = response.data.data.balance || 0;\n          this.$emit('refresh-header');\n        }\n      } catch (error) {\n        this.$message.error('获取用户余额失败');\n      }\n    },\n    applyUsageCustomDateRange() {\n      if (!this.customUsageDateStart || !this.customUsageDateEnd) {\n        this.$message.error('请选择开始和结束日期');\n        return;\n      }\n      const startDate = new Date(this.customUsageDateStart);\n      const endDate = new Date(this.customUsageDateEnd);\n      if (startDate > endDate) {\n        this.$message.error('开始日期不能晚于结束日期');\n        return;\n      }\n      this.usagePage = 1; // 重置到第一页\n      this.fetchUsageData(); // 重新获取数据\n    },\n\n    // 格式化日期时间（带时区处理）\n    formatDateTimeusage(dateTimeString) {\n      if (!dateTimeString) return '--';\n      try {\n        const date = new Date(dateTimeString);\n        // 转换为本地时间字符串\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit',\n          second: '2-digit',\n          hour12: false\n        }).replace(/\\//g, '-');\n      } catch (e) {\n        return '--';\n      }\n    },\n    // 计算运行时长（带时区处理）\n    calculateDuration(startTime, endTime) {\n      if (!startTime) return \"0分钟\";\n      try {\n        const start = new Date(startTime);\n        const end = endTime ? new Date(endTime) : new Date();\n\n        // 检查时间是否有效\n        if (isNaN(start.getTime())) return \"时间无效\";\n        if (endTime && isNaN(end.getTime())) return \"时间无效\";\n\n        // 如果开始时间在未来\n        if (start > new Date()) {\n          return \"未开始\";\n        }\n\n        // 如果结束时间在未来（仍在运行中）\n        if (!endTime || end > new Date()) {\n          const diffMs = new Date() - start;\n          const hours = Math.floor(diffMs / (1000 * 60 * 60));\n          const minutes = Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n          return `${hours}小时${minutes}分钟 `;\n        }\n\n        // 正常计算时长\n        const diffMs = end - start;\n        const hours = Math.floor(diffMs / (1000 * 60 * 60));\n        const minutes = Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n        if (hours > 0) {\n          return `${hours}小时${minutes}分钟`;\n        }\n        return `${minutes}分钟`;\n      } catch (e) {\n        return \"--\";\n      }\n    },\n    navigateToRecharge() {\n      // 使用 replace 而不是 push 避免路由历史堆积\n      this.$router.replace({\n        path: '/userorder',\n        query: {\n          activeTab: 'recharge',\n          timestamp: Date.now() // 确保每次导航都是唯一的\n        }\n      });\n    },\n\n    // 从后端获取订单数据\n    async fetchOrders() {\n      this.orderLoading = true;\n      this.orderError = null;\n      try {\n        const response = await postAnyData(\"/system/order/getOrderAndProduct\");\n\n        // 添加排序逻辑\n        this.orderData = response.data.data.map(order => ({\n          id: order.name,\n          order_number: order.order_name,\n          payment_status: this.mapOrderStatus(order.order_staus),\n          payment_method: order.order_payment_method || '余额支付',\n          total_price: order.order_price,\n          unit_price: this.calculateUnitPrice(order),\n          duration: order.order_buy_time,\n          created_at: order.create_time,\n          gpu_model: order.name || '未知型号',\n          region: order.Region || '未知地区',\n          gpu_count: order.graphics_card_number || 0,\n          video_memory: order.video_memory || '未知',\n          cpu_cores: order.gpu_nuclear_number || 0,\n          system_disk: order.system_disk || '未知',\n          cloud_disk: order.data_disk || '未知',\n          memory: order.internal_memory || '未知',\n          rawData: order\n        })).sort((a, b) => {\n          // 按创建时间降序排序（最新的在前）\n          return new Date(b.created_at) - new Date(a.created_at);\n        });\n      } catch (error) {\n        this.orderError = '获取订单数据失败，请稍后重试';\n        // this.$message.error(this.orderError);\n      } finally {\n        this.orderLoading = false;\n      }\n    },\n    // 获取收支明细数据\n    async fetchTransactions() {\n      this.transactionLoading = true;\n      this.transactionError = null;\n      try {\n        const response = await postAnyData(\"/system/order/getConsumptionOrder\");\n        const rawData = response.data.data;\n\n        // 用map去重，确保不会因为相同order_id重复\n        const uniqueMap = new Map();\n        rawData.forEach(transaction => {\n          // 用topup_id或gpu_order_id作为唯一主键\n          const uniqueId = transaction.topup_id || transaction.gpu_order_id;\n          if (!uniqueMap.has(uniqueId)) {\n            uniqueMap.set(uniqueId, {\n              transaction_id: transaction.topup_order_id || transaction.gpu_order_name,\n              type: transaction.source_table === \"收入\" ? 'income' : 'expense',\n              pay_type: this.mapPayType(transaction.source_table),\n              amount: transaction.topup_topup || transaction.gpu_order_price,\n              created_at: transaction.create_time,\n              payment_channel: transaction.payment_method || '未知',\n              status: this.mapTransactionStatus(transaction.order_staus),\n              description: transaction.description || '无'\n            });\n          }\n        });\n        this.transactionData = Array.from(uniqueMap.values());\n      } catch (error) {\n        this.transactionLoading = '暂无记录';\n      } finally {\n        this.transactionLoading = false;\n      }\n    },\n    async fetchUsageData() {\n      this.usageLoading = true;\n      this.usageError = null;\n      try {\n        let params = {};\n\n        // 添加日期范围参数\n        if (this.usageDateRange === 'custom' && this.customUsageDateStart && this.customUsageDateEnd) {\n          params.start_date = this.customUsageDateStart;\n          params.end_date = this.customUsageDateEnd;\n        } else if (this.usageDateRange !== 'custom') {\n          // 处理预设日期范围\n          const days = parseInt(this.usageDateRange);\n          const cutoffDate = new Date();\n          cutoffDate.setDate(cutoffDate.getDate() - days);\n          params.start_date = cutoffDate.toISOString().split('T')[0];\n        }\n\n        // 添加GPU过滤参数\n        if (this.usageFilterGpu) {\n          params.gpu_model = this.usageFilterGpu;\n        }\n        // 这里应该改为实际的API调用，目前使用模拟数据\n        const response = await postAnyData(\"/system/order/getProductResumption\");\n\n        // 如果API已准备好，应该使用实际数据\n        // this.usageData = response.data.data;\n\n        // 临时使用模拟数据\n        const mockData = [];\n        this.usageData = mockData;\n        return this.usageData;\n      } catch (error) {\n        this.usageError = '加载使用记录失败，请稍后重试';\n        // this.$message.error(this.usageError);\n        return []; // 出错时返回空数组\n      } finally {\n        this.usageLoading = false;\n      }\n    },\n    // 获取汇总数据\n    // 获取汇总数据 (去重版)\n    async fetchSummaryData() {\n      try {\n        const response = await postAnyData(\"/system/order/getConsumptionOrder\");\n        const transactions = response.data.data || [];\n\n        // 先做去重，避免重复计算\n        const uniqueMap = new Map();\n        transactions.forEach(t => {\n          const uniqueId = t.topup_id || t.gpu_order_id;\n          if (!uniqueMap.has(uniqueId)) {\n            uniqueMap.set(uniqueId, t);\n          }\n        });\n        const uniqueTransactions = Array.from(uniqueMap.values());\n\n        // 计算总充值\n        const totalRecharge = uniqueTransactions.filter(t => t.source_table === \"收入\").reduce((sum, t) => sum + (parseFloat(t.topup_topup) || 0), 0);\n\n        // 计算总消费\n        const totalExpense = uniqueTransactions.filter(t => t.source_table === \"支出\").reduce((sum, t) => sum + (parseFloat(t.gpu_order_price) || 0), 0);\n        this.summaryData = {\n          totalRecharge,\n          totalExpense,\n          balance: this.userBalance\n        };\n      } catch (error) {\n        this.$message.error('获取账户汇总数据失败');\n      }\n    },\n    // 映射订单状态\n    mapOrderStatus(status) {\n      const statusMap = {\n        '1': 'paid',\n        // 支付成功\n        '2': 'failed',\n        // 支付失败\n        '3': 'unpaid' // 未支付\n      };\n\n      return statusMap[status] || status;\n    },\n    // 映射交易类型\n    mapPayType(type) {\n      return type === \"收入\" ? '充值' : '消费';\n    },\n    // 映射交易状态\n    mapTransactionStatus(status) {\n      const statusMap = {\n        '1': 'success',\n        // 成功\n        '2': 'failed',\n        // 失败\n        '3': 'processing' // 处理中\n      };\n\n      return statusMap[status] || status;\n    },\n    // 计算单价（根据商品信息和订单时长）\n    calculateUnitPrice(order) {\n      if (!order.computing) return order.order_price;\n\n      // 根据购买单位选择对应的价格\n      switch (order.order_unit) {\n        case 'hour':\n          return order.computing.price_bour;\n        case 'day':\n          return order.computing.price_day;\n        case 'month':\n          return order.computing.price_mouth;\n        case 'year':\n          return order.computing.price_year;\n        default:\n          return order.order_price;\n      }\n    },\n    // Formatting methods\n    formatPrice(price) {\n      if (isNaN(price)) return '0.00';\n      return parseFloat(price).toFixed(2);\n    },\n    formatDate(dateString) {\n      return new Date(dateString).toLocaleDateString();\n    },\n    formatDateTime(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleString();\n    },\n    // Order methods\n    getPaymentStatusClass(status) {\n      return {\n        'paid': 'status-success',\n        'unpaid': 'status-pending',\n        'refunded': 'status-info',\n        'failed': 'status-error',\n        'cancelled': 'status-warning'\n      }[status] || '';\n    },\n    getPaymentStatusText(status) {\n      return {\n        'paid': '已支付',\n        'unpaid': '未支付',\n        'refunded': '已退款',\n        'failed': '支付失败',\n        'cancelled': '已取消'\n      }[status] || status;\n    },\n    getPaymentMethodClass(method) {\n      return {\n        '支付宝': 'payment-alipay',\n        '微信支付': 'payment-wechat',\n        '银行卡': 'payment-bank',\n        '余额支付': 'payment-balance',\n        '未支付': 'payment-warning'\n      }[method] || '';\n    },\n    getPaymentMethodText(method) {\n      return {\n        'alipay': '支付宝',\n        'wechat': '微信支付',\n        'bank': '银行卡',\n        'balance': '余额'\n      }[method] || method;\n    },\n    searchOrders() {\n      this.currentPage = 1;\n    },\n    clearOrderSearch() {\n      this.orderSearchQuery = '';\n      this.currentPage = 1;\n    },\n    sortBy(field) {\n      if (field === 'created_at') {\n        this.orderData.sort((a, b) => {\n          return new Date(b[field]) - new Date(a[field]);\n        });\n      } else {\n        this.orderData.sort((a, b) => {\n          return a[field] > b[field] ? 1 : -1;\n        });\n      }\n    },\n    viewOrderDetails(order) {\n      this.selectedOrder = order;\n      this.showOrderDetails = true;\n      this.$nextTick(() => {\n        document.querySelector('.order-details').scrollIntoView({\n          behavior: 'smooth'\n        });\n      });\n    },\n    showOrderList() {\n      this.showOrderDetails = false;\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      });\n    },\n    goToPage(page) {\n      this.currentPage = page;\n    },\n    handlePageSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1;\n    },\n    // Transaction methods\n    searchTransactions() {\n      this.transactionPage = 1;\n    },\n    applyCustomDateRange() {\n      if (this.customDateStart && this.customDateEnd) {\n        this.transactionPage = 1;\n      }\n    },\n    getTransactionTypeClass(type) {\n      return {\n        'income': 'income-type',\n        'expense': 'expense-type',\n        'refund': 'refund-type'\n      }[type] || '';\n    },\n    getTransactionTypeName(type) {\n      return {\n        'income': '收入',\n        'expense': '支出',\n        'refund': '退款'\n      }[type] || type;\n    },\n    getTransactionTypeNamePay(payType) {\n      return {\n        'income': '收入',\n        'expense': '支出'\n      }[payType] || payType;\n    },\n    getTransactionStatusClass(status) {\n      return {\n        'success': 'status-success',\n        'pending': 'status-pending',\n        'failed': 'status-error',\n        'processing': 'status-info'\n      }[status] || '';\n    },\n    getTransactionStatusName(status) {\n      return {\n        'success': '成功',\n        'pending': '处理中',\n        'failed': '失败',\n        'processing': '处理中'\n      }[status] || status;\n    },\n    // Usage methods\n    getUsageStatusClass(status) {\n      return {\n        'active': 'status-success',\n        'running': 'status-running',\n        'about_to_expire': 'status-warning',\n        'expired': 'status-error',\n        'completed': 'status-complete',\n        'paused': 'status-info'\n      }[status] || '';\n    },\n    getUsageStatusText(status) {\n      return {\n        'active': '可用',\n        'running': '使用中',\n        'about_to_expire': '即将到期',\n        'completed': '已结束',\n        'paused': '已暂停'\n      }[status] || status;\n    },\n    renewService(record) {\n      this.$message.success(`正在为 ${record.gpu_model} 续费`);\n    },\n    // Recharge methods\n    getRechargeAmount() {\n      return this.rechargeAmount || this.customRechargeAmount;\n    },\n    handleCustomAmountInput() {\n      this.rechargeAmount = null;\n    },\n    async submitRecharge() {\n      const amount = this.getRechargeAmount();\n      if (!amount || amount <= 0) {\n        // this.$message.error('请输入有效的充值金额');\n        this.showNotificationMessage('请输入有效的充值金额', 'error');\n        return;\n      }\n      this.rechargeLoading = true;\n      try {\n        // 调用充值API\n        const response = await getAnyData('/yun/scanPay', {\n          amount: amount\n        });\n        if (response.data.includes('充值金额不足')) {\n          // 提取需要充值的金额（正则表达式匹配数字和小数点）\n          this.showNotificationMessage(response.data, 'error');\n          return;\n        }\n        // 处理支付表单\n        const tempDiv = document.createElement('div');\n        tempDiv.innerHTML = response.data;\n        document.body.appendChild(tempDiv);\n        const form = tempDiv.querySelector('form');\n\n        // console.log(response)\n\n        if (form) {\n          this.showNotificationMessage('正在跳转到支付页面...', 'success');\n          form.target = '_blank'; // 设置表单提交目标为新窗口\n          form.submit();\n          // this.$message.success('正在跳转到支付页面...');\n\n          // 支付成功后刷新余额和交易记录\n          setTimeout(() => {\n            this.fetchUserBalance();\n            this.fetchTransactions();\n          }, 3000);\n        } else {\n          this.showNotificationMessage('支付表单生成失败', 'error');\n        }\n      } catch (error) {\n        this.showNotificationMessage('充值请求失败，请稍后重试', 'error');\n      } finally {\n        this.rechargeLoading = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "CommonPagination", "SlideNotification", "postAnyData", "getAnyData", "getToken", "name", "components", "data", "currentSection", "pageSize", "userBalance", "showNotification", "notificationMessage", "notificationType", "orderData", "orderSearchQuery", "currentPage", "showOrderDetails", "<PERSON><PERSON><PERSON><PERSON>", "orderLoading", "orderError", "transactionData", "transactionSearchQuery", "transactionDateRange", "transactionType", "transactionPage", "customDateStart", "customDateEnd", "transactionLoading", "transactionError", "summaryData", "totalRecharge", "totalExpense", "balance", "customUsageDateStart", "customUsageDateEnd", "usageData", "usageDateRange", "usageFilterGpu", "usageLoading", "usageError", "usagePage", "gpuModels", "id", "rechargeAmounts", "rechargeAmount", "customRechargeAmount", "paymentMethod", "rechargeLoading", "computed", "filteredOrderData", "query", "toLowerCase", "filter", "order", "order_number", "includes", "paginatedOrders", "startIndex", "endIndex", "slice", "currentOrderTotal", "length", "filteredTransactionData", "filtered", "days", "parseInt", "cutoffDate", "Date", "setDate", "getDate", "transaction", "transactionDate", "created_at", "startDate", "endDate", "setHours", "type", "transaction_id", "paginatedTransactions", "filteredUsageData", "record", "recordDate", "start_time", "gpu_model", "paginatedUsageRecords", "can<PERSON>ech<PERSON><PERSON>", "amount", "getRechargeAmount", "watch", "$route", "to", "activeTab", "newVal", "fetchTransactions", "fetchSummaryData", "fetchOrders", "fetchUserBalance", "loadSectionData", "created", "$watch", "<PERSON><PERSON><PERSON><PERSON>", "immediate", "then", "sortBy", "<PERSON><PERSON><PERSON><PERSON>", "$emit", "methods", "showNotificationMessage", "message", "changeSection", "section", "$router", "replace", "fetchUsageData", "response", "code", "error", "$message", "applyUsageCustomDateRange", "formatDateTimeusage", "dateTimeString", "date", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "hour12", "e", "calculateDuration", "startTime", "endTime", "start", "end", "isNaN", "getTime", "diffMs", "hours", "Math", "floor", "minutes", "navigateToRecharge", "path", "timestamp", "now", "map", "order_name", "payment_status", "mapOrderStatus", "order_staus", "payment_method", "order_payment_method", "total_price", "order_price", "unit_price", "calculateUnitPrice", "duration", "order_buy_time", "create_time", "region", "Region", "gpu_count", "graphics_card_number", "video_memory", "cpu_cores", "gpu_nuclear_number", "system_disk", "cloud_disk", "data_disk", "memory", "internal_memory", "rawData", "sort", "a", "b", "uniqueMap", "Map", "for<PERSON>ach", "uniqueId", "topup_id", "gpu_order_id", "has", "set", "topup_order_id", "gpu_order_name", "source_table", "pay_type", "mapPayType", "topup_topup", "gpu_order_price", "payment_channel", "status", "mapTransactionStatus", "description", "Array", "from", "values", "params", "start_date", "end_date", "toISOString", "split", "mockData", "transactions", "t", "uniqueTransactions", "reduce", "sum", "parseFloat", "statusMap", "computing", "order_unit", "price_bour", "price_day", "price_mouth", "price_year", "formatPrice", "price", "toFixed", "formatDate", "dateString", "toLocaleDateString", "formatDateTime", "getPaymentStatusClass", "getPaymentStatusText", "getPaymentMethodClass", "method", "getPaymentMethodText", "searchOrders", "clearOrderSearch", "field", "viewOrderDetails", "$nextTick", "document", "querySelector", "scrollIntoView", "behavior", "showOrderList", "window", "scrollTo", "top", "goToPage", "page", "handlePageSizeChange", "size", "searchTransactions", "applyCustomDateRange", "getTransactionTypeClass", "getTransactionTypeName", "getTransactionTypeNamePay", "payType", "getTransactionStatusClass", "getTransactionStatusName", "getUsageStatusClass", "getUsageStatusText", "renewService", "success", "handleCustomAmountInput", "submit<PERSON>echarge", "tempDiv", "createElement", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "form", "target", "submit", "setTimeout"], "sources": ["src/views/Ordermange/OrderView.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <SlideNotification\r\n        v-if=\"showNotification\"\r\n        :message=\"notificationMessage\"\r\n        :type=\"notificationType\"\r\n        :duration=\"3000\"\r\n        :minHeight= minHeight\r\n        @close=\"showNotification = false\"\r\n    />\r\n    <div style=\"width: 100%\">\r\n      <div class=\"fee-center-container\">\r\n        <div class=\"navigation-sidebar\">\r\n          <h2 class=\"nav-title\">费用中心</h2>\r\n          <ul class=\"nav-list\">\r\n\r\n            <li class=\"nav-item\" :class=\"{ active: currentSection === 'transactions' }\">\r\n              <a href=\"#\" @click.prevent=\"changeSection('transactions')\">\r\n                <i class=\"el-icon-money\"></i>\r\n                <span>收支明细</span>\r\n              </a>\r\n            </li>\r\n\r\n            <li class=\"nav-item\" :class=\"{ active: currentSection === 'recharge' }\">\r\n              <a href=\"#\" @click.prevent=\"changeSection('recharge')\">\r\n                <i class=\"el-icon-wallet\"></i>\r\n                <span>充值</span>\r\n              </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n\r\n        <!-- Main Content Area -->\r\n        <div class=\"main-content\">\r\n          <!-- Orders Management Section -->\r\n          <div v-if=\"currentSection === 'orders'\">\r\n            <!-- Order List View -->\r\n            <div class=\"tab-content\" v-show=\"!showOrderDetails\">\r\n              <div class=\"search-section\">\r\n                <div class=\"search-bar\">\r\n                  <input type=\"text\" placeholder=\"搜索订单号\" v-model=\"orderSearchQuery\" />\r\n                  <button class=\"search-button\" @click=\"searchOrders\">\r\n                    <i class=\"el-icon-search\"></i>\r\n                  </button>\r\n                  <button v-if=\"orderSearchQuery\" class=\"clear-button\" @click=\"clearOrderSearch\">\r\n                    <i class=\"el-icon-close\"></i>\r\n                  </button>\r\n                </div>\r\n                <div class=\"currency-display\">\r\n                  金额单位: ¥\r\n                  <span class=\"flow-count\">订单总数: {{ currentOrderTotal }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"table-container\">\r\n                <div v-if=\"orderLoading\" class=\"loading-state\">\r\n                  <i class=\"el-icon-loading\"></i>\r\n                  <span>正在加载订单数据...</span>\r\n                </div>\r\n\r\n                <div v-if=\"orderError\" class=\"error-state\">\r\n                  <i class=\"el-icon-error\"></i>\r\n                  <span>{{ orderError }}</span>\r\n                  <button @click=\"fetchOrders\">重试</button>\r\n                </div>\r\n                <table class=\"data-table\">\r\n                  <thead>\r\n                  <tr>\r\n                    <th>订单号 <i class=\"el-icon-sort\" @click=\"sortBy('order_number')\"></i></th>\r\n                    <th>订单创建时间 <i class=\"el-icon-sort\" @click=\"sortBy('created_at')\"></i></th>\r\n                    <th>支付状态 <i class=\"el-icon-sort\" @click=\"sortBy('payment_status')\"></i></th>\r\n                    <th>单价 <i class=\"el-icon-sort\" @click=\"sortBy('unit_price')\"></i></th>\r\n                    <th>时长 <i class=\"el-icon-sort\" @click=\"sortBy('duration')\"></i></th>\r\n                    <th>付款方式 <i class=\"el-icon-sort\" @click=\"sortBy('payment_method')\"></i></th>\r\n                    <th>合计</th>\r\n                    <th>操作</th>\r\n                  </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                  <tr v-for=\"(order, index) in paginatedOrders\" :key=\"'order-'+index\">\r\n                    <td>{{ order.order_number }}</td>\r\n                    <td>{{ formatDateTime(order.created_at) }}</td>\r\n                    <td>\r\n                      <span class=\"status-tag\" :class=\"getPaymentStatusClass(order.payment_status)\">\r\n                        {{ getPaymentStatusText(order.payment_status) }}\r\n                      </span>\r\n                    </td>\r\n                    <td>{{ formatPrice(order.unit_price) }}</td>\r\n                    <td>{{ order.duration }}</td>\r\n                    <td>\r\n                      <span class=\"payment-method-tag\" :class=\"getPaymentMethodClass(order.payment_method)\">\r\n                        {{ getPaymentMethodText(order.payment_method) }}\r\n                      </span>\r\n                    </td>\r\n                    <td>{{ formatPrice(order.total_price) }}</td>\r\n                    <td>\r\n                      <span class=\"operation-link\" @click=\"viewOrderDetails(order)\">查看详情</span>\r\n                    </td>\r\n                  </tr>\r\n                  <tr v-if=\"filteredOrderData.length === 0 && !orderLoading\">\r\n                    <td colspan=\"8\" class=\"empty-state\">\r\n                      <div class=\"empty-container\">\r\n                        <i class=\"el-icon-document empty-icon\"></i>\r\n                        <div class=\"empty-text\">没有找到匹配的订单</div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n\r\n              <common-pagination\r\n                  :current-page=\"currentPage\"\r\n                  :total=\"filteredOrderData.length\"\r\n                  :page-size=\"pageSize\"\r\n                  @change-page=\"goToPage\"\r\n                  @change-page-size=\"handlePageSizeChange\"\r\n              />\r\n            </div>\r\n\r\n            <!-- Order Details View -->\r\n            <div class=\"order-details\" v-show=\"showOrderDetails\">\r\n              <div class=\"detail-card\">\r\n                <div class=\"detail-header\">\r\n                  <h2 class=\"detail-title\">订单详情</h2>\r\n                  <button class=\"back-button\" @click=\"showOrderList\">\r\n                    <i class=\"el-icon-back\"></i> 返回列表\r\n                  </button>\r\n                </div>\r\n\r\n                <div class=\"detail-content\">\r\n                  <h3 class=\"detail-subtitle\">订单概况</h3>\r\n                  <div class=\"detail-section\">\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">订单号:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.order_number }}</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">订单状态:</div>\r\n                        <div class=\"detail-value\">\r\n                          <span class=\"status-tag\" :class=\"getPaymentStatusClass(selectedOrder.payment_status)\">\r\n                            {{ getPaymentStatusText(selectedOrder.payment_status) }}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">支付方式:</div>\r\n                        <div class=\"detail-value\">\r\n                          <span class=\"payment-method-tag\" :class=\"getPaymentMethodClass(selectedOrder.payment_method)\">\r\n                            {{ getPaymentMethodText(selectedOrder.payment_method) }}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">单价:</div>\r\n                        <div class=\"detail-value\">¥ {{ formatPrice(selectedOrder.unit_price) }}</div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">订单创建时间:</div>\r\n                        <div class=\"detail-value\">{{ formatDateTime(selectedOrder.created_at) }}</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">订单金额:</div>\r\n                        <div class=\"detail-value\">¥ {{ formatPrice(selectedOrder.total_price) }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <h3 class=\"detail-subtitle\">GPU信息</h3>\r\n                  <div class=\"detail-section\">\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">型号:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.gpu_model }}</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">地区:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.region }}</div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">显卡数量:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.gpu_count }} 个</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">显存:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.video_memory }} GB</div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">VCPU核数:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.cpu_cores }} 核</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">系统盘:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.system_disk }} SSD</div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">云盘:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.cloud_disk }} GB</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">内存:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.memory }} GB</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Transactions Section -->\r\n          <div v-if=\"currentSection === 'transactions'\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"search-section\">\r\n                <div class=\"date-range-picker\">\r\n                  <select v-model=\"transactionDateRange\">\r\n                    <option value=\"7\">最近7天</option>\r\n                    <option value=\"30\">最近一个月</option>\r\n                    <option value=\"90\">最近三个月</option>\r\n                    <option value=\"custom\">自定义时间段</option>\r\n                  </select>\r\n\r\n                  <div v-if=\"transactionDateRange === 'custom'\" class=\"custom-date-range\">\r\n                    <input type=\"date\" v-model=\"customDateStart\" />\r\n                    <span>至</span>\r\n                    <input type=\"date\" v-model=\"customDateEnd\" />\r\n<!--                    <button class=\"apply-button\" @click=\"applyCustomDateRange\">确认</button>-->\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"search-filters\">\r\n                  <select v-model=\"transactionType\">\r\n                    <option value=\"\">全部类型</option>\r\n                    <option value=\"income\">收入</option>\r\n                    <option value=\"expense\">支出</option>\r\n                  </select>\r\n\r\n                  <input\r\n                      type=\"text\"\r\n                      placeholder=\"搜索流水号\"\r\n                      v-model=\"transactionSearchQuery\"\r\n                      @keyup.enter=\"searchTransactions\"\r\n                  />\r\n                  <button class=\"search-button\" @click=\"searchTransactions\">\r\n                    <i class=\"el-icon-search\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"transaction-summary\">\r\n                <div class=\"summary-card\">\r\n                  <div class=\"summary-title\">总充值</div>\r\n                  <div class=\"summary-value income\">¥ {{ formatPrice(summaryData.totalRecharge) }}</div>\r\n                </div>\r\n                <div class=\"summary-card\">\r\n                  <div class=\"summary-title\">总消费</div>\r\n                  <div class=\"summary-value expense\">¥ {{ formatPrice(summaryData.totalExpense) }}</div>\r\n                </div>\r\n                <div class=\"summary-card\">\r\n                  <div class=\"summary-title\">账户余额</div>\r\n                  <div class=\"summary-value\">¥ {{ formatPrice(userBalance) }}</div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"table-container\">\r\n                <div v-if=\"transactionLoading\" class=\"loading-state\">\r\n                  <i class=\"el-icon-loading\"></i>\r\n                  <span>正在加载交易数据...</span>\r\n                </div>\r\n\r\n                <div v-if=\"transactionError\" class=\"error-state\">\r\n                  <i class=\"el-icon-error\"></i>\r\n                  <span>{{ transactionError }}</span>\r\n                  <button @click=\"fetchTransactions\">重试</button>\r\n                </div>\r\n\r\n                <table class=\"data-table\">\r\n                  <thead>\r\n                  <tr>\r\n                    <th>流水号</th>\r\n                    <th>交易时间</th>\r\n                    <th>收支类型</th>\r\n                    <th>交易类型</th>\r\n                    <th>金额</th>\r\n                    <th>交易渠道</th>\r\n                    <th>备注</th>\r\n                  </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                  <tr v-for=\"(transaction, index) in paginatedTransactions\" :key=\"'transaction-'+index\">\r\n                    <td>{{ transaction.transaction_id }}</td>\r\n                    <td>{{ formatDateTime(transaction.created_at) }}</td>\r\n                    <td>\r\n                      <span class=\"transaction-type\" :class=\"getTransactionTypeClass(transaction.type)\">\r\n                        {{ getTransactionTypeName(transaction.type) }}\r\n                      </span>\r\n                    </td>\r\n                    <td>{{ getTransactionTypeNamePay(transaction.pay_type) }}</td>\r\n                    <td :class=\"transaction.type === 'expense' ? 'expense-amount' : 'income-amount'\">\r\n                      {{ transaction.type === 'expense' ? '￥ - ' : '￥ + ' }}{{ formatPrice(transaction.amount) }}\r\n                    </td>\r\n\r\n                    <td :class=\"transaction.type === 'expense' ? 'expense-zhifu' : 'income-shouru'\">\r\n                      {{ getPaymentMethodText(transaction.payment_channel) }}\r\n                    </td>\r\n                    <td>{{ transaction.description }}</td>\r\n                  </tr>\r\n                  <tr v-if=\"filteredTransactionData.length === 0 && !transactionLoading\">\r\n                    <td colspan=\"8\" class=\"empty-state\">\r\n                      <div class=\"empty-container\">\r\n                        <i class=\"el-icon-money empty-icon\"></i>\r\n                        <div class=\"empty-text\">没有找到匹配的交易记录</div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n\r\n              <common-pagination\r\n                  :current-page=\"transactionPage\"\r\n                  :total=\"filteredTransactionData.length\"\r\n                  :page-size=\"pageSize\"\r\n                  @change-page=\"(page) => transactionPage = page\"\r\n                  @change-page-size=\"(size) => { pageSize = size; transactionPage = 1; }\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Usage Records Section -->\r\n          <div v-if=\"currentSection === 'usage'\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"search-section\">\r\n                <div class=\"date-range-picker\">\r\n                  <select v-model=\"usageDateRange\">\r\n                    <option value=\"7\">最近7天</option>\r\n                    <option value=\"30\">最近一个月</option>\r\n                    <option value=\"90\">最近三个月</option>\r\n                    <option value=\"custom\">自定义时间段</option>\r\n                  </select>\r\n\r\n                  <div v-if=\"usageDateRange === 'custom'\" class=\"custom-date-range\">\r\n                    <input type=\"date\" v-model=\"customUsageDateStart\" />\r\n                    <span>至</span>\r\n                    <input type=\"date\" v-model=\"customUsageDateEnd\" />\r\n<!--                    <button class=\"apply-button\" @click=\"applyUsageCustomDateRange\">确认</button>-->\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"search-filters\">\r\n                  <select v-model=\"usageFilterGpu\">\r\n                    <option value=\"\">全部GPU型号</option>\r\n                    <option v-for=\"gpu in gpuModels\" :key=\"gpu.id\" :value=\"gpu.id\">\r\n                      {{ gpu.name }}\r\n                    </option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"table-container\">\r\n                <table class=\"data-table\">\r\n                  <thead>\r\n                  <tr>\r\n                    <th>GPU型号</th>\r\n                    <th>状态</th>\r\n                    <th>开始时间</th>\r\n                    <th>结束时间</th>\r\n                    <th>使用时长</th>\r\n                    <th>计费金额</th>\r\n                    <th>操作</th>\r\n                  </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                  <tr v-for=\"(record, index) in paginatedUsageRecords\" :key=\"'usage-'+index\">\r\n                    <td>{{ record.gpu_model }}</td>\r\n                    <td>\r\n              <span class=\"status-tag\" :class=\"getUsageStatusClass(record.status)\">\r\n                {{ getUsageStatusText(record.status) }}\r\n              </span>\r\n                    </td>\r\n                    <td>{{ formatDateTime(record.start_time) }}</td>\r\n                    <td>{{ record.end_time ? formatDateTime(record.end_time) : '--' }}</td>\r\n                    <td>{{ calculateDuration(record.start_time, record.end_time) }}</td>\r\n                    <td>¥ {{ formatPrice(record.cost / 10000) }}</td>\r\n                    <td>\r\n                      <span class=\"operation-link\" @click=\"navigateToRecharge\">续费</span>\r\n                      <span v-if=\"record.status === 'scheduled'\" class=\"operation-link cancel-link\" @click=\"cancelReservation(record)\">取消</span>\r\n                    </td>\r\n                  </tr>\r\n                  <tr v-if=\"filteredUsageData.length === 0\">\r\n                    <td colspan=\"7\" class=\"empty-state\">\r\n                      <div class=\"empty-container\">\r\n                        <i class=\"el-icon-time empty-icon\"></i>\r\n                        <div class=\"empty-text\">没有找到匹配的使用记录</div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n\r\n              <common-pagination\r\n                  :current-page=\"usagePage\"\r\n                  :total=\"filteredUsageData.length\"\r\n                  :page-size=\"pageSize\"\r\n                  @change-page=\"(page) => usagePage = page\"\r\n                  @change-page-size=\"(size) => { pageSize = size; usagePage = 1; }\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Recharge Section -->\r\n          <div v-if=\"currentSection === 'recharge'\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"account-balance\">\r\n                <div class=\"balance-info\">\r\n                  <div class=\"balance-label\">当前账户余额</div>\r\n                  <div class=\"balance-value\">¥ {{ formatPrice(userBalance) }}</div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"recharge-options\">\r\n                <h3 class=\"recharge-title\">选择充值金额</h3>\r\n\r\n                <div class=\"amount-options\">\r\n                  <div\r\n                      v-for=\"amount in rechargeAmounts\"\r\n                      :key=\"'amount-'+amount\"\r\n                      :class=\"['amount-option', { selected: rechargeAmount === amount }]\"\r\n                      @click=\"rechargeAmount = amount\"\r\n                  >\r\n                    {{ amount }}元\r\n                  </div>\r\n                  <div class=\"amount-option custom-amount\">\r\n                    <input\r\n                        type=\"number\"\r\n                        placeholder=\"其他金额\"\r\n                        v-model=\"customRechargeAmount\"\r\n                        @focus=\"rechargeAmount = null\"\r\n                        @input=\"handleCustomAmountInput\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <h3 class=\"recharge-title\">选择支付方式</h3>\r\n\r\n                <div class=\"payment-methods\">\r\n                  <div\r\n                      :class=\"['payment-method', { selected: paymentMethod === 'alipay' }]\"\r\n                      @click=\"paymentMethod = 'alipay'\"\r\n                  >\r\n                    <img src=\"../../assets/images/payment/alipay.svg\" alt=\"支付宝\" class=\"payment-icon\">\r\n                    <span>支付宝</span>\r\n                  </div>\r\n<!--                  <div-->\r\n<!--                      :class=\"['payment-method', { selected: paymentMethod === 'wechat' }]\"-->\r\n<!--                      @click=\"paymentMethod = 'wechat'\"-->\r\n<!--                  >-->\r\n<!--                    <i class=\"el-icon-wechat\"></i>-->\r\n<!--                    <span>微信支付</span>-->\r\n<!--                  </div>-->\r\n                </div>\r\n\r\n                <div class=\"recharge-action\">\r\n                  <button\r\n                      class=\"recharge-button\"\r\n                      @click=\"submitRecharge\"\r\n                      :disabled=\"!canRecharge\"\r\n                  >\r\n                    立即充值\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/layout-fee\";\r\nimport CommonPagination from \"@/views/Ordermange/CommonPagination\";\r\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\r\nimport { postAnyData, getAnyData } from \"@/api/login\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"OrderView\",\r\n  components: { SlideNotification, Layout, CommonPagination },\r\n  data() {\r\n    return {\r\n      // General\r\n      currentSection: 'transactions',\r\n      pageSize: 6,\r\n      userBalance: 0,\r\n      showNotification: false,\r\n      notificationMessage: '',\r\n      notificationType: 'success',\r\n\r\n      // Orders\r\n      orderData: [],\r\n      orderSearchQuery: '',\r\n      currentPage: 1,\r\n      showOrderDetails: false,\r\n      selectedOrder: {},\r\n      orderLoading: false,\r\n      orderError: null,\r\n\r\n      // Transactions\r\n      transactionData: [],\r\n      transactionSearchQuery: '',\r\n      transactionDateRange: '30',\r\n      transactionType: '',\r\n      transactionPage: 1,\r\n      customDateStart: '',\r\n      customDateEnd: '',\r\n      transactionLoading: false,\r\n      transactionError: null,\r\n      summaryData: {\r\n        totalRecharge: 0,\r\n        totalExpense: 0,\r\n        balance: 0\r\n      },\r\n\r\n      // Usage\r\n      customUsageDateStart: '',\r\n      customUsageDateEnd: '',\r\n      usageData: [],\r\n      usageDateRange: '30',\r\n      usageFilterGpu: '',\r\n      usageLoading: false,\r\n      usageError: null,\r\n      usagePage: 1,\r\n      gpuModels: [\r\n        { id: 'a100', name: 'NVIDIA A100' },\r\n        { id: 'v100', name: 'NVIDIA V100' },\r\n        { id: 'rtx3090', name: 'NVIDIA RTX 3090' }\r\n      ],\r\n\r\n      // Recharge\r\n      rechargeAmounts: [100, 200, 500, 1000, 2000],\r\n      rechargeAmount: 100,\r\n      customRechargeAmount: null,\r\n      paymentMethod: 'alipay',\r\n      rechargeLoading: false\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // Orders\r\n    filteredOrderData() {\r\n      if (!this.orderSearchQuery) return this.orderData;\r\n\r\n      const query = this.orderSearchQuery.toLowerCase();\r\n      return this.orderData.filter(order =>\r\n          order.order_number.toLowerCase().includes(query)\r\n      );\r\n    },\r\n\r\n    paginatedOrders() {\r\n      const startIndex = (this.currentPage - 1) * this.pageSize;\r\n      const endIndex = startIndex + this.pageSize;\r\n      return this.filteredOrderData.slice(startIndex, endIndex);\r\n    },\r\n\r\n    currentOrderTotal() {\r\n      return this.filteredOrderData.length;\r\n    },\r\n\r\n    // Transactions\r\n    filteredTransactionData() {\r\n      let filtered = [...this.transactionData];\r\n\r\n      // Filter by date range\r\n      if (this.transactionDateRange !== 'custom') {\r\n        const days = parseInt(this.transactionDateRange);\r\n        const cutoffDate = new Date();\r\n        cutoffDate.setDate(cutoffDate.getDate() - days);\r\n\r\n        filtered = filtered.filter(transaction => {\r\n          const transactionDate = new Date(transaction.created_at);\r\n          return transactionDate >= cutoffDate;\r\n        });\r\n      } else if (this.customDateStart && this.customDateEnd) {\r\n        const startDate = new Date(this.customDateStart);\r\n        const endDate = new Date(this.customDateEnd);\r\n        endDate.setHours(23, 59, 59, 999); // End of the day\r\n\r\n        filtered = filtered.filter(transaction => {\r\n          const transactionDate = new Date(transaction.created_at);\r\n          return transactionDate >= startDate && transactionDate <= endDate;\r\n        });\r\n      }\r\n\r\n      // Filter by type\r\n      if (this.transactionType) {\r\n        filtered = filtered.filter(transaction =>\r\n            transaction.type === this.transactionType\r\n        );\r\n      }\r\n\r\n      // Filter by search query\r\n      if (this.transactionSearchQuery) {\r\n        const query = this.transactionSearchQuery.toLowerCase();\r\n        filtered = filtered.filter(transaction =>\r\n            transaction.transaction_id.toLowerCase().includes(query)\r\n        );\r\n      }\r\n\r\n      return filtered;\r\n    },\r\n\r\n    paginatedTransactions() {\r\n      const startIndex = (this.transactionPage - 1) * this.pageSize;\r\n      const endIndex = startIndex + this.pageSize;\r\n      return this.filteredTransactionData.slice(startIndex, endIndex);\r\n    },\r\n\r\n\r\n// usage\r\n    filteredUsageData() {\r\n      if (!this.usageData || this.usageData.length === 0) return [];\r\n\r\n      let filtered = [...this.usageData];\r\n\r\n      // 按日期范围过滤\r\n      if (this.usageDateRange !== 'custom') {\r\n        const days = parseInt(this.usageDateRange);\r\n        const cutoffDate = new Date();\r\n        cutoffDate.setDate(cutoffDate.getDate() - days);\r\n\r\n        filtered = filtered.filter(record => {\r\n          const recordDate = new Date(record.start_time || record.created_at);\r\n          return recordDate >= cutoffDate;\r\n        });\r\n      } else if (this.customUsageDateStart && this.customUsageDateEnd) {\r\n        const startDate = new Date(this.customUsageDateStart);\r\n        const endDate = new Date(this.customUsageDateEnd);\r\n        endDate.setHours(23, 59, 59, 999); // 设置为当天的最后一刻\r\n\r\n        filtered = filtered.filter(record => {\r\n          const recordDate = new Date(record.start_time || record.created_at);\r\n          return recordDate >= startDate && recordDate <= endDate;\r\n        });\r\n      }\r\n\r\n      // 按GPU型号过滤\r\n      if (this.usageFilterGpu) {\r\n        filtered = filtered.filter(record =>\r\n            record.gpu_model.toLowerCase().includes(this.usageFilterGpu.toLowerCase())\r\n        );\r\n      }\r\n\r\n      return filtered;\r\n    },\r\n\r\n    paginatedUsageRecords() {\r\n      const startIndex = (this.usagePage - 1) * this.pageSize;\r\n      const endIndex = startIndex + this.pageSize;\r\n      return this.filteredUsageData.slice(startIndex, endIndex);\r\n    },\r\n\r\n    // Recharge\r\n    canRecharge() {\r\n      const amount = this.getRechargeAmount();\r\n      return amount && amount > 0 && this.paymentMethod && !this.rechargeLoading;\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    $route(to) {\r\n      // 当路由变化时检查activeTab参数\r\n      const activeTab = to.query.activeTab;\r\n      if (activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(activeTab)) {\r\n        this.currentSection = activeTab;\r\n      }\r\n    },\r\n    currentSection(newVal) {\r\n      if (newVal === 'transactions') {\r\n        this.fetchTransactions();\r\n        this.fetchSummaryData();\r\n      } else if (newVal === 'orders') {\r\n        this.fetchOrders();\r\n      } else if (newVal === 'recharge') {\r\n        this.fetchUserBalance();\r\n      }else if (newVal === 'transactions') {\r\n        this.fetchTransactions();\r\n        // this.currentSection = 'transactions';\r\n        this.loadSectionData('transactions');\r\n      }\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.fetchUserBalance();\r\n    this.fetchTransactions();\r\n    const activeTab = this.$route.query.activeTab || 'transactions';\r\n    // console.log(\"activetab\",activeTab)\r\n    this.currentSection = activeTab;\r\n\r\n    // 根据当前激活的标签页加载对应数据\r\n    this.loadSectionData(activeTab);\r\n    this.$watch(\r\n        () => this.$route.query,\r\n        (newQuery) => {\r\n          if (newQuery.activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(newQuery.activeTab)) {\r\n            this.currentSection = newQuery.activeTab;\r\n          }\r\n        },\r\n        { immediate: true }\r\n    );\r\n    this.fetchOrders().then(() => {\r\n      // 默认按创建时间排序\r\n      this.sortBy('created_at');\r\n\r\n      // 检查URL中的activeTab参数\r\n      const activeTab = this.$route.query.activeTab;\r\n      if (activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(activeTab)) {\r\n        this.currentSection = activeTab;\r\n      }\r\n    });\r\n    this.fetchSummaryData();\r\n  },\r\n  beforeDestroy() {\r\n    this.$emit('refresh-header')\r\n  },\r\n\r\n  methods: {\r\n\r\n    showNotificationMessage(message, type = 'info') {\r\n      this.notificationMessage = message;\r\n      this.notificationType = type;\r\n      this.showNotification = true;\r\n    },\r\n\r\n    changeSection(section) {\r\n      // 更新当前标签页\r\n      this.currentSection = section;\r\n\r\n      // 更新 URL，确保刷新后仍然能恢复\r\n      this.$router.replace({\r\n        query: {\r\n          ...this.$route.query, // 保留其他查询参数\r\n          activeTab: section,   // 更新 activeTab\r\n        },\r\n      });\r\n\r\n      // 加载对应数据\r\n      if (section === 'orders') {\r\n        this.fetchOrders();\r\n      } else if (section === 'transactions') {\r\n        this.fetchTransactions();\r\n        this.fetchSummaryData();\r\n      } else if (section === 'usage') {\r\n        this.fetchUsageData();\r\n      } else if (section === 'recharge') {\r\n        this.fetchUserBalance();\r\n      }\r\n    },\r\n    loadSectionData(section) {\r\n      switch(section) {\r\n        case 'orders':\r\n          this.fetchOrders();\r\n          break;\r\n        case 'transactions':\r\n          this.fetchTransactions();\r\n          this.fetchSummaryData();\r\n          break;\r\n        case 'usage':\r\n          this.fetchUsageData();\r\n          break;\r\n        case 'recharge':\r\n          this.fetchUserBalance();\r\n          break;\r\n      }},\r\n    // 获取用户余额\r\n    async fetchUserBalance() {\r\n      try {\r\n        const response = await postAnyData(\"/logout/cilent/getInfo\");\r\n        if (response.data.code === 200) {\r\n          this.userBalance = response.data.data.balance || 0;\r\n          this.$emit('refresh-header')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取用户余额失败');\r\n      }\r\n    },\r\n\r\n    applyUsageCustomDateRange() {\r\n      if (!this.customUsageDateStart || !this.customUsageDateEnd) {\r\n        this.$message.error('请选择开始和结束日期');\r\n        return;\r\n      }\r\n\r\n      const startDate = new Date(this.customUsageDateStart);\r\n      const endDate = new Date(this.customUsageDateEnd);\r\n\r\n      if (startDate > endDate) {\r\n        this.$message.error('开始日期不能晚于结束日期');\r\n        return;\r\n      }\r\n\r\n      this.usagePage = 1; // 重置到第一页\r\n      this.fetchUsageData(); // 重新获取数据\r\n    },\r\n\r\n    // 格式化日期时间（带时区处理）\r\n    formatDateTimeusage(dateTimeString) {\r\n      if (!dateTimeString) return '--';\r\n\r\n      try {\r\n        const date = new Date(dateTimeString);\r\n        // 转换为本地时间字符串\r\n        return date.toLocaleString('zh-CN', {\r\n          year: 'numeric',\r\n          month: '2-digit',\r\n          day: '2-digit',\r\n          hour: '2-digit',\r\n          minute: '2-digit',\r\n          second: '2-digit',\r\n          hour12: false\r\n        }).replace(/\\//g, '-');\r\n      } catch (e) {\r\n        return '--';\r\n      }\r\n    },\r\n\r\n    // 计算运行时长（带时区处理）\r\n    calculateDuration(startTime, endTime) {\r\n      if (!startTime) return \"0分钟\";\r\n\r\n      try {\r\n        const start = new Date(startTime);\r\n        const end = endTime ? new Date(endTime) : new Date();\r\n\r\n        // 检查时间是否有效\r\n        if (isNaN(start.getTime())) return \"时间无效\";\r\n        if (endTime && isNaN(end.getTime())) return \"时间无效\";\r\n\r\n        // 如果开始时间在未来\r\n        if (start > new Date()) {\r\n          return \"未开始\";\r\n        }\r\n\r\n        // 如果结束时间在未来（仍在运行中）\r\n        if (!endTime || end > new Date()) {\r\n          const diffMs = new Date() - start;\r\n          const hours = Math.floor(diffMs / (1000 * 60 * 60));\r\n          const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n          return `${hours}小时${minutes}分钟 `;\r\n        }\r\n\r\n        // 正常计算时长\r\n        const diffMs = end - start;\r\n        const hours = Math.floor(diffMs / (1000 * 60 * 60));\r\n        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n        if (hours > 0) {\r\n          return `${hours}小时${minutes}分钟`;\r\n        }\r\n        return `${minutes}分钟`;\r\n      } catch (e) {\r\n        return \"--\";\r\n      }\r\n    },\r\n\r\n\r\n\r\n    navigateToRecharge() {\r\n      // 使用 replace 而不是 push 避免路由历史堆积\r\n      this.$router.replace({\r\n        path: '/userorder',\r\n        query: {\r\n          activeTab: 'recharge',\r\n          timestamp: Date.now() // 确保每次导航都是唯一的\r\n        }\r\n      });\r\n    },\r\n\r\n    // 从后端获取订单数据\r\n    async fetchOrders() {\r\n      this.orderLoading = true;\r\n      this.orderError = null;\r\n      try {\r\n        const response = await postAnyData(\"/system/order/getOrderAndProduct\");\r\n\r\n        // 添加排序逻辑\r\n        this.orderData = response.data.data.map(order => ({\r\n          id: order.name,\r\n          order_number: order.order_name,\r\n          payment_status: this.mapOrderStatus(order.order_staus),\r\n          payment_method: order.order_payment_method || '余额支付',\r\n          total_price: order.order_price,\r\n          unit_price: this.calculateUnitPrice(order),\r\n          duration: order.order_buy_time,\r\n          created_at: order.create_time,\r\n          gpu_model: order.name || '未知型号',\r\n          region: order.Region || '未知地区',\r\n          gpu_count: order.graphics_card_number || 0,\r\n          video_memory: order.video_memory || '未知',\r\n          cpu_cores: order.gpu_nuclear_number || 0,\r\n          system_disk: order.system_disk || '未知',\r\n          cloud_disk: order.data_disk || '未知',\r\n          memory: order.internal_memory || '未知',\r\n          rawData: order\r\n        })).sort((a, b) => {\r\n          // 按创建时间降序排序（最新的在前）\r\n          return new Date(b.created_at) - new Date(a.created_at);\r\n        });\r\n      } catch (error) {\r\n        this.orderError = '获取订单数据失败，请稍后重试';\r\n        // this.$message.error(this.orderError);\r\n      } finally {\r\n        this.orderLoading = false;\r\n      }\r\n    },\r\n\r\n    // 获取收支明细数据\r\n    async fetchTransactions() {\r\n      this.transactionLoading = true;\r\n      this.transactionError = null;\r\n      try {\r\n        const response = await postAnyData(\"/system/order/getConsumptionOrder\");\r\n        const rawData = response.data.data;\r\n\r\n        // 用map去重，确保不会因为相同order_id重复\r\n        const uniqueMap = new Map();\r\n        rawData.forEach(transaction => {\r\n          // 用topup_id或gpu_order_id作为唯一主键\r\n          const uniqueId = transaction.topup_id || transaction.gpu_order_id;\r\n\r\n          if (!uniqueMap.has(uniqueId)) {\r\n            uniqueMap.set(uniqueId, {\r\n              transaction_id: transaction.topup_order_id || transaction.gpu_order_name,\r\n              type: transaction.source_table === \"收入\" ? 'income' : 'expense',\r\n              pay_type: this.mapPayType(transaction.source_table),\r\n              amount: transaction.topup_topup || transaction.gpu_order_price,\r\n              created_at: transaction.create_time,\r\n              payment_channel: transaction.payment_method || '未知',\r\n              status: this.mapTransactionStatus(transaction.order_staus),\r\n              description: transaction.description || '无'\r\n            });\r\n          }\r\n        });\r\n\r\n        this.transactionData = Array.from(uniqueMap.values());\r\n      } catch (error) {\r\n        this.transactionLoading = '暂无记录';\r\n      } finally {\r\n        this.transactionLoading = false;\r\n      }\r\n    },\r\n\r\n\r\n    async fetchUsageData() {\r\n      this.usageLoading = true;\r\n      this.usageError = null;\r\n      try {\r\n        let params = {};\r\n\r\n        // 添加日期范围参数\r\n        if (this.usageDateRange === 'custom' && this.customUsageDateStart && this.customUsageDateEnd) {\r\n          params.start_date = this.customUsageDateStart;\r\n          params.end_date = this.customUsageDateEnd;\r\n        } else if (this.usageDateRange !== 'custom') {\r\n          // 处理预设日期范围\r\n          const days = parseInt(this.usageDateRange);\r\n          const cutoffDate = new Date();\r\n          cutoffDate.setDate(cutoffDate.getDate() - days);\r\n          params.start_date = cutoffDate.toISOString().split('T')[0];\r\n        }\r\n\r\n        // 添加GPU过滤参数\r\n        if (this.usageFilterGpu) {\r\n          params.gpu_model = this.usageFilterGpu;\r\n        }\r\n        // 这里应该改为实际的API调用，目前使用模拟数据\r\n        const response = await postAnyData(\"/system/order/getProductResumption\");\r\n\r\n        // 如果API已准备好，应该使用实际数据\r\n        // this.usageData = response.data.data;\r\n\r\n        // 临时使用模拟数据\r\n        const mockData = [\r\n\r\n        ];\r\n\r\n        this.usageData = mockData;\r\n        return this.usageData;\r\n\r\n      } catch (error) {\r\n        this.usageError = '加载使用记录失败，请稍后重试';\r\n        // this.$message.error(this.usageError);\r\n        return []; // 出错时返回空数组\r\n      } finally {\r\n        this.usageLoading = false;\r\n      }\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    // 获取汇总数据\r\n    // 获取汇总数据 (去重版)\r\n    async fetchSummaryData() {\r\n      try {\r\n        const response = await postAnyData(\"/system/order/getConsumptionOrder\");\r\n        const transactions = response.data.data || [];\r\n\r\n        // 先做去重，避免重复计算\r\n        const uniqueMap = new Map();\r\n        transactions.forEach(t => {\r\n          const uniqueId = t.topup_id || t.gpu_order_id;\r\n          if (!uniqueMap.has(uniqueId)) {\r\n            uniqueMap.set(uniqueId, t);\r\n          }\r\n        });\r\n\r\n        const uniqueTransactions = Array.from(uniqueMap.values());\r\n\r\n        // 计算总充值\r\n        const totalRecharge = uniqueTransactions\r\n            .filter(t => t.source_table === \"收入\")\r\n            .reduce((sum, t) => sum + (parseFloat(t.topup_topup) || 0), 0);\r\n\r\n        // 计算总消费\r\n        const totalExpense = uniqueTransactions\r\n            .filter(t => t.source_table === \"支出\")\r\n            .reduce((sum, t) => sum + (parseFloat(t.gpu_order_price) || 0), 0);\r\n\r\n        this.summaryData = {\r\n          totalRecharge,\r\n          totalExpense,\r\n          balance: this.userBalance\r\n        };\r\n      } catch (error) {\r\n        this.$message.error('获取账户汇总数据失败');\r\n      }\r\n    },\r\n\r\n    // 映射订单状态\r\n    mapOrderStatus(status) {\r\n      const statusMap = {\r\n        '1': 'paid',     // 支付成功\r\n        '2': 'failed',   // 支付失败\r\n        '3': 'unpaid'    // 未支付\r\n      };\r\n      return statusMap[status] || status;\r\n    },\r\n\r\n    // 映射交易类型\r\n    mapPayType(type) {\r\n      return type === \"收入\" ? '充值' : '消费';\r\n    },\r\n\r\n    // 映射交易状态\r\n    mapTransactionStatus(status) {\r\n      const statusMap = {\r\n        '1': 'success',    // 成功\r\n        '2': 'failed',     // 失败\r\n        '3': 'processing'  // 处理中\r\n      };\r\n      return statusMap[status] || status;\r\n    },\r\n\r\n    // 计算单价（根据商品信息和订单时长）\r\n    calculateUnitPrice(order) {\r\n      if (!order.computing) return order.order_price;\r\n\r\n      // 根据购买单位选择对应的价格\r\n      switch(order.order_unit) {\r\n        case 'hour':\r\n          return order.computing.price_bour;\r\n        case 'day':\r\n          return order.computing.price_day;\r\n        case 'month':\r\n          return order.computing.price_mouth;\r\n        case 'year':\r\n          return order.computing.price_year;\r\n        default:\r\n          return order.order_price;\r\n      }\r\n    },\r\n\r\n    // Formatting methods\r\n    formatPrice(price) {\r\n      if (isNaN(price)) return '0.00';\r\n      return parseFloat(price).toFixed(2);\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      return new Date(dateString).toLocaleDateString();\r\n    },\r\n\r\n    formatDateTime(dateString) {\r\n      if (!dateString) return '';\r\n      const date = new Date(dateString);\r\n      return date.toLocaleString();\r\n    },\r\n\r\n    // Order methods\r\n    getPaymentStatusClass(status) {\r\n      return {\r\n        'paid': 'status-success',\r\n        'unpaid': 'status-pending',\r\n        'refunded': 'status-info',\r\n        'failed': 'status-error',\r\n        'cancelled': 'status-warning'\r\n      }[status] || '';\r\n    },\r\n\r\n    getPaymentStatusText(status) {\r\n      return {\r\n        'paid': '已支付',\r\n        'unpaid': '未支付',\r\n        'refunded': '已退款',\r\n        'failed': '支付失败',\r\n        'cancelled': '已取消'\r\n      }[status] || status;\r\n    },\r\n\r\n    getPaymentMethodClass(method) {\r\n      return {\r\n        '支付宝': 'payment-alipay',\r\n        '微信支付': 'payment-wechat',\r\n        '银行卡': 'payment-bank',\r\n        '余额支付': 'payment-balance',\r\n        '未支付': 'payment-warning'\r\n      }[method] || '';\r\n    },\r\n\r\n    getPaymentMethodText(method) {\r\n      return {\r\n        'alipay': '支付宝',\r\n        'wechat': '微信支付',\r\n        'bank': '银行卡',\r\n        'balance': '余额'\r\n      }[method] || method;\r\n    },\r\n\r\n    searchOrders() {\r\n      this.currentPage = 1;\r\n    },\r\n\r\n    clearOrderSearch() {\r\n      this.orderSearchQuery = '';\r\n      this.currentPage = 1;\r\n    },\r\n\r\n    sortBy(field) {\r\n      if (field === 'created_at') {\r\n        this.orderData.sort((a, b) => {\r\n          return new Date(b[field]) - new Date(a[field]);\r\n        });\r\n      } else {\r\n        this.orderData.sort((a, b) => {\r\n          return a[field] > b[field] ? 1 : -1;\r\n        });\r\n      }\r\n    },\r\n\r\n    viewOrderDetails(order) {\r\n      this.selectedOrder = order;\r\n      this.showOrderDetails = true;\r\n      this.$nextTick(() => {\r\n        document.querySelector('.order-details').scrollIntoView({ behavior: 'smooth' });\r\n      });\r\n    },\r\n\r\n    showOrderList() {\r\n      this.showOrderDetails = false;\r\n      window.scrollTo({ top: 0, behavior: 'smooth' });\r\n    },\r\n\r\n    goToPage(page) {\r\n      this.currentPage = page;\r\n    },\r\n\r\n    handlePageSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n    },\r\n\r\n    // Transaction methods\r\n    searchTransactions() {\r\n      this.transactionPage = 1;\r\n    },\r\n\r\n    applyCustomDateRange() {\r\n      if (this.customDateStart && this.customDateEnd) {\r\n        this.transactionPage = 1;\r\n      }\r\n    },\r\n\r\n    getTransactionTypeClass(type) {\r\n      return {\r\n        'income': 'income-type',\r\n        'expense': 'expense-type',\r\n        'refund': 'refund-type'\r\n      }[type] || '';\r\n    },\r\n\r\n    getTransactionTypeName(type) {\r\n      return {\r\n        'income': '收入',\r\n        'expense': '支出',\r\n        'refund': '退款'\r\n      }[type] || type;\r\n    },\r\n\r\n    getTransactionTypeNamePay(payType) {\r\n      return {\r\n        'income': '收入',\r\n        'expense': '支出',\r\n      }[payType] || payType;\r\n    },\r\n\r\n    getTransactionStatusClass(status) {\r\n      return {\r\n        'success': 'status-success',\r\n        'pending': 'status-pending',\r\n        'failed': 'status-error',\r\n        'processing': 'status-info'\r\n      }[status] || '';\r\n    },\r\n\r\n    getTransactionStatusName(status) {\r\n      return {\r\n        'success': '成功',\r\n        'pending': '处理中',\r\n        'failed': '失败',\r\n        'processing': '处理中'\r\n      }[status] || status;\r\n    },\r\n\r\n    // Usage methods\r\n    getUsageStatusClass(status) {\r\n      return {\r\n        'active': 'status-success',\r\n        'running': 'status-running',\r\n        'about_to_expire': 'status-warning',\r\n        'expired': 'status-error',\r\n        'completed': 'status-complete',\r\n        'paused': 'status-info'\r\n      }[status] || '';\r\n    },\r\n\r\n    getUsageStatusText(status) {\r\n      return {\r\n        'active': '可用',\r\n        'running': '使用中',\r\n        'about_to_expire': '即将到期',\r\n        'completed': '已结束',\r\n        'paused': '已暂停'\r\n      }[status] || status;\r\n    },\r\n\r\n    renewService(record) {\r\n      this.$message.success(`正在为 ${record.gpu_model} 续费`);\r\n    },\r\n\r\n    // Recharge methods\r\n    getRechargeAmount() {\r\n      return this.rechargeAmount || this.customRechargeAmount;\r\n    },\r\n\r\n    handleCustomAmountInput() {\r\n      this.rechargeAmount = null;\r\n    },\r\n\r\n    async submitRecharge() {\r\n      const amount = this.getRechargeAmount();\r\n      if (!amount || amount <= 0) {\r\n        // this.$message.error('请输入有效的充值金额');\r\n        this.showNotificationMessage('请输入有效的充值金额', 'error');\r\n\r\n        return;\r\n      }\r\n\r\n      this.rechargeLoading = true;\r\n\r\n      try {\r\n        // 调用充值API\r\n        const response = await getAnyData('/yun/scanPay', { amount: amount });\r\n        if (response.data.includes('充值金额不足')) {\r\n          // 提取需要充值的金额（正则表达式匹配数字和小数点）\r\n          this.showNotificationMessage(response.data, 'error');\r\n          return;\r\n        }\r\n        // 处理支付表单\r\n        const tempDiv = document.createElement('div');\r\n\r\n        tempDiv.innerHTML = response.data;\r\n        document.body.appendChild(tempDiv);\r\n        const form = tempDiv.querySelector('form');\r\n\r\n        // console.log(response)\r\n\r\n\r\n\r\n        if (form) {\r\n          this.showNotificationMessage('正在跳转到支付页面...', 'success');\r\n          form.target = '_blank'; // 设置表单提交目标为新窗口\r\n          form.submit();\r\n          // this.$message.success('正在跳转到支付页面...');\r\n\r\n          // 支付成功后刷新余额和交易记录\r\n          setTimeout(() => {\r\n            this.fetchUserBalance();\r\n            this.fetchTransactions();\r\n          }, 3000);\r\n        } else {\r\n          this.showNotificationMessage('支付表单生成失败', 'error');\r\n        }\r\n      } catch (error) {\r\n        this.showNotificationMessage('充值请求失败，请稍后重试', 'error');\r\n      } finally {\r\n        this.rechargeLoading = false;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.fee-center-container {\r\n  display: flex;\r\n  max-width: 2560px;\r\n  background-color: #f5f7fa;\r\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n.navigation-sidebar {\r\n  width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #ebeef5;\r\n  position: fixed;\r\n  height: 100%;\r\n  z-index: 10;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.nav-title {\r\n  font-size: 18px;\r\n  color: #303133;\r\n  padding: 20px 16px;\r\n  margin: 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.nav-list {\r\n  list-style: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.nav-item {\r\n  padding: 14px 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.nav-item a {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #303133;\r\n  text-decoration: none;\r\n}\r\n\r\n.nav-item i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.nav-item.active {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.nav-item.active a {\r\n  color: #409eff;\r\n}\r\n\r\n.nav-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.main-content {\r\n  flex: 1;\r\n  margin-left: 200px;\r\n  padding: 20px;\r\n}\r\n\r\n.tab-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-bar {\r\n  position: relative;\r\n  width: 300px;\r\n}\r\n\r\n.search-bar input {\r\n  width: 100%;\r\n  height: 36px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  padding: 0 30px 0 12px;\r\n  font-size: 14px;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.search-bar input:focus {\r\n  border-color: #409eff;\r\n  outline: none;\r\n}\r\n\r\n.search-button, .clear-button {\r\n  position: absolute;\r\n  right: 8px;\r\n  top: 8px;\r\n  background: none;\r\n  border: none;\r\n  color: #606266;\r\n  cursor: pointer;\r\n}\r\n\r\n.clear-button {\r\n  right: 30px;\r\n}\r\n\r\n.currency-display {\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.flow-count {\r\n  margin-left: 16px;\r\n}\r\n\r\n.table-container {\r\n  overflow-x: auto;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.data-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n}\r\n\r\n.data-table th, .data-table td {\r\n  padding: 12px 8px;\r\n  text-align: left;\r\n  font-size: 14px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.data-table th {\r\n  color: #909399;\r\n  font-weight: 500;\r\n  white-space: nowrap;\r\n}\r\n\r\n.data-table td {\r\n  color: #606266;\r\n}\r\n\r\n.data-table th i {\r\n  margin-left: 4px;\r\n  cursor: pointer;\r\n  color: #c0c4cc;\r\n}\r\n\r\n.data-table th i:hover {\r\n  color: #409eff;\r\n}\r\n\r\n.status-tag {\r\n  width: 10vh;\r\n  text-align: center;\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-success {\r\n  background-color: #f0f9eb;\r\n  color: #67c23a;\r\n}\r\n\r\n.status-pending {\r\n  background-color: #fdf6ec;\r\n  color: #e6a23c;\r\n}\r\n\r\n.status-error {\r\n  background-color: #fef0f0;\r\n  color: #f56c6c;\r\n}\r\n\r\n.status-info {\r\n  background-color: #f4f4f5;\r\n  color: #909399;\r\n}\r\n\r\n.status-warning {\r\n  background-color: #fdf6ec;\r\n  color: #e6a23c;\r\n}\r\n\r\n.status-running {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n.status-complete {\r\n  background-color: #ecf5ff;\r\n  color: #e6a23c;\r\n}\r\n.payment-method-tag {\r\n  width: 10vh;\r\n  text-align: center;\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.payment-alipay {\r\n  background-color: #e6f7ff;\r\n  color: #1890ff;\r\n}\r\n\r\n.payment-wechat {\r\n  background-color: #f6ffed;\r\n  color: #52c41a;\r\n}\r\n\r\n.payment-bank {\r\n  background-color: #fff2e8;\r\n  color: #fa8c16;\r\n}\r\n\r\n.payment-balance {\r\n  background-color: #f9f0ff;\r\n  color: #722ed1;\r\n}\r\n\r\n.payment-warning {\r\n  background-color: #fdf6ec;\r\n  color: #e6a23c;\r\n}\r\n\r\n.operation-link {\r\n  color: #409eff;\r\n  cursor: pointer;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.operation-link:hover {\r\n  color: #66b1ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.expense-amount {\r\n  color: #e52a2a !important;\r\n  font-weight: bold !important;\r\n}\r\n.income-amount {\r\n  color: #4fc44f !important;\r\n  font-weight: bold !important;\r\n}\r\n\r\n.empty-state {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n}\r\n\r\n.empty-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 48px;\r\n  color: #c0c4cc;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.empty-text {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.order-details {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.detail-card {\r\n  padding: 20px;\r\n}\r\n\r\n.detail-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  padding-bottom: 16px;\r\n}\r\n\r\n.detail-title {\r\n  font-size: 18px;\r\n  color: #303133;\r\n  margin: 0;\r\n}\r\n\r\n.back-button {\r\n  display: flex;\r\n  align-items: center;\r\n  background: none;\r\n  border: none;\r\n  color: #409eff;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  padding: 6px 12px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.back-button:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.back-button i {\r\n  margin-right: 4px;\r\n}\r\n\r\n.detail-content {\r\n  padding: 0 16px;\r\n  max-height: 395px;\r\n}\r\n\r\n.detail-subtitle {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  margin: 24px 0 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.detail-section {\r\n  margin-bottom: 24px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n}\r\n\r\n.detail-row {\r\n  display: flex;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.detail-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.detail-item {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.detail-label {\r\n  width: 120px;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.detail-value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.transaction-summary, .usage-summary {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.summary-card {\r\n  flex: 1;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n  margin-right: 16px;\r\n  text-align: center;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.summary-card:hover {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.summary-card:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n.summary-title {\r\n  color: #909399;\r\n  font-size: 14px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.summary-value {\r\n  color: #303133;\r\n  font-size: 24px;\r\n  font-weight: 500;\r\n}\r\n\r\n.summary-value.income {\r\n  color: #67c23a;\r\n}\r\n\r\n.summary-value.expense {\r\n  color: #f56c6c;\r\n}\r\n\r\n.transaction-type {\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.income-type {\r\n  background-color: #f0f9eb;\r\n  color: #67c23a;\r\n}\r\n\r\n.expense-type {\r\n  background-color: #fef0f0;\r\n  color: #f56c6c;\r\n}\r\n\r\n.refund-type {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.date-range-picker {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.date-range-picker select,\r\n.search-filters select,\r\n.search-filters input {\r\n  height: 36px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  padding: 0 12px;\r\n  margin-right: 12px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  transition: border-color 0.2s;\r\n}\r\n\r\n.date-range-picker select:focus,\r\n.search-filters select:focus,\r\n.search-filters input:focus {\r\n  border-color: #409eff;\r\n  outline: none;\r\n}\r\n\r\n.custom-date-range {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: 12px;\r\n}\r\n\r\n.custom-date-range input {\r\n  width: 140px;\r\n  margin-right: 8px;\r\n  height: 36px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  padding: 0 12px;\r\n}\r\n\r\n.custom-date-range span {\r\n  margin: 0 8px;\r\n  color: #606266;\r\n}\r\n\r\n.apply-button {\r\n  background-color: #409eff;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 8px 16px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.apply-button:hover {\r\n  background-color: #66b1ff;\r\n}\r\n\r\n.account-balance {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.balance-info {\r\n  display: flex;\r\n  align-items: baseline;\r\n  justify-content: flex-start;\r\n  background-color: #fff;\r\n  padding: 16px 24px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  width: 100%;\r\n}\r\n\r\n.balance-label {\r\n  font-size: 20px;\r\n  color: #606266;\r\n  margin-right: 12px;\r\n}\r\n\r\n.balance-value {\r\n  font-size: 22px;\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.recharge-options {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.recharge-title {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  margin: 0 0 16px 0;\r\n  font-weight: 500;\r\n}\r\n\r\n.amount-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.amount-option {\r\n  width: calc(20% - 16px);\r\n  min-width: 100px;\r\n  height: 50px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  color: #606266;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.amount-option:hover {\r\n  border-color: #c6e2ff;\r\n  color: #409eff;\r\n}\r\n\r\n.amount-option.selected {\r\n  border-color: #409eff;\r\n  color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.custom-amount {\r\n  width: calc(20% - 16px);\r\n  min-width: 100px;\r\n}\r\n\r\n.custom-amount input {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n  outline: none;\r\n  text-align: center;\r\n  font-size: 16px;\r\n  color: #606266;\r\n}\r\n\r\n.payment-methods {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.payment-method {\r\n  width: 140px;\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;  /* 垂直居中 */\r\n  justify-content: center;  /* 水平居中 */\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  padding: 0 16px;  /* 添加内边距 */\r\n}\r\n\r\n.payment-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  margin-right: 8px;  /* 图标和文字之间的间距 */\r\n}\r\n\r\n.payment-text {\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.payment-method:hover {\r\n  border-color: #c6e2ff;\r\n}\r\n\r\n.payment-method.selected {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.payment-method.selected .payment-icon,\r\n.payment-method.selected .payment-text {\r\n  color: #409eff;\r\n}\r\n\r\n.recharge-action {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 32px;\r\n}\r\n\r\n.recharge-button {\r\n  width: 200px;\r\n  height: 40px;\r\n  background-color: #409eff;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.recharge-button:hover {\r\n  background-color: #66b1ff;\r\n}\r\n\r\n.recharge-button:disabled {\r\n  background-color: #a0cfff;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.loading-state, .error-state {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.loading-state i {\r\n  font-size: 18px;\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n  animation: rotating 2s linear infinite;\r\n}\r\n\r\n.error-state i {\r\n  font-size: 18px;\r\n  margin-right: 8px;\r\n  color: #f56c6c;\r\n}\r\n\r\n.error-state button {\r\n  margin-left: 12px;\r\n  padding: 4px 12px;\r\n  background-color: #f56c6c;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n}\r\n\r\n.payment-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n@keyframes rotating {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 992px) {\r\n  .navigation-sidebar {\r\n    width: 180px;\r\n  }\r\n\r\n  .main-content {\r\n    margin-left: 180px;\r\n  }\r\n\r\n  .amount-option {\r\n    width: calc(25% - 16px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .navigation-sidebar {\r\n    width: 160px;\r\n  }\r\n\r\n  .main-content {\r\n    margin-left: 160px;\r\n  }\r\n\r\n  .search-section {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .search-bar {\r\n    width: 100%;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .amount-option {\r\n    width: calc(33.33% - 16px);\r\n  }\r\n}\r\n\r\n@media (max-width: 576px) {\r\n  .navigation-sidebar {\r\n    width: 100%;\r\n    position: static;\r\n    height: auto;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .main-content {\r\n    margin-left: 0;\r\n  }\r\n\r\n  .transaction-summary, .usage-summary {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .summary-card {\r\n    margin-right: 0;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .summary-card:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .amount-option {\r\n    width: calc(50% - 16px);\r\n  }\r\n\r\n  .payment-methods {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .payment-method {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAkfA,OAAAA,MAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,iBAAA;AACA,SAAAC,WAAA,EAAAC,UAAA;AACA,SAAAC,QAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAL,iBAAA;IAAAF,MAAA;IAAAC;EAAA;EACAO,KAAA;IACA;MACA;MACAC,cAAA;MACAC,QAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,gBAAA;MAEA;MACAC,SAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,aAAA;MACAC,YAAA;MACAC,UAAA;MAEA;MACAC,eAAA;MACAC,sBAAA;MACAC,oBAAA;MACAC,eAAA;MACAC,eAAA;MACAC,eAAA;MACAC,aAAA;MACAC,kBAAA;MACAC,gBAAA;MACAC,WAAA;QACAC,aAAA;QACAC,YAAA;QACAC,OAAA;MACA;MAEA;MACAC,oBAAA;MACAC,kBAAA;MACAC,SAAA;MACAC,cAAA;MACAC,cAAA;MACAC,YAAA;MACAC,UAAA;MACAC,SAAA;MACAC,SAAA,GACA;QAAAC,EAAA;QAAAtC,IAAA;MAAA,GACA;QAAAsC,EAAA;QAAAtC,IAAA;MAAA,GACA;QAAAsC,EAAA;QAAAtC,IAAA;MAAA,EACA;MAEA;MACAuC,eAAA;MACAC,cAAA;MACAC,oBAAA;MACAC,aAAA;MACAC,eAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,kBAAA;MACA,UAAAnC,gBAAA,cAAAD,SAAA;MAEA,MAAAqC,KAAA,QAAApC,gBAAA,CAAAqC,WAAA;MACA,YAAAtC,SAAA,CAAAuC,MAAA,CAAAC,KAAA,IACAA,KAAA,CAAAC,YAAA,CAAAH,WAAA,GAAAI,QAAA,CAAAL,KAAA,EACA;IACA;IAEAM,gBAAA;MACA,MAAAC,UAAA,SAAA1C,WAAA,aAAAP,QAAA;MACA,MAAAkD,QAAA,GAAAD,UAAA,QAAAjD,QAAA;MACA,YAAAyC,iBAAA,CAAAU,KAAA,CAAAF,UAAA,EAAAC,QAAA;IACA;IAEAE,kBAAA;MACA,YAAAX,iBAAA,CAAAY,MAAA;IACA;IAEA;IACAC,wBAAA;MACA,IAAAC,QAAA,YAAA3C,eAAA;;MAEA;MACA,SAAAE,oBAAA;QACA,MAAA0C,IAAA,GAAAC,QAAA,MAAA3C,oBAAA;QACA,MAAA4C,UAAA,OAAAC,IAAA;QACAD,UAAA,CAAAE,OAAA,CAAAF,UAAA,CAAAG,OAAA,KAAAL,IAAA;QAEAD,QAAA,GAAAA,QAAA,CAAAX,MAAA,CAAAkB,WAAA;UACA,MAAAC,eAAA,OAAAJ,IAAA,CAAAG,WAAA,CAAAE,UAAA;UACA,OAAAD,eAAA,IAAAL,UAAA;QACA;MACA,gBAAAzC,eAAA,SAAAC,aAAA;QACA,MAAA+C,SAAA,OAAAN,IAAA,MAAA1C,eAAA;QACA,MAAAiD,OAAA,OAAAP,IAAA,MAAAzC,aAAA;QACAgD,OAAA,CAAAC,QAAA;;QAEAZ,QAAA,GAAAA,QAAA,CAAAX,MAAA,CAAAkB,WAAA;UACA,MAAAC,eAAA,OAAAJ,IAAA,CAAAG,WAAA,CAAAE,UAAA;UACA,OAAAD,eAAA,IAAAE,SAAA,IAAAF,eAAA,IAAAG,OAAA;QACA;MACA;;MAEA;MACA,SAAAnD,eAAA;QACAwC,QAAA,GAAAA,QAAA,CAAAX,MAAA,CAAAkB,WAAA,IACAA,WAAA,CAAAM,IAAA,UAAArD,eAAA,CACA;MACA;;MAEA;MACA,SAAAF,sBAAA;QACA,MAAA6B,KAAA,QAAA7B,sBAAA,CAAA8B,WAAA;QACAY,QAAA,GAAAA,QAAA,CAAAX,MAAA,CAAAkB,WAAA,IACAA,WAAA,CAAAO,cAAA,CAAA1B,WAAA,GAAAI,QAAA,CAAAL,KAAA,EACA;MACA;MAEA,OAAAa,QAAA;IACA;IAEAe,sBAAA;MACA,MAAArB,UAAA,SAAAjC,eAAA,aAAAhB,QAAA;MACA,MAAAkD,QAAA,GAAAD,UAAA,QAAAjD,QAAA;MACA,YAAAsD,uBAAA,CAAAH,KAAA,CAAAF,UAAA,EAAAC,QAAA;IACA;IAGA;IACAqB,kBAAA;MACA,UAAA5C,SAAA,SAAAA,SAAA,CAAA0B,MAAA;MAEA,IAAAE,QAAA,YAAA5B,SAAA;;MAEA;MACA,SAAAC,cAAA;QACA,MAAA4B,IAAA,GAAAC,QAAA,MAAA7B,cAAA;QACA,MAAA8B,UAAA,OAAAC,IAAA;QACAD,UAAA,CAAAE,OAAA,CAAAF,UAAA,CAAAG,OAAA,KAAAL,IAAA;QAEAD,QAAA,GAAAA,QAAA,CAAAX,MAAA,CAAA4B,MAAA;UACA,MAAAC,UAAA,OAAAd,IAAA,CAAAa,MAAA,CAAAE,UAAA,IAAAF,MAAA,CAAAR,UAAA;UACA,OAAAS,UAAA,IAAAf,UAAA;QACA;MACA,gBAAAjC,oBAAA,SAAAC,kBAAA;QACA,MAAAuC,SAAA,OAAAN,IAAA,MAAAlC,oBAAA;QACA,MAAAyC,OAAA,OAAAP,IAAA,MAAAjC,kBAAA;QACAwC,OAAA,CAAAC,QAAA;;QAEAZ,QAAA,GAAAA,QAAA,CAAAX,MAAA,CAAA4B,MAAA;UACA,MAAAC,UAAA,OAAAd,IAAA,CAAAa,MAAA,CAAAE,UAAA,IAAAF,MAAA,CAAAR,UAAA;UACA,OAAAS,UAAA,IAAAR,SAAA,IAAAQ,UAAA,IAAAP,OAAA;QACA;MACA;;MAEA;MACA,SAAArC,cAAA;QACA0B,QAAA,GAAAA,QAAA,CAAAX,MAAA,CAAA4B,MAAA,IACAA,MAAA,CAAAG,SAAA,CAAAhC,WAAA,GAAAI,QAAA,MAAAlB,cAAA,CAAAc,WAAA,IACA;MACA;MAEA,OAAAY,QAAA;IACA;IAEAqB,sBAAA;MACA,MAAA3B,UAAA,SAAAjB,SAAA,aAAAhC,QAAA;MACA,MAAAkD,QAAA,GAAAD,UAAA,QAAAjD,QAAA;MACA,YAAAuE,iBAAA,CAAApB,KAAA,CAAAF,UAAA,EAAAC,QAAA;IACA;IAEA;IACA2B,YAAA;MACA,MAAAC,MAAA,QAAAC,iBAAA;MACA,OAAAD,MAAA,IAAAA,MAAA,aAAAxC,aAAA,UAAAC,eAAA;IACA;EACA;EAEAyC,KAAA;IACAC,OAAAC,EAAA;MACA;MACA,MAAAC,SAAA,GAAAD,EAAA,CAAAxC,KAAA,CAAAyC,SAAA;MACA,IAAAA,SAAA,oDAAApC,QAAA,CAAAoC,SAAA;QACA,KAAApF,cAAA,GAAAoF,SAAA;MACA;IACA;IACApF,eAAAqF,MAAA;MACA,IAAAA,MAAA;QACA,KAAAC,iBAAA;QACA,KAAAC,gBAAA;MACA,WAAAF,MAAA;QACA,KAAAG,WAAA;MACA,WAAAH,MAAA;QACA,KAAAI,gBAAA;MACA,WAAAJ,MAAA;QACA,KAAAC,iBAAA;QACA;QACA,KAAAI,eAAA;MACA;IACA;EACA;EAEAC,QAAA;IACA,KAAAF,gBAAA;IACA,KAAAH,iBAAA;IACA,MAAAF,SAAA,QAAAF,MAAA,CAAAvC,KAAA,CAAAyC,SAAA;IACA;IACA,KAAApF,cAAA,GAAAoF,SAAA;;IAEA;IACA,KAAAM,eAAA,CAAAN,SAAA;IACA,KAAAQ,MAAA,CACA,WAAAV,MAAA,CAAAvC,KAAA,EACAkD,QAAA;MACA,IAAAA,QAAA,CAAAT,SAAA,oDAAApC,QAAA,CAAA6C,QAAA,CAAAT,SAAA;QACA,KAAApF,cAAA,GAAA6F,QAAA,CAAAT,SAAA;MACA;IACA,GACA;MAAAU,SAAA;IAAA,EACA;IACA,KAAAN,WAAA,GAAAO,IAAA;MACA;MACA,KAAAC,MAAA;;MAEA;MACA,MAAAZ,SAAA,QAAAF,MAAA,CAAAvC,KAAA,CAAAyC,SAAA;MACA,IAAAA,SAAA,oDAAApC,QAAA,CAAAoC,SAAA;QACA,KAAApF,cAAA,GAAAoF,SAAA;MACA;IACA;IACA,KAAAG,gBAAA;EACA;EACAU,cAAA;IACA,KAAAC,KAAA;EACA;EAEAC,OAAA;IAEAC,wBAAAC,OAAA,EAAAhC,IAAA;MACA,KAAAjE,mBAAA,GAAAiG,OAAA;MACA,KAAAhG,gBAAA,GAAAgE,IAAA;MACA,KAAAlE,gBAAA;IACA;IAEAmG,cAAAC,OAAA;MACA;MACA,KAAAvG,cAAA,GAAAuG,OAAA;;MAEA;MACA,KAAAC,OAAA,CAAAC,OAAA;QACA9D,KAAA;UACA,QAAAuC,MAAA,CAAAvC,KAAA;UAAA;UACAyC,SAAA,EAAAmB,OAAA;QACA;MACA;;MAEA;MACA,IAAAA,OAAA;QACA,KAAAf,WAAA;MACA,WAAAe,OAAA;QACA,KAAAjB,iBAAA;QACA,KAAAC,gBAAA;MACA,WAAAgB,OAAA;QACA,KAAAG,cAAA;MACA,WAAAH,OAAA;QACA,KAAAd,gBAAA;MACA;IACA;IACAC,gBAAAa,OAAA;MACA,QAAAA,OAAA;QACA;UACA,KAAAf,WAAA;UACA;QACA;UACA,KAAAF,iBAAA;UACA,KAAAC,gBAAA;UACA;QACA;UACA,KAAAmB,cAAA;UACA;QACA;UACA,KAAAjB,gBAAA;UACA;MAAA;IACA;IACA;IACA,MAAAA,iBAAA;MACA;QACA,MAAAkB,QAAA,SAAAjH,WAAA;QACA,IAAAiH,QAAA,CAAA5G,IAAA,CAAA6G,IAAA;UACA,KAAA1G,WAAA,GAAAyG,QAAA,CAAA5G,IAAA,CAAAA,IAAA,CAAA0B,OAAA;UACA,KAAAyE,KAAA;QACA;MACA,SAAAW,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;MACA;IACA;IAEAE,0BAAA;MACA,UAAArF,oBAAA,UAAAC,kBAAA;QACA,KAAAmF,QAAA,CAAAD,KAAA;QACA;MACA;MAEA,MAAA3C,SAAA,OAAAN,IAAA,MAAAlC,oBAAA;MACA,MAAAyC,OAAA,OAAAP,IAAA,MAAAjC,kBAAA;MAEA,IAAAuC,SAAA,GAAAC,OAAA;QACA,KAAA2C,QAAA,CAAAD,KAAA;QACA;MACA;MAEA,KAAA5E,SAAA;MACA,KAAAyE,cAAA;IACA;;IAEA;IACAM,oBAAAC,cAAA;MACA,KAAAA,cAAA;MAEA;QACA,MAAAC,IAAA,OAAAtD,IAAA,CAAAqD,cAAA;QACA;QACA,OAAAC,IAAA,CAAAC,cAAA;UACAC,IAAA;UACAC,KAAA;UACAC,GAAA;UACAC,IAAA;UACAC,MAAA;UACAC,MAAA;UACAC,MAAA;QACA,GAAAjB,OAAA;MACA,SAAAkB,CAAA;QACA;MACA;IACA;IAEA;IACAC,kBAAAC,SAAA,EAAAC,OAAA;MACA,KAAAD,SAAA;MAEA;QACA,MAAAE,KAAA,OAAAnE,IAAA,CAAAiE,SAAA;QACA,MAAAG,GAAA,GAAAF,OAAA,OAAAlE,IAAA,CAAAkE,OAAA,QAAAlE,IAAA;;QAEA;QACA,IAAAqE,KAAA,CAAAF,KAAA,CAAAG,OAAA;QACA,IAAAJ,OAAA,IAAAG,KAAA,CAAAD,GAAA,CAAAE,OAAA;;QAEA;QACA,IAAAH,KAAA,OAAAnE,IAAA;UACA;QACA;;QAEA;QACA,KAAAkE,OAAA,IAAAE,GAAA,OAAApE,IAAA;UACA,MAAAuE,MAAA,OAAAvE,IAAA,KAAAmE,KAAA;UACA,MAAAK,KAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,MAAA;UACA,MAAAI,OAAA,GAAAF,IAAA,CAAAC,KAAA,CAAAH,MAAA;UAEA,UAAAC,KAAA,KAAAG,OAAA;QACA;;QAEA;QACA,MAAAJ,MAAA,GAAAH,GAAA,GAAAD,KAAA;QACA,MAAAK,KAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,MAAA;QACA,MAAAI,OAAA,GAAAF,IAAA,CAAAC,KAAA,CAAAH,MAAA;QAEA,IAAAC,KAAA;UACA,UAAAA,KAAA,KAAAG,OAAA;QACA;QACA,UAAAA,OAAA;MACA,SAAAZ,CAAA;QACA;MACA;IACA;IAIAa,mBAAA;MACA;MACA,KAAAhC,OAAA,CAAAC,OAAA;QACAgC,IAAA;QACA9F,KAAA;UACAyC,SAAA;UACAsD,SAAA,EAAA9E,IAAA,CAAA+E,GAAA;QACA;MACA;IACA;;IAEA;IACA,MAAAnD,YAAA;MACA,KAAA7E,YAAA;MACA,KAAAC,UAAA;MACA;QACA,MAAA+F,QAAA,SAAAjH,WAAA;;QAEA;QACA,KAAAY,SAAA,GAAAqG,QAAA,CAAA5G,IAAA,CAAAA,IAAA,CAAA6I,GAAA,CAAA9F,KAAA;UACAX,EAAA,EAAAW,KAAA,CAAAjD,IAAA;UACAkD,YAAA,EAAAD,KAAA,CAAA+F,UAAA;UACAC,cAAA,OAAAC,cAAA,CAAAjG,KAAA,CAAAkG,WAAA;UACAC,cAAA,EAAAnG,KAAA,CAAAoG,oBAAA;UACAC,WAAA,EAAArG,KAAA,CAAAsG,WAAA;UACAC,UAAA,OAAAC,kBAAA,CAAAxG,KAAA;UACAyG,QAAA,EAAAzG,KAAA,CAAA0G,cAAA;UACAvF,UAAA,EAAAnB,KAAA,CAAA2G,WAAA;UACA7E,SAAA,EAAA9B,KAAA,CAAAjD,IAAA;UACA6J,MAAA,EAAA5G,KAAA,CAAA6G,MAAA;UACAC,SAAA,EAAA9G,KAAA,CAAA+G,oBAAA;UACAC,YAAA,EAAAhH,KAAA,CAAAgH,YAAA;UACAC,SAAA,EAAAjH,KAAA,CAAAkH,kBAAA;UACAC,WAAA,EAAAnH,KAAA,CAAAmH,WAAA;UACAC,UAAA,EAAApH,KAAA,CAAAqH,SAAA;UACAC,MAAA,EAAAtH,KAAA,CAAAuH,eAAA;UACAC,OAAA,EAAAxH;QACA,IAAAyH,IAAA,EAAAC,CAAA,EAAAC,CAAA;UACA;UACA,WAAA7G,IAAA,CAAA6G,CAAA,CAAAxG,UAAA,QAAAL,IAAA,CAAA4G,CAAA,CAAAvG,UAAA;QACA;MACA,SAAA4C,KAAA;QACA,KAAAjG,UAAA;QACA;MACA;QACA,KAAAD,YAAA;MACA;IACA;IAEA;IACA,MAAA2E,kBAAA;MACA,KAAAlE,kBAAA;MACA,KAAAC,gBAAA;MACA;QACA,MAAAsF,QAAA,SAAAjH,WAAA;QACA,MAAA4K,OAAA,GAAA3D,QAAA,CAAA5G,IAAA,CAAAA,IAAA;;QAEA;QACA,MAAA2K,SAAA,OAAAC,GAAA;QACAL,OAAA,CAAAM,OAAA,CAAA7G,WAAA;UACA;UACA,MAAA8G,QAAA,GAAA9G,WAAA,CAAA+G,QAAA,IAAA/G,WAAA,CAAAgH,YAAA;UAEA,KAAAL,SAAA,CAAAM,GAAA,CAAAH,QAAA;YACAH,SAAA,CAAAO,GAAA,CAAAJ,QAAA;cACAvG,cAAA,EAAAP,WAAA,CAAAmH,cAAA,IAAAnH,WAAA,CAAAoH,cAAA;cACA9G,IAAA,EAAAN,WAAA,CAAAqH,YAAA;cACAC,QAAA,OAAAC,UAAA,CAAAvH,WAAA,CAAAqH,YAAA;cACArG,MAAA,EAAAhB,WAAA,CAAAwH,WAAA,IAAAxH,WAAA,CAAAyH,eAAA;cACAvH,UAAA,EAAAF,WAAA,CAAA0F,WAAA;cACAgC,eAAA,EAAA1H,WAAA,CAAAkF,cAAA;cACAyC,MAAA,OAAAC,oBAAA,CAAA5H,WAAA,CAAAiF,WAAA;cACA4C,WAAA,EAAA7H,WAAA,CAAA6H,WAAA;YACA;UACA;QACA;QAEA,KAAA/K,eAAA,GAAAgL,KAAA,CAAAC,IAAA,CAAApB,SAAA,CAAAqB,MAAA;MACA,SAAAlF,KAAA;QACA,KAAAzF,kBAAA;MACA;QACA,KAAAA,kBAAA;MACA;IACA;IAGA,MAAAsF,eAAA;MACA,KAAA3E,YAAA;MACA,KAAAC,UAAA;MACA;QACA,IAAAgK,MAAA;;QAEA;QACA,SAAAnK,cAAA,sBAAAH,oBAAA,SAAAC,kBAAA;UACAqK,MAAA,CAAAC,UAAA,QAAAvK,oBAAA;UACAsK,MAAA,CAAAE,QAAA,QAAAvK,kBAAA;QACA,gBAAAE,cAAA;UACA;UACA,MAAA4B,IAAA,GAAAC,QAAA,MAAA7B,cAAA;UACA,MAAA8B,UAAA,OAAAC,IAAA;UACAD,UAAA,CAAAE,OAAA,CAAAF,UAAA,CAAAG,OAAA,KAAAL,IAAA;UACAuI,MAAA,CAAAC,UAAA,GAAAtI,UAAA,CAAAwI,WAAA,GAAAC,KAAA;QACA;;QAEA;QACA,SAAAtK,cAAA;UACAkK,MAAA,CAAApH,SAAA,QAAA9C,cAAA;QACA;QACA;QACA,MAAA6E,QAAA,SAAAjH,WAAA;;QAEA;QACA;;QAEA;QACA,MAAA2M,QAAA,KAEA;QAEA,KAAAzK,SAAA,GAAAyK,QAAA;QACA,YAAAzK,SAAA;MAEA,SAAAiF,KAAA;QACA,KAAA7E,UAAA;QACA;QACA;MACA;QACA,KAAAD,YAAA;MACA;IACA;IAMA;IACA;IACA,MAAAwD,iBAAA;MACA;QACA,MAAAoB,QAAA,SAAAjH,WAAA;QACA,MAAA4M,YAAA,GAAA3F,QAAA,CAAA5G,IAAA,CAAAA,IAAA;;QAEA;QACA,MAAA2K,SAAA,OAAAC,GAAA;QACA2B,YAAA,CAAA1B,OAAA,CAAA2B,CAAA;UACA,MAAA1B,QAAA,GAAA0B,CAAA,CAAAzB,QAAA,IAAAyB,CAAA,CAAAxB,YAAA;UACA,KAAAL,SAAA,CAAAM,GAAA,CAAAH,QAAA;YACAH,SAAA,CAAAO,GAAA,CAAAJ,QAAA,EAAA0B,CAAA;UACA;QACA;QAEA,MAAAC,kBAAA,GAAAX,KAAA,CAAAC,IAAA,CAAApB,SAAA,CAAAqB,MAAA;;QAEA;QACA,MAAAxK,aAAA,GAAAiL,kBAAA,CACA3J,MAAA,CAAA0J,CAAA,IAAAA,CAAA,CAAAnB,YAAA,WACAqB,MAAA,EAAAC,GAAA,EAAAH,CAAA,KAAAG,GAAA,IAAAC,UAAA,CAAAJ,CAAA,CAAAhB,WAAA;;QAEA;QACA,MAAA/J,YAAA,GAAAgL,kBAAA,CACA3J,MAAA,CAAA0J,CAAA,IAAAA,CAAA,CAAAnB,YAAA,WACAqB,MAAA,EAAAC,GAAA,EAAAH,CAAA,KAAAG,GAAA,IAAAC,UAAA,CAAAJ,CAAA,CAAAf,eAAA;QAEA,KAAAlK,WAAA;UACAC,aAAA;UACAC,YAAA;UACAC,OAAA,OAAAvB;QACA;MACA,SAAA2G,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;MACA;IACA;IAEA;IACAkC,eAAA2C,MAAA;MACA,MAAAkB,SAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;;MACA,OAAAA,SAAA,CAAAlB,MAAA,KAAAA,MAAA;IACA;IAEA;IACAJ,WAAAjH,IAAA;MACA,OAAAA,IAAA;IACA;IAEA;IACAsH,qBAAAD,MAAA;MACA,MAAAkB,SAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;;MACA,OAAAA,SAAA,CAAAlB,MAAA,KAAAA,MAAA;IACA;IAEA;IACApC,mBAAAxG,KAAA;MACA,KAAAA,KAAA,CAAA+J,SAAA,SAAA/J,KAAA,CAAAsG,WAAA;;MAEA;MACA,QAAAtG,KAAA,CAAAgK,UAAA;QACA;UACA,OAAAhK,KAAA,CAAA+J,SAAA,CAAAE,UAAA;QACA;UACA,OAAAjK,KAAA,CAAA+J,SAAA,CAAAG,SAAA;QACA;UACA,OAAAlK,KAAA,CAAA+J,SAAA,CAAAI,WAAA;QACA;UACA,OAAAnK,KAAA,CAAA+J,SAAA,CAAAK,UAAA;QACA;UACA,OAAApK,KAAA,CAAAsG,WAAA;MAAA;IAEA;IAEA;IACA+D,YAAAC,KAAA;MACA,IAAAnF,KAAA,CAAAmF,KAAA;MACA,OAAAT,UAAA,CAAAS,KAAA,EAAAC,OAAA;IACA;IAEAC,WAAAC,UAAA;MACA,WAAA3J,IAAA,CAAA2J,UAAA,EAAAC,kBAAA;IACA;IAEAC,eAAAF,UAAA;MACA,KAAAA,UAAA;MACA,MAAArG,IAAA,OAAAtD,IAAA,CAAA2J,UAAA;MACA,OAAArG,IAAA,CAAAC,cAAA;IACA;IAEA;IACAuG,sBAAAhC,MAAA;MACA;QACA;QACA;QACA;QACA;QACA;MACA,EAAAA,MAAA;IACA;IAEAiC,qBAAAjC,MAAA;MACA;QACA;QACA;QACA;QACA;QACA;MACA,EAAAA,MAAA,KAAAA,MAAA;IACA;IAEAkC,sBAAAC,MAAA;MACA;QACA;QACA;QACA;QACA;QACA;MACA,EAAAA,MAAA;IACA;IAEAC,qBAAAD,MAAA;MACA;QACA;QACA;QACA;QACA;MACA,EAAAA,MAAA,KAAAA,MAAA;IACA;IAEAE,aAAA;MACA,KAAAvN,WAAA;IACA;IAEAwN,iBAAA;MACA,KAAAzN,gBAAA;MACA,KAAAC,WAAA;IACA;IAEAwF,OAAAiI,KAAA;MACA,IAAAA,KAAA;QACA,KAAA3N,SAAA,CAAAiK,IAAA,EAAAC,CAAA,EAAAC,CAAA;UACA,WAAA7G,IAAA,CAAA6G,CAAA,CAAAwD,KAAA,SAAArK,IAAA,CAAA4G,CAAA,CAAAyD,KAAA;QACA;MACA;QACA,KAAA3N,SAAA,CAAAiK,IAAA,EAAAC,CAAA,EAAAC,CAAA;UACA,OAAAD,CAAA,CAAAyD,KAAA,IAAAxD,CAAA,CAAAwD,KAAA;QACA;MACA;IACA;IAEAC,iBAAApL,KAAA;MACA,KAAApC,aAAA,GAAAoC,KAAA;MACA,KAAArC,gBAAA;MACA,KAAA0N,SAAA;QACAC,QAAA,CAAAC,aAAA,mBAAAC,cAAA;UAAAC,QAAA;QAAA;MACA;IACA;IAEAC,cAAA;MACA,KAAA/N,gBAAA;MACAgO,MAAA,CAAAC,QAAA;QAAAC,GAAA;QAAAJ,QAAA;MAAA;IACA;IAEAK,SAAAC,IAAA;MACA,KAAArO,WAAA,GAAAqO,IAAA;IACA;IAEAC,qBAAAC,IAAA;MACA,KAAA9O,QAAA,GAAA8O,IAAA;MACA,KAAAvO,WAAA;IACA;IAEA;IACAwO,mBAAA;MACA,KAAA/N,eAAA;IACA;IAEAgO,qBAAA;MACA,SAAA/N,eAAA,SAAAC,aAAA;QACA,KAAAF,eAAA;MACA;IACA;IAEAiO,wBAAA7K,IAAA;MACA;QACA;QACA;QACA;MACA,EAAAA,IAAA;IACA;IAEA8K,uBAAA9K,IAAA;MACA;QACA;QACA;QACA;MACA,EAAAA,IAAA,KAAAA,IAAA;IACA;IAEA+K,0BAAAC,OAAA;MACA;QACA;QACA;MACA,EAAAA,OAAA,KAAAA,OAAA;IACA;IAEAC,0BAAA5D,MAAA;MACA;QACA;QACA;QACA;QACA;MACA,EAAAA,MAAA;IACA;IAEA6D,yBAAA7D,MAAA;MACA;QACA;QACA;QACA;QACA;MACA,EAAAA,MAAA,KAAAA,MAAA;IACA;IAEA;IACA8D,oBAAA9D,MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,EAAAA,MAAA;IACA;IAEA+D,mBAAA/D,MAAA;MACA;QACA;QACA;QACA;QACA;QACA;MACA,EAAAA,MAAA,KAAAA,MAAA;IACA;IAEAgE,aAAAjL,MAAA;MACA,KAAAqC,QAAA,CAAA6I,OAAA,QAAAlL,MAAA,CAAAG,SAAA;IACA;IAEA;IACAI,kBAAA;MACA,YAAA3C,cAAA,SAAAC,oBAAA;IACA;IAEAsN,wBAAA;MACA,KAAAvN,cAAA;IACA;IAEA,MAAAwN,eAAA;MACA,MAAA9K,MAAA,QAAAC,iBAAA;MACA,KAAAD,MAAA,IAAAA,MAAA;QACA;QACA,KAAAqB,uBAAA;QAEA;MACA;MAEA,KAAA5D,eAAA;MAEA;QACA;QACA,MAAAmE,QAAA,SAAAhH,UAAA;UAAAoF,MAAA,EAAAA;QAAA;QACA,IAAA4B,QAAA,CAAA5G,IAAA,CAAAiD,QAAA;UACA;UACA,KAAAoD,uBAAA,CAAAO,QAAA,CAAA5G,IAAA;UACA;QACA;QACA;QACA,MAAA+P,OAAA,GAAA1B,QAAA,CAAA2B,aAAA;QAEAD,OAAA,CAAAE,SAAA,GAAArJ,QAAA,CAAA5G,IAAA;QACAqO,QAAA,CAAA6B,IAAA,CAAAC,WAAA,CAAAJ,OAAA;QACA,MAAAK,IAAA,GAAAL,OAAA,CAAAzB,aAAA;;QAEA;;QAIA,IAAA8B,IAAA;UACA,KAAA/J,uBAAA;UACA+J,IAAA,CAAAC,MAAA;UACAD,IAAA,CAAAE,MAAA;UACA;;UAEA;UACAC,UAAA;YACA,KAAA7K,gBAAA;YACA,KAAAH,iBAAA;UACA;QACA;UACA,KAAAc,uBAAA;QACA;MACA,SAAAS,KAAA;QACA,KAAAT,uBAAA;MACA;QACA,KAAA5D,eAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}