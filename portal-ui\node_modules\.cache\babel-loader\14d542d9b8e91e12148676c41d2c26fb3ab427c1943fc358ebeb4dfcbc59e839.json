{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"Layout\", [_c(\"div\", {\n    staticClass: \"layout-container\",\n    staticStyle: {\n      width: \"100%\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"page-header\"\n  }, [_c(\"div\", {\n    staticClass: \"am-container\"\n  }, [_c(\"h1\", {\n    staticClass: \"page-header-title\"\n  }, [_vm._v(\"客户案例\")])])]), _c(\"div\", {\n    staticClass: \"breadcrumb-box\"\n  }, [_c(\"div\", {\n    staticClass: \"am-container\"\n  }, [_c(\"ol\", {\n    staticClass: \"am-breadcrumb\"\n  }, [_c(\"li\", [_c(\"router-link\", {\n    attrs: {\n      to: \"/\"\n    }\n  }, [_vm._v(\"首页\")])], 1), _c(\"li\", {\n    staticClass: \"am-active\"\n  }, [_vm._v(\"客户案例\")])])])])]), _c(\"div\", {\n    staticClass: \"section example\"\n  }, [_c(\"div\", {\n    staticClass: \"container\",\n    staticStyle: {\n      \"max-width\": \"1160px\",\n      margin: \"0 auto\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"section--header\"\n  }, [_c(\"h2\", {\n    staticClass: \"section--title\"\n  }, [_vm._v(\"全球首创 自主创新\")]), _c(\"p\", {\n    staticClass: \"section--description\"\n  }, [_vm._v(\" Enterplorer Studio是一套面向企业级移动信息化建设的开发平台。集聚开发、测试、 \"), _c(\"br\"), _vm._v(\"打包、发布于一体的移动化开发综合平台。 \")])]), _c(\"div\", {\n    staticClass: \"example-container\"\n  }, [_c(\"div\", {\n    staticClass: \"am-tabs\"\n  }, [_c(\"ul\", {\n    staticClass: \"am-tabs-nav am-nav am-nav-tabs am-g\"\n  }, _vm._l(_vm.tabList, function (tab, index) {\n    return _c(\"li\", {\n      key: index,\n      staticClass: \"am-u-md-2\",\n      class: _vm.tabIndex === index ? \"am-active\" : \"\",\n      on: {\n        click: function ($event) {\n          $event.preventDefault();\n          return _vm.changeTab(index);\n        }\n      }\n    }, [_c(\"a\", {\n      attrs: {\n        href: \"#\"\n      }\n    }, [_c(\"i\", {\n      class: tab.icon\n    }), _vm._v(_vm._s(tab.name))])]);\n  }), 0), _c(\"div\", {\n    staticClass: \"tabs\"\n  }, _vm._l(_vm.list, function (image, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"tab\"\n    }, [_c(\"img\", {\n      attrs: {\n        src: image,\n        alt: \"\"\n      }\n    })]);\n  }), 0)])])])])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "width", "_v", "attrs", "to", "margin", "_l", "tabList", "tab", "index", "key", "class", "tabIndex", "on", "click", "$event", "preventDefault", "changeTab", "href", "icon", "_s", "name", "list", "image", "src", "alt", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/views/ExampleView.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"Layout\", [\n    _c(\n      \"div\",\n      { staticClass: \"layout-container\", staticStyle: { width: \"100%\" } },\n      [\n        _c(\"div\", { staticClass: \"page-header\" }, [\n          _c(\"div\", { staticClass: \"am-container\" }, [\n            _c(\"h1\", { staticClass: \"page-header-title\" }, [\n              _vm._v(\"客户案例\"),\n            ]),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"breadcrumb-box\" }, [\n          _c(\"div\", { staticClass: \"am-container\" }, [\n            _c(\"ol\", { staticClass: \"am-breadcrumb\" }, [\n              _c(\n                \"li\",\n                [_c(\"router-link\", { attrs: { to: \"/\" } }, [_vm._v(\"首页\")])],\n                1\n              ),\n              _c(\"li\", { staticClass: \"am-active\" }, [_vm._v(\"客户案例\")]),\n            ]),\n          ]),\n        ]),\n      ]\n    ),\n    _c(\"div\", { staticClass: \"section example\" }, [\n      _c(\n        \"div\",\n        {\n          staticClass: \"container\",\n          staticStyle: { \"max-width\": \"1160px\", margin: \"0 auto\" },\n        },\n        [\n          _c(\"div\", { staticClass: \"section--header\" }, [\n            _c(\"h2\", { staticClass: \"section--title\" }, [\n              _vm._v(\"全球首创 自主创新\"),\n            ]),\n            _c(\"p\", { staticClass: \"section--description\" }, [\n              _vm._v(\n                \" Enterplorer Studio是一套面向企业级移动信息化建设的开发平台。集聚开发、测试、 \"\n              ),\n              _c(\"br\"),\n              _vm._v(\"打包、发布于一体的移动化开发综合平台。 \"),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"example-container\" }, [\n            _c(\"div\", { staticClass: \"am-tabs\" }, [\n              _c(\n                \"ul\",\n                { staticClass: \"am-tabs-nav am-nav am-nav-tabs am-g\" },\n                _vm._l(_vm.tabList, function (tab, index) {\n                  return _c(\n                    \"li\",\n                    {\n                      key: index,\n                      staticClass: \"am-u-md-2\",\n                      class: _vm.tabIndex === index ? \"am-active\" : \"\",\n                      on: {\n                        click: function ($event) {\n                          $event.preventDefault()\n                          return _vm.changeTab(index)\n                        },\n                      },\n                    },\n                    [\n                      _c(\"a\", { attrs: { href: \"#\" } }, [\n                        _c(\"i\", { class: tab.icon }),\n                        _vm._v(_vm._s(tab.name)),\n                      ]),\n                    ]\n                  )\n                }),\n                0\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"tabs\" },\n                _vm._l(_vm.list, function (image, index) {\n                  return _c(\"div\", { key: index, staticClass: \"tab\" }, [\n                    _c(\"img\", { attrs: { src: image, alt: \"\" } }),\n                  ])\n                }),\n                0\n              ),\n            ]),\n          ]),\n        ]\n      ),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,QAAQ,EAAE,CAClBA,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,kBAAkB;IAAEC,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EACnE,CACEJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC7CH,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCF,EAAE,CACA,IAAI,EACJ,CAACA,EAAE,CAAC,aAAa,EAAE;IAAEM,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAC3D,CAAC,CACF,EACDL,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACzD,CAAC,CACH,CAAC,CACH,CAAC,CACH,CACF,EACDL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxBC,WAAW,EAAE;MAAE,WAAW,EAAE,QAAQ;MAAEK,MAAM,EAAE;IAAS;EACzD,CAAC,EACD,CACER,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACM,EAAE,CAAC,WAAW,CAAC,CACpB,CAAC,EACFL,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CAC/CH,GAAG,CAACM,EAAE,CACJ,mDAAmD,CACpD,EACDL,EAAE,CAAC,IAAI,CAAC,EACRD,GAAG,CAACM,EAAE,CAAC,sBAAsB,CAAC,CAC/B,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCF,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAsC,CAAC,EACtDH,GAAG,CAACU,EAAE,CAACV,GAAG,CAACW,OAAO,EAAE,UAAUC,GAAG,EAAEC,KAAK,EAAE;IACxC,OAAOZ,EAAE,CACP,IAAI,EACJ;MACEa,GAAG,EAAED,KAAK;MACVV,WAAW,EAAE,WAAW;MACxBY,KAAK,EAAEf,GAAG,CAACgB,QAAQ,KAAKH,KAAK,GAAG,WAAW,GAAG,EAAE;MAChDI,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvBA,MAAM,CAACC,cAAc,EAAE;UACvB,OAAOpB,GAAG,CAACqB,SAAS,CAACR,KAAK,CAAC;QAC7B;MACF;IACF,CAAC,EACD,CACEZ,EAAE,CAAC,GAAG,EAAE;MAAEM,KAAK,EAAE;QAAEe,IAAI,EAAE;MAAI;IAAE,CAAC,EAAE,CAChCrB,EAAE,CAAC,GAAG,EAAE;MAAEc,KAAK,EAAEH,GAAG,CAACW;IAAK,CAAC,CAAC,EAC5BvB,GAAG,CAACM,EAAE,CAACN,GAAG,CAACwB,EAAE,CAACZ,GAAG,CAACa,IAAI,CAAC,CAAC,CACzB,CAAC,CACH,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,EACDxB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAO,CAAC,EACvBH,GAAG,CAACU,EAAE,CAACV,GAAG,CAAC0B,IAAI,EAAE,UAAUC,KAAK,EAAEd,KAAK,EAAE;IACvC,OAAOZ,EAAE,CAAC,KAAK,EAAE;MAAEa,GAAG,EAAED,KAAK;MAAEV,WAAW,EAAE;IAAM,CAAC,EAAE,CACnDF,EAAE,CAAC,KAAK,EAAE;MAAEM,KAAK,EAAE;QAAEqB,GAAG,EAAED,KAAK;QAAEE,GAAG,EAAE;MAAG;IAAE,CAAC,CAAC,CAC9C,CAAC;EACJ,CAAC,CAAC,EACF,CAAC,CACF,CACF,CAAC,CACH,CAAC,CACH,CACF,CACF,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB/B,MAAM,CAACgC,aAAa,GAAG,IAAI;AAE3B,SAAShC,MAAM,EAAE+B,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}