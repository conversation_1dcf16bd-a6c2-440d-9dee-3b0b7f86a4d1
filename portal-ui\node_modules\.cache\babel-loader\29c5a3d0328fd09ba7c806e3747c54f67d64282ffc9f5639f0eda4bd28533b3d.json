{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"transition\", {\n    attrs: {\n      name: \"slide\"\n    }\n  }, [_vm.visible ? _c(\"div\", {\n    class: [\"notification\", `notification-${_vm.type}`],\n    style: {\n      minHeight: _vm.minHeight\n    },\n    attrs: {\n      role: \"alert\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"notification-content\"\n  }, [_vm.type === \"error\" ? _c(\"div\", {\n    staticClass: \"icon-wrapper\"\n  }, [_c(\"span\", {\n    staticClass: \"error-icon\"\n  }, [_vm._v(\"×\")])]) : _vm._e(), _c(\"span\", {\n    staticClass: \"message\"\n  }, [_vm._v(_vm._s(_vm.message))]), _vm.closable ? _c(\"button\", {\n    staticClass: \"close-btn\",\n    on: {\n      click: _vm.close\n    }\n  }, [_vm._v(\"×\")]) : _vm._e()])]) : _vm._e()]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "name", "visible", "class", "type", "style", "minHeight", "role", "staticClass", "_v", "_e", "_s", "message", "closable", "on", "click", "close", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/components/common/header/SlideNotification.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"transition\", { attrs: { name: \"slide\" } }, [\n    _vm.visible\n      ? _c(\n          \"div\",\n          {\n            class: [\"notification\", `notification-${_vm.type}`],\n            style: { minHeight: _vm.minHeight },\n            attrs: { role: \"alert\" },\n          },\n          [\n            _c(\"div\", { staticClass: \"notification-content\" }, [\n              _vm.type === \"error\"\n                ? _c(\"div\", { staticClass: \"icon-wrapper\" }, [\n                    _c(\"span\", { staticClass: \"error-icon\" }, [_vm._v(\"×\")]),\n                  ])\n                : _vm._e(),\n              _c(\"span\", { staticClass: \"message\" }, [\n                _vm._v(_vm._s(_vm.message)),\n              ]),\n              _vm.closable\n                ? _c(\n                    \"button\",\n                    { staticClass: \"close-btn\", on: { click: _vm.close } },\n                    [_vm._v(\"×\")]\n                  )\n                : _vm._e(),\n            ]),\n          ]\n        )\n      : _vm._e(),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,YAAY,EAAE;IAAEE,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAQ;EAAE,CAAC,EAAE,CACpDJ,GAAG,CAACK,OAAO,GACPJ,EAAE,CACA,KAAK,EACL;IACEK,KAAK,EAAE,CAAC,cAAc,EAAG,gBAAeN,GAAG,CAACO,IAAK,EAAC,CAAC;IACnDC,KAAK,EAAE;MAAEC,SAAS,EAAET,GAAG,CAACS;IAAU,CAAC;IACnCN,KAAK,EAAE;MAAEO,IAAI,EAAE;IAAQ;EACzB,CAAC,EACD,CACET,EAAE,CAAC,KAAK,EAAE;IAAEU,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDX,GAAG,CAACO,IAAI,KAAK,OAAO,GAChBN,EAAE,CAAC,KAAK,EAAE;IAAEU,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCV,EAAE,CAAC,MAAM,EAAE;IAAEU,WAAW,EAAE;EAAa,CAAC,EAAE,CAACX,GAAG,CAACY,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACzD,CAAC,GACFZ,GAAG,CAACa,EAAE,EAAE,EACZZ,EAAE,CAAC,MAAM,EAAE;IAAEU,WAAW,EAAE;EAAU,CAAC,EAAE,CACrCX,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,OAAO,CAAC,CAAC,CAC5B,CAAC,EACFf,GAAG,CAACgB,QAAQ,GACRf,EAAE,CACA,QAAQ,EACR;IAAEU,WAAW,EAAE,WAAW;IAAEM,EAAE,EAAE;MAAEC,KAAK,EAAElB,GAAG,CAACmB;IAAM;EAAE,CAAC,EACtD,CAACnB,GAAG,CAACY,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,GACDZ,GAAG,CAACa,EAAE,EAAE,CACb,CAAC,CACH,CACF,GACDb,GAAG,CAACa,EAAE,EAAE,CACb,CAAC;AACJ,CAAC;AACD,IAAIO,eAAe,GAAG,EAAE;AACxBrB,MAAM,CAACsB,aAAa,GAAG,IAAI;AAE3B,SAAStB,MAAM,EAAEqB,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}