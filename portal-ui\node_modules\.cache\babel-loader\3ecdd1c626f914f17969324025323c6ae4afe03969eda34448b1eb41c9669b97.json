{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as THREE from 'three';\nexport default {\n  name: 'Globe3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      globe: null,\n      nodes: [],\n      selectedNode: null,\n      mousePosition: {\n        x: 0,\n        y: 0\n      },\n      animationId: null,\n      // 全球算力节点数据\n      computeNodes: [{\n        name: '北京节点',\n        lat: 39.9042,\n        lng: 116.4074,\n        description: '华北地区主要算力中心',\n        gpuCount: '2000+',\n        computePower: '500 PFLOPS'\n      }, {\n        name: '上海节点',\n        lat: 31.2304,\n        lng: 121.4737,\n        description: '华东地区核心算力枢纽',\n        gpuCount: '1800+',\n        computePower: '450 PFLOPS'\n      }, {\n        name: '深圳节点',\n        lat: 22.3193,\n        lng: 114.1694,\n        description: '华南地区智算中心',\n        gpuCount: '1500+',\n        computePower: '380 PFLOPS'\n      }, {\n        name: '成都节点',\n        lat: 30.5728,\n        lng: 104.0668,\n        description: '西南地区算力基地',\n        gpuCount: '1200+',\n        computePower: '300 PFLOPS'\n      }, {\n        name: '杭州节点',\n        lat: 30.2741,\n        lng: 120.1551,\n        description: '长三角算力集群',\n        gpuCount: '1000+',\n        computePower: '250 PFLOPS'\n      }, {\n        name: '新加坡节点',\n        lat: 1.3521,\n        lng: 103.8198,\n        description: '东南亚算力中心',\n        gpuCount: '800+',\n        computePower: '200 PFLOPS'\n      }]\n    };\n  },\n  computed: {\n    nodeInfoStyle() {\n      return {\n        left: this.mousePosition.x + 20 + 'px',\n        top: this.mousePosition.y - 50 + 'px'\n      };\n    }\n  },\n  mounted() {\n    this.initThree();\n    this.createGlobe();\n    this.createNodes();\n    this.animate();\n    this.addEventListeners();\n  },\n  beforeDestroy() {\n    this.cleanup();\n  },\n  methods: {\n    initThree() {\n      // 创建场景\n      this.scene = new THREE.Scene();\n\n      // 创建相机\n      const container = this.$refs.canvasContainer;\n      const width = container.clientWidth;\n      const height = container.clientHeight;\n      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);\n      this.camera.position.z = 3;\n\n      // 创建渲染器\n      this.renderer = new THREE.WebGLRenderer({\n        antialias: true,\n        alpha: true\n      });\n      this.renderer.setSize(width, height);\n      this.renderer.setClearColor(0x000000, 0);\n      container.appendChild(this.renderer.domElement);\n\n      // 添加环境光\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.6);\n      this.scene.add(ambientLight);\n\n      // 添加方向光\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n      directionalLight.position.set(1, 1, 1);\n      this.scene.add(directionalLight);\n    },\n    createGlobe() {\n      // 创建地球几何体\n      const geometry = new THREE.SphereGeometry(1, 64, 64);\n\n      // 创建地球材质 - 使用更真实的地球效果\n      const material = new THREE.MeshPhongMaterial({\n        color: 0x2E86AB,\n        transparent: true,\n        opacity: 0.9,\n        shininess: 100,\n        wireframe: false\n      });\n      this.globe = new THREE.Mesh(geometry, material);\n      this.scene.add(this.globe);\n\n      // 添加地球轮廓线 - 经纬线效果\n      const wireframeGeometry = new THREE.SphereGeometry(1.005, 32, 16);\n      const wireframeMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        wireframe: true,\n        transparent: true,\n        opacity: 0.4\n      });\n      const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial);\n      this.scene.add(wireframe);\n\n      // 添加大气层效果\n      const atmosphereGeometry = new THREE.SphereGeometry(1.1, 32, 32);\n      const atmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4A90FF,\n        transparent: true,\n        opacity: 0.1,\n        side: THREE.BackSide\n      });\n      const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);\n      this.scene.add(atmosphere);\n    },\n    createNodes() {\n      this.computeNodes.forEach(nodeData => {\n        const node = this.createNode(nodeData);\n        this.nodes.push(node);\n        this.scene.add(node.mesh);\n      });\n    },\n    createNode(nodeData) {\n      // 将经纬度转换为3D坐标\n      const phi = (90 - nodeData.lat) * (Math.PI / 180);\n      const theta = (nodeData.lng + 180) * (Math.PI / 180);\n      const x = -(1.05 * Math.sin(phi) * Math.cos(theta));\n      const y = 1.05 * Math.cos(phi);\n      const z = 1.05 * Math.sin(phi) * Math.sin(theta);\n\n      // 创建节点几何体\n      const geometry = new THREE.SphereGeometry(0.02, 16, 16);\n      const material = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true\n      });\n      const mesh = new THREE.Mesh(geometry, material);\n      mesh.position.set(x, y, z);\n\n      // 创建光环效果\n      const ringGeometry = new THREE.RingGeometry(0.03, 0.05, 16);\n      const ringMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        transparent: true,\n        opacity: 0.6,\n        side: THREE.DoubleSide\n      });\n      const ring = new THREE.Mesh(ringGeometry, ringMaterial);\n      ring.position.copy(mesh.position);\n      ring.lookAt(new THREE.Vector3(0, 0, 0));\n      this.scene.add(ring);\n      return {\n        mesh,\n        ring,\n        data: nodeData,\n        position: {\n          x,\n          y,\n          z\n        }\n      };\n    },\n    animate() {\n      this.animationId = requestAnimationFrame(this.animate);\n\n      // 旋转地球\n      if (this.globe) {\n        this.globe.rotation.y += 0.005;\n      }\n\n      // 节点脉冲动画\n      this.nodes.forEach((node, index) => {\n        const time = Date.now() * 0.001;\n        const scale = 1 + Math.sin(time * 2 + index) * 0.3;\n        node.mesh.scale.setScalar(scale);\n\n        // 光环旋转\n        if (node.ring) {\n          node.ring.rotation.z += 0.02;\n        }\n      });\n      this.renderer.render(this.scene, this.camera);\n    },\n    addEventListeners() {\n      window.addEventListener('resize', this.onWindowResize);\n      this.renderer.domElement.addEventListener('mousemove', this.onMouseMove);\n      this.renderer.domElement.addEventListener('click', this.onMouseClick);\n    },\n    onWindowResize() {\n      const container = this.$refs.canvasContainer;\n      const width = container.clientWidth;\n      const height = container.clientHeight;\n      this.camera.aspect = width / height;\n      this.camera.updateProjectionMatrix();\n      this.renderer.setSize(width, height);\n    },\n    onMouseMove(event) {\n      this.mousePosition.x = event.clientX;\n      this.mousePosition.y = event.clientY;\n\n      // 射线检测\n      const mouse = new THREE.Vector2();\n      const rect = this.renderer.domElement.getBoundingClientRect();\n      mouse.x = (event.clientX - rect.left) / rect.width * 2 - 1;\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;\n      const raycaster = new THREE.Raycaster();\n      raycaster.setFromCamera(mouse, this.camera);\n      const intersects = raycaster.intersectObjects(this.nodes.map(n => n.mesh));\n      if (intersects.length > 0) {\n        const intersectedNode = this.nodes.find(n => n.mesh === intersects[0].object);\n        this.selectedNode = intersectedNode ? intersectedNode.data : null;\n        this.renderer.domElement.style.cursor = 'pointer';\n      } else {\n        this.selectedNode = null;\n        this.renderer.domElement.style.cursor = 'default';\n      }\n    },\n    onMouseClick() {\n      if (this.selectedNode) {\n        this.$emit('node-selected', this.selectedNode);\n      }\n    },\n    cleanup() {\n      if (this.animationId) {\n        cancelAnimationFrame(this.animationId);\n      }\n      window.removeEventListener('resize', this.onWindowResize);\n      if (this.renderer) {\n        this.renderer.dispose();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["THREE", "name", "data", "scene", "camera", "renderer", "globe", "nodes", "selectedNode", "mousePosition", "x", "y", "animationId", "computeNodes", "lat", "lng", "description", "gpuCount", "computePower", "computed", "nodeInfoStyle", "left", "top", "mounted", "initThree", "createGlobe", "createNodes", "animate", "addEventListeners", "<PERSON><PERSON><PERSON><PERSON>", "cleanup", "methods", "Scene", "container", "$refs", "canvasContainer", "width", "clientWidth", "height", "clientHeight", "PerspectiveCamera", "position", "z", "WebGLRenderer", "antialias", "alpha", "setSize", "setClearColor", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "add", "directionalLight", "DirectionalLight", "set", "geometry", "SphereGeometry", "material", "MeshPhongMaterial", "color", "transparent", "opacity", "shininess", "wireframe", "<PERSON><PERSON>", "wireframeGeometry", "wireframeMaterial", "MeshBasicMaterial", "atmosphereGeometry", "atmosphereMaterial", "side", "BackSide", "atmosphere", "for<PERSON>ach", "nodeData", "node", "createNode", "push", "mesh", "phi", "Math", "PI", "theta", "sin", "cos", "ringGeometry", "RingGeometry", "ringMaterial", "DoubleSide", "ring", "copy", "lookAt", "Vector3", "requestAnimationFrame", "rotation", "index", "time", "Date", "now", "scale", "setScalar", "render", "window", "addEventListener", "onWindowResize", "onMouseMove", "onMouseClick", "aspect", "updateProjectionMatrix", "event", "clientX", "clientY", "mouse", "Vector2", "rect", "getBoundingClientRect", "raycaster", "Raycaster", "setFromCamera", "intersects", "intersectObjects", "map", "n", "length", "intersectedNode", "find", "object", "style", "cursor", "$emit", "cancelAnimationFrame", "removeEventListener", "dispose"], "sources": ["src/components/common/Globe3D.vue"], "sourcesContent": ["<template>\n  <div class=\"globe-container\" ref=\"globeContainer\">\n    <div class=\"globe-canvas\" ref=\"canvasContainer\"></div>\n    <div class=\"globe-overlay\">\n      <div class=\"globe-title\">全球算力布局</div>\n      <div class=\"globe-subtitle\">分布式高性能计算网络</div>\n      <div class=\"node-info\" v-if=\"selectedNode\" :style=\"nodeInfoStyle\">\n        <h4>{{ selectedNode.name }}</h4>\n        <p>{{ selectedNode.description }}</p>\n        <div class=\"node-stats\">\n          <span>GPU节点: {{ selectedNode.gpuCount }}</span>\n          <span>算力: {{ selectedNode.computePower }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as THREE from 'three'\n\nexport default {\n  name: 'Globe3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      globe: null,\n      nodes: [],\n      selectedNode: null,\n      mousePosition: { x: 0, y: 0 },\n      animationId: null,\n      // 全球算力节点数据\n      computeNodes: [\n        {\n          name: '北京节点',\n          lat: 39.9042,\n          lng: 116.4074,\n          description: '华北地区主要算力中心',\n          gpuCount: '2000+',\n          computePower: '500 PFLOPS'\n        },\n        {\n          name: '上海节点',\n          lat: 31.2304,\n          lng: 121.4737,\n          description: '华东地区核心算力枢纽',\n          gpuCount: '1800+',\n          computePower: '450 PFLOPS'\n        },\n        {\n          name: '深圳节点',\n          lat: 22.3193,\n          lng: 114.1694,\n          description: '华南地区智算中心',\n          gpuCount: '1500+',\n          computePower: '380 PFLOPS'\n        },\n        {\n          name: '成都节点',\n          lat: 30.5728,\n          lng: 104.0668,\n          description: '西南地区算力基地',\n          gpuCount: '1200+',\n          computePower: '300 PFLOPS'\n        },\n        {\n          name: '杭州节点',\n          lat: 30.2741,\n          lng: 120.1551,\n          description: '长三角算力集群',\n          gpuCount: '1000+',\n          computePower: '250 PFLOPS'\n        },\n        {\n          name: '新加坡节点',\n          lat: 1.3521,\n          lng: 103.8198,\n          description: '东南亚算力中心',\n          gpuCount: '800+',\n          computePower: '200 PFLOPS'\n        }\n      ]\n    }\n  },\n  computed: {\n    nodeInfoStyle() {\n      return {\n        left: this.mousePosition.x + 20 + 'px',\n        top: this.mousePosition.y - 50 + 'px'\n      }\n    }\n  },\n  mounted() {\n    this.initThree()\n    this.createGlobe()\n    this.createNodes()\n    this.animate()\n    this.addEventListeners()\n  },\n  beforeDestroy() {\n    this.cleanup()\n  },\n  methods: {\n    initThree() {\n      // 创建场景\n      this.scene = new THREE.Scene()\n      \n      // 创建相机\n      const container = this.$refs.canvasContainer\n      const width = container.clientWidth\n      const height = container.clientHeight\n      \n      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)\n      this.camera.position.z = 3\n      \n      // 创建渲染器\n      this.renderer = new THREE.WebGLRenderer({ \n        antialias: true, \n        alpha: true \n      })\n      this.renderer.setSize(width, height)\n      this.renderer.setClearColor(0x000000, 0)\n      container.appendChild(this.renderer.domElement)\n      \n      // 添加环境光\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)\n      this.scene.add(ambientLight)\n      \n      // 添加方向光\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)\n      directionalLight.position.set(1, 1, 1)\n      this.scene.add(directionalLight)\n    },\n    \n    createGlobe() {\n      // 创建地球几何体\n      const geometry = new THREE.SphereGeometry(1, 64, 64)\n\n      // 创建地球材质 - 使用更真实的地球效果\n      const material = new THREE.MeshPhongMaterial({\n        color: 0x2E86AB,\n        transparent: true,\n        opacity: 0.9,\n        shininess: 100,\n        wireframe: false\n      })\n\n      this.globe = new THREE.Mesh(geometry, material)\n      this.scene.add(this.globe)\n\n      // 添加地球轮廓线 - 经纬线效果\n      const wireframeGeometry = new THREE.SphereGeometry(1.005, 32, 16)\n      const wireframeMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        wireframe: true,\n        transparent: true,\n        opacity: 0.4\n      })\n      const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial)\n      this.scene.add(wireframe)\n\n      // 添加大气层效果\n      const atmosphereGeometry = new THREE.SphereGeometry(1.1, 32, 32)\n      const atmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4A90FF,\n        transparent: true,\n        opacity: 0.1,\n        side: THREE.BackSide\n      })\n      const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial)\n      this.scene.add(atmosphere)\n    },\n    \n    createNodes() {\n      this.computeNodes.forEach(nodeData => {\n        const node = this.createNode(nodeData)\n        this.nodes.push(node)\n        this.scene.add(node.mesh)\n      })\n    },\n    \n    createNode(nodeData) {\n      // 将经纬度转换为3D坐标\n      const phi = (90 - nodeData.lat) * (Math.PI / 180)\n      const theta = (nodeData.lng + 180) * (Math.PI / 180)\n      \n      const x = -(1.05 * Math.sin(phi) * Math.cos(theta))\n      const y = 1.05 * Math.cos(phi)\n      const z = 1.05 * Math.sin(phi) * Math.sin(theta)\n      \n      // 创建节点几何体\n      const geometry = new THREE.SphereGeometry(0.02, 16, 16)\n      const material = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true\n      })\n      \n      const mesh = new THREE.Mesh(geometry, material)\n      mesh.position.set(x, y, z)\n      \n      // 创建光环效果\n      const ringGeometry = new THREE.RingGeometry(0.03, 0.05, 16)\n      const ringMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        transparent: true,\n        opacity: 0.6,\n        side: THREE.DoubleSide\n      })\n      const ring = new THREE.Mesh(ringGeometry, ringMaterial)\n      ring.position.copy(mesh.position)\n      ring.lookAt(new THREE.Vector3(0, 0, 0))\n      this.scene.add(ring)\n      \n      return {\n        mesh,\n        ring,\n        data: nodeData,\n        position: { x, y, z }\n      }\n    },\n    \n    animate() {\n      this.animationId = requestAnimationFrame(this.animate)\n      \n      // 旋转地球\n      if (this.globe) {\n        this.globe.rotation.y += 0.005\n      }\n      \n      // 节点脉冲动画\n      this.nodes.forEach((node, index) => {\n        const time = Date.now() * 0.001\n        const scale = 1 + Math.sin(time * 2 + index) * 0.3\n        node.mesh.scale.setScalar(scale)\n        \n        // 光环旋转\n        if (node.ring) {\n          node.ring.rotation.z += 0.02\n        }\n      })\n      \n      this.renderer.render(this.scene, this.camera)\n    },\n    \n    addEventListeners() {\n      window.addEventListener('resize', this.onWindowResize)\n      this.renderer.domElement.addEventListener('mousemove', this.onMouseMove)\n      this.renderer.domElement.addEventListener('click', this.onMouseClick)\n    },\n    \n    onWindowResize() {\n      const container = this.$refs.canvasContainer\n      const width = container.clientWidth\n      const height = container.clientHeight\n      \n      this.camera.aspect = width / height\n      this.camera.updateProjectionMatrix()\n      this.renderer.setSize(width, height)\n    },\n    \n    onMouseMove(event) {\n      this.mousePosition.x = event.clientX\n      this.mousePosition.y = event.clientY\n      \n      // 射线检测\n      const mouse = new THREE.Vector2()\n      const rect = this.renderer.domElement.getBoundingClientRect()\n      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1\n      \n      const raycaster = new THREE.Raycaster()\n      raycaster.setFromCamera(mouse, this.camera)\n      \n      const intersects = raycaster.intersectObjects(this.nodes.map(n => n.mesh))\n      \n      if (intersects.length > 0) {\n        const intersectedNode = this.nodes.find(n => n.mesh === intersects[0].object)\n        this.selectedNode = intersectedNode ? intersectedNode.data : null\n        this.renderer.domElement.style.cursor = 'pointer'\n      } else {\n        this.selectedNode = null\n        this.renderer.domElement.style.cursor = 'default'\n      }\n    },\n    \n    onMouseClick() {\n      if (this.selectedNode) {\n        this.$emit('node-selected', this.selectedNode)\n      }\n    },\n    \n    cleanup() {\n      if (this.animationId) {\n        cancelAnimationFrame(this.animationId)\n      }\n      \n      window.removeEventListener('resize', this.onWindowResize)\n      \n      if (this.renderer) {\n        this.renderer.dispose()\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.globe-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n\n.globe-canvas {\n  width: 100%;\n  height: 100%;\n}\n\n.globe-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n  color: white;\n}\n\n.globe-title {\n  font-size: 32px;\n  font-weight: 600;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);\n}\n\n.globe-subtitle {\n  font-size: 18px;\n  opacity: 0.9;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n}\n\n.node-info {\n  position: fixed;\n  background: rgba(20, 112, 255, 0.95);\n  color: white;\n  padding: 15px;\n  border-radius: 8px;\n  box-shadow: 0 4px 15px rgba(0,0,0,0.3);\n  pointer-events: none;\n  z-index: 1000;\n  min-width: 200px;\n  backdrop-filter: blur(10px);\n}\n\n.node-info h4 {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.node-info p {\n  margin: 0 0 10px 0;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.node-stats {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.node-stats span {\n  font-size: 12px;\n  opacity: 0.8;\n}\n\n@media (max-width: 768px) {\n  .globe-title {\n    font-size: 24px;\n  }\n  \n  .globe-subtitle {\n    font-size: 16px;\n  }\n  \n  .node-info {\n    min-width: 180px;\n    padding: 12px;\n  }\n}\n</style>\n"], "mappings": ";AAmBA,YAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,KAAA;MACAC,MAAA;MACAC,QAAA;MACAC,KAAA;MACAC,KAAA;MACAC,YAAA;MACAC,aAAA;QAAAC,CAAA;QAAAC,CAAA;MAAA;MACAC,WAAA;MACA;MACAC,YAAA,GACA;QACAZ,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAjB,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAjB,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAjB,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAjB,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACAjB,IAAA;QACAa,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA;IAEA;EACA;EACAC,QAAA;IACAC,cAAA;MACA;QACAC,IAAA,OAAAZ,aAAA,CAAAC,CAAA;QACAY,GAAA,OAAAb,aAAA,CAAAE,CAAA;MACA;IACA;EACA;EACAY,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,WAAA;IACA,KAAAC,WAAA;IACA,KAAAC,OAAA;IACA,KAAAC,iBAAA;EACA;EACAC,cAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAP,UAAA;MACA;MACA,KAAArB,KAAA,OAAAH,KAAA,CAAAgC,KAAA;;MAEA;MACA,MAAAC,SAAA,QAAAC,KAAA,CAAAC,eAAA;MACA,MAAAC,KAAA,GAAAH,SAAA,CAAAI,WAAA;MACA,MAAAC,MAAA,GAAAL,SAAA,CAAAM,YAAA;MAEA,KAAAnC,MAAA,OAAAJ,KAAA,CAAAwC,iBAAA,KAAAJ,KAAA,GAAAE,MAAA;MACA,KAAAlC,MAAA,CAAAqC,QAAA,CAAAC,CAAA;;MAEA;MACA,KAAArC,QAAA,OAAAL,KAAA,CAAA2C,aAAA;QACAC,SAAA;QACAC,KAAA;MACA;MACA,KAAAxC,QAAA,CAAAyC,OAAA,CAAAV,KAAA,EAAAE,MAAA;MACA,KAAAjC,QAAA,CAAA0C,aAAA;MACAd,SAAA,CAAAe,WAAA,MAAA3C,QAAA,CAAA4C,UAAA;;MAEA;MACA,MAAAC,YAAA,OAAAlD,KAAA,CAAAmD,YAAA;MACA,KAAAhD,KAAA,CAAAiD,GAAA,CAAAF,YAAA;;MAEA;MACA,MAAAG,gBAAA,OAAArD,KAAA,CAAAsD,gBAAA;MACAD,gBAAA,CAAAZ,QAAA,CAAAc,GAAA;MACA,KAAApD,KAAA,CAAAiD,GAAA,CAAAC,gBAAA;IACA;IAEA5B,YAAA;MACA;MACA,MAAA+B,QAAA,OAAAxD,KAAA,CAAAyD,cAAA;;MAEA;MACA,MAAAC,QAAA,OAAA1D,KAAA,CAAA2D,iBAAA;QACAC,KAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;MACA;MAEA,KAAA1D,KAAA,OAAAN,KAAA,CAAAiE,IAAA,CAAAT,QAAA,EAAAE,QAAA;MACA,KAAAvD,KAAA,CAAAiD,GAAA,MAAA9C,KAAA;;MAEA;MACA,MAAA4D,iBAAA,OAAAlE,KAAA,CAAAyD,cAAA;MACA,MAAAU,iBAAA,OAAAnE,KAAA,CAAAoE,iBAAA;QACAR,KAAA;QACAI,SAAA;QACAH,WAAA;QACAC,OAAA;MACA;MACA,MAAAE,SAAA,OAAAhE,KAAA,CAAAiE,IAAA,CAAAC,iBAAA,EAAAC,iBAAA;MACA,KAAAhE,KAAA,CAAAiD,GAAA,CAAAY,SAAA;;MAEA;MACA,MAAAK,kBAAA,OAAArE,KAAA,CAAAyD,cAAA;MACA,MAAAa,kBAAA,OAAAtE,KAAA,CAAAoE,iBAAA;QACAR,KAAA;QACAC,WAAA;QACAC,OAAA;QACAS,IAAA,EAAAvE,KAAA,CAAAwE;MACA;MACA,MAAAC,UAAA,OAAAzE,KAAA,CAAAiE,IAAA,CAAAI,kBAAA,EAAAC,kBAAA;MACA,KAAAnE,KAAA,CAAAiD,GAAA,CAAAqB,UAAA;IACA;IAEA/C,YAAA;MACA,KAAAb,YAAA,CAAA6D,OAAA,CAAAC,QAAA;QACA,MAAAC,IAAA,QAAAC,UAAA,CAAAF,QAAA;QACA,KAAApE,KAAA,CAAAuE,IAAA,CAAAF,IAAA;QACA,KAAAzE,KAAA,CAAAiD,GAAA,CAAAwB,IAAA,CAAAG,IAAA;MACA;IACA;IAEAF,WAAAF,QAAA;MACA;MACA,MAAAK,GAAA,SAAAL,QAAA,CAAA7D,GAAA,KAAAmE,IAAA,CAAAC,EAAA;MACA,MAAAC,KAAA,IAAAR,QAAA,CAAA5D,GAAA,WAAAkE,IAAA,CAAAC,EAAA;MAEA,MAAAxE,CAAA,YAAAuE,IAAA,CAAAG,GAAA,CAAAJ,GAAA,IAAAC,IAAA,CAAAI,GAAA,CAAAF,KAAA;MACA,MAAAxE,CAAA,UAAAsE,IAAA,CAAAI,GAAA,CAAAL,GAAA;MACA,MAAAtC,CAAA,UAAAuC,IAAA,CAAAG,GAAA,CAAAJ,GAAA,IAAAC,IAAA,CAAAG,GAAA,CAAAD,KAAA;;MAEA;MACA,MAAA3B,QAAA,OAAAxD,KAAA,CAAAyD,cAAA;MACA,MAAAC,QAAA,OAAA1D,KAAA,CAAAoE,iBAAA;QACAR,KAAA;QACAC,WAAA;MACA;MAEA,MAAAkB,IAAA,OAAA/E,KAAA,CAAAiE,IAAA,CAAAT,QAAA,EAAAE,QAAA;MACAqB,IAAA,CAAAtC,QAAA,CAAAc,GAAA,CAAA7C,CAAA,EAAAC,CAAA,EAAA+B,CAAA;;MAEA;MACA,MAAA4C,YAAA,OAAAtF,KAAA,CAAAuF,YAAA;MACA,MAAAC,YAAA,OAAAxF,KAAA,CAAAoE,iBAAA;QACAR,KAAA;QACAC,WAAA;QACAC,OAAA;QACAS,IAAA,EAAAvE,KAAA,CAAAyF;MACA;MACA,MAAAC,IAAA,OAAA1F,KAAA,CAAAiE,IAAA,CAAAqB,YAAA,EAAAE,YAAA;MACAE,IAAA,CAAAjD,QAAA,CAAAkD,IAAA,CAAAZ,IAAA,CAAAtC,QAAA;MACAiD,IAAA,CAAAE,MAAA,KAAA5F,KAAA,CAAA6F,OAAA;MACA,KAAA1F,KAAA,CAAAiD,GAAA,CAAAsC,IAAA;MAEA;QACAX,IAAA;QACAW,IAAA;QACAxF,IAAA,EAAAyE,QAAA;QACAlC,QAAA;UAAA/B,CAAA;UAAAC,CAAA;UAAA+B;QAAA;MACA;IACA;IAEAf,QAAA;MACA,KAAAf,WAAA,GAAAkF,qBAAA,MAAAnE,OAAA;;MAEA;MACA,SAAArB,KAAA;QACA,KAAAA,KAAA,CAAAyF,QAAA,CAAApF,CAAA;MACA;;MAEA;MACA,KAAAJ,KAAA,CAAAmE,OAAA,EAAAE,IAAA,EAAAoB,KAAA;QACA,MAAAC,IAAA,GAAAC,IAAA,CAAAC,GAAA;QACA,MAAAC,KAAA,OAAAnB,IAAA,CAAAG,GAAA,CAAAa,IAAA,OAAAD,KAAA;QACApB,IAAA,CAAAG,IAAA,CAAAqB,KAAA,CAAAC,SAAA,CAAAD,KAAA;;QAEA;QACA,IAAAxB,IAAA,CAAAc,IAAA;UACAd,IAAA,CAAAc,IAAA,CAAAK,QAAA,CAAArD,CAAA;QACA;MACA;MAEA,KAAArC,QAAA,CAAAiG,MAAA,MAAAnG,KAAA,OAAAC,MAAA;IACA;IAEAwB,kBAAA;MACA2E,MAAA,CAAAC,gBAAA,gBAAAC,cAAA;MACA,KAAApG,QAAA,CAAA4C,UAAA,CAAAuD,gBAAA,mBAAAE,WAAA;MACA,KAAArG,QAAA,CAAA4C,UAAA,CAAAuD,gBAAA,eAAAG,YAAA;IACA;IAEAF,eAAA;MACA,MAAAxE,SAAA,QAAAC,KAAA,CAAAC,eAAA;MACA,MAAAC,KAAA,GAAAH,SAAA,CAAAI,WAAA;MACA,MAAAC,MAAA,GAAAL,SAAA,CAAAM,YAAA;MAEA,KAAAnC,MAAA,CAAAwG,MAAA,GAAAxE,KAAA,GAAAE,MAAA;MACA,KAAAlC,MAAA,CAAAyG,sBAAA;MACA,KAAAxG,QAAA,CAAAyC,OAAA,CAAAV,KAAA,EAAAE,MAAA;IACA;IAEAoE,YAAAI,KAAA;MACA,KAAArG,aAAA,CAAAC,CAAA,GAAAoG,KAAA,CAAAC,OAAA;MACA,KAAAtG,aAAA,CAAAE,CAAA,GAAAmG,KAAA,CAAAE,OAAA;;MAEA;MACA,MAAAC,KAAA,OAAAjH,KAAA,CAAAkH,OAAA;MACA,MAAAC,IAAA,QAAA9G,QAAA,CAAA4C,UAAA,CAAAmE,qBAAA;MACAH,KAAA,CAAAvG,CAAA,IAAAoG,KAAA,CAAAC,OAAA,GAAAI,IAAA,CAAA9F,IAAA,IAAA8F,IAAA,CAAA/E,KAAA;MACA6E,KAAA,CAAAtG,CAAA,MAAAmG,KAAA,CAAAE,OAAA,GAAAG,IAAA,CAAA7F,GAAA,IAAA6F,IAAA,CAAA7E,MAAA;MAEA,MAAA+E,SAAA,OAAArH,KAAA,CAAAsH,SAAA;MACAD,SAAA,CAAAE,aAAA,CAAAN,KAAA,OAAA7G,MAAA;MAEA,MAAAoH,UAAA,GAAAH,SAAA,CAAAI,gBAAA,MAAAlH,KAAA,CAAAmH,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAA5C,IAAA;MAEA,IAAAyC,UAAA,CAAAI,MAAA;QACA,MAAAC,eAAA,QAAAtH,KAAA,CAAAuH,IAAA,CAAAH,CAAA,IAAAA,CAAA,CAAA5C,IAAA,KAAAyC,UAAA,IAAAO,MAAA;QACA,KAAAvH,YAAA,GAAAqH,eAAA,GAAAA,eAAA,CAAA3H,IAAA;QACA,KAAAG,QAAA,CAAA4C,UAAA,CAAA+E,KAAA,CAAAC,MAAA;MACA;QACA,KAAAzH,YAAA;QACA,KAAAH,QAAA,CAAA4C,UAAA,CAAA+E,KAAA,CAAAC,MAAA;MACA;IACA;IAEAtB,aAAA;MACA,SAAAnG,YAAA;QACA,KAAA0H,KAAA,uBAAA1H,YAAA;MACA;IACA;IAEAsB,QAAA;MACA,SAAAlB,WAAA;QACAuH,oBAAA,MAAAvH,WAAA;MACA;MAEA2F,MAAA,CAAA6B,mBAAA,gBAAA3B,cAAA;MAEA,SAAApG,QAAA;QACA,KAAAA,QAAA,CAAAgI,OAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}