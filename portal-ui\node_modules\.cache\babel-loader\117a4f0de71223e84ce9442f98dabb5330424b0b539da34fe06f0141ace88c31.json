{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as THREE from 'three';\nexport default {\n  name: 'Globe3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      globe: null,\n      nodes: [],\n      selectedNode: null,\n      mousePosition: {\n        x: 0,\n        y: 0\n      },\n      animationId: null,\n      isDragging: false,\n      previousMousePosition: {\n        x: 0,\n        y: 0\n      },\n      rotationSpeed: {\n        x: 0,\n        y: 0.005\n      },\n      isLoading: true,\n      // 全球算力节点数据\n      computeNodes: [{\n        name: '北京节点',\n        lat: 39.9042,\n        lng: 116.4074,\n        description: '华北地区主要算力中心',\n        gpuCount: '2000+',\n        computePower: '500 PFLOPS'\n      }, {\n        name: '上海节点',\n        lat: 31.2304,\n        lng: 121.4737,\n        description: '华东地区核心算力枢纽',\n        gpuCount: '1800+',\n        computePower: '450 PFLOPS'\n      }, {\n        name: '深圳节点',\n        lat: 22.3193,\n        lng: 114.1694,\n        description: '华南地区智算中心',\n        gpuCount: '1500+',\n        computePower: '380 PFLOPS'\n      }, {\n        name: '成都节点',\n        lat: 30.5728,\n        lng: 104.0668,\n        description: '西南地区算力基地',\n        gpuCount: '1200+',\n        computePower: '300 PFLOPS'\n      }, {\n        name: '杭州节点',\n        lat: 30.2741,\n        lng: 120.1551,\n        description: '长三角算力集群',\n        gpuCount: '1000+',\n        computePower: '250 PFLOPS'\n      }, {\n        name: '新加坡节点',\n        lat: 1.3521,\n        lng: 103.8198,\n        description: '东南亚算力中心',\n        gpuCount: '800+',\n        computePower: '200 PFLOPS'\n      }]\n    };\n  },\n  computed: {\n    nodeInfoStyle() {\n      return {\n        left: this.mousePosition.x + 20 + 'px',\n        top: this.mousePosition.y - 50 + 'px'\n      };\n    }\n  },\n  mounted() {\n    this.initThree();\n    this.createGlobe();\n    this.createNodes();\n    this.createStars();\n    this.animate();\n    this.addEventListeners();\n\n    // 延迟隐藏加载指示器\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1500);\n  },\n  beforeDestroy() {\n    this.cleanup();\n  },\n  methods: {\n    initThree() {\n      // 创建场景\n      this.scene = new THREE.Scene();\n\n      // 创建相机\n      const container = this.$refs.canvasContainer;\n      const width = container.clientWidth;\n      const height = container.clientHeight;\n      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);\n      this.camera.position.z = 3;\n\n      // 创建渲染器\n      this.renderer = new THREE.WebGLRenderer({\n        antialias: true,\n        alpha: true\n      });\n      this.renderer.setSize(width, height);\n      this.renderer.setClearColor(0x000000, 0);\n      container.appendChild(this.renderer.domElement);\n\n      // 添加环境光\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.6);\n      this.scene.add(ambientLight);\n\n      // 添加方向光\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n      directionalLight.position.set(1, 1, 1);\n      this.scene.add(directionalLight);\n    },\n    createGlobe() {\n      // 创建地球几何体\n      const geometry = new THREE.SphereGeometry(1, 64, 64);\n\n      // 创建地球材质 - 使用更真实的地球效果\n      const material = new THREE.MeshPhongMaterial({\n        color: 0x2E86AB,\n        transparent: true,\n        opacity: 0.9,\n        shininess: 100,\n        wireframe: false\n      });\n      this.globe = new THREE.Mesh(geometry, material);\n      this.scene.add(this.globe);\n\n      // 添加地球轮廓线 - 经纬线效果\n      const wireframeGeometry = new THREE.SphereGeometry(1.005, 32, 16);\n      const wireframeMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        wireframe: true,\n        transparent: true,\n        opacity: 0.4\n      });\n      const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial);\n      this.scene.add(wireframe);\n\n      // 添加大气层效果\n      const atmosphereGeometry = new THREE.SphereGeometry(1.1, 32, 32);\n      const atmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4A90FF,\n        transparent: true,\n        opacity: 0.1,\n        side: THREE.BackSide\n      });\n      const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);\n      this.scene.add(atmosphere);\n    },\n    createNodes() {\n      this.computeNodes.forEach(nodeData => {\n        const node = this.createNode(nodeData);\n        this.nodes.push(node);\n        this.scene.add(node.mesh);\n      });\n\n      // 创建节点间的连接线\n      this.createConnections();\n    },\n    createNode(nodeData) {\n      // 将经纬度转换为3D坐标\n      const phi = (90 - nodeData.lat) * (Math.PI / 180);\n      const theta = (nodeData.lng + 180) * (Math.PI / 180);\n      const x = -(1.05 * Math.sin(phi) * Math.cos(theta));\n      const y = 1.05 * Math.cos(phi);\n      const z = 1.05 * Math.sin(phi) * Math.sin(theta);\n\n      // 创建节点几何体\n      const geometry = new THREE.SphereGeometry(0.02, 16, 16);\n      const material = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true\n      });\n      const mesh = new THREE.Mesh(geometry, material);\n      mesh.position.set(x, y, z);\n\n      // 创建光环效果\n      const ringGeometry = new THREE.RingGeometry(0.03, 0.05, 16);\n      const ringMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        transparent: true,\n        opacity: 0.6,\n        side: THREE.DoubleSide\n      });\n      const ring = new THREE.Mesh(ringGeometry, ringMaterial);\n      ring.position.copy(mesh.position);\n      ring.lookAt(new THREE.Vector3(0, 0, 0));\n      this.scene.add(ring);\n      return {\n        mesh,\n        ring,\n        data: nodeData,\n        position: {\n          x,\n          y,\n          z\n        }\n      };\n    },\n    createConnections() {\n      // 创建主要节点间的连接线\n      const connections = [[0, 1],\n      // 北京-上海\n      [1, 2],\n      // 上海-深圳\n      [0, 3],\n      // 北京-成都\n      [1, 4],\n      // 上海-杭州\n      [2, 5] // 深圳-新加坡\n      ];\n\n      connections.forEach(([startIdx, endIdx]) => {\n        const startNode = this.nodes[startIdx];\n        const endNode = this.nodes[endIdx];\n        if (startNode && endNode) {\n          const curve = new THREE.QuadraticBezierCurve3(new THREE.Vector3(startNode.position.x, startNode.position.y, startNode.position.z), new THREE.Vector3(0, 0, 0),\n          // 控制点在地球中心上方\n          new THREE.Vector3(endNode.position.x, endNode.position.y, endNode.position.z));\n          const points = curve.getPoints(50);\n          const geometry = new THREE.BufferGeometry().setFromPoints(points);\n          const material = new THREE.LineBasicMaterial({\n            color: 0x4A90FF,\n            transparent: true,\n            opacity: 0.6\n          });\n          const line = new THREE.Line(geometry, material);\n          this.scene.add(line);\n        }\n      });\n    },\n    createStars() {\n      // 创建星空背景\n      const starsGeometry = new THREE.BufferGeometry();\n      const starsMaterial = new THREE.PointsMaterial({\n        color: 0xFFFFFF,\n        size: 2,\n        transparent: true,\n        opacity: 0.8\n      });\n      const starsVertices = [];\n      for (let i = 0; i < 1000; i++) {\n        const x = (Math.random() - 0.5) * 2000;\n        const y = (Math.random() - 0.5) * 2000;\n        const z = (Math.random() - 0.5) * 2000;\n        starsVertices.push(x, y, z);\n      }\n      starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));\n      const stars = new THREE.Points(starsGeometry, starsMaterial);\n      this.scene.add(stars);\n    },\n    animate() {\n      this.animationId = requestAnimationFrame(this.animate);\n\n      // 旋转地球\n      if (this.globe) {\n        this.globe.rotation.x += this.rotationSpeed.x;\n        this.globe.rotation.y += this.rotationSpeed.y;\n\n        // 逐渐减慢拖拽后的旋转\n        this.rotationSpeed.x *= 0.98;\n        if (!this.isDragging && Math.abs(this.rotationSpeed.y) > 0.005) {\n          this.rotationSpeed.y *= 0.98;\n        } else if (!this.isDragging) {\n          this.rotationSpeed.y = 0.005; // 恢复默认旋转速度\n        }\n      }\n\n      // 节点脉冲动画\n      this.nodes.forEach((node, index) => {\n        const time = Date.now() * 0.001;\n        const scale = 1 + Math.sin(time * 2 + index) * 0.3;\n        node.mesh.scale.setScalar(scale);\n\n        // 光环旋转\n        if (node.ring) {\n          node.ring.rotation.z += 0.02;\n        }\n      });\n      this.renderer.render(this.scene, this.camera);\n    },\n    addEventListeners() {\n      window.addEventListener('resize', this.onWindowResize);\n      this.renderer.domElement.addEventListener('mousemove', this.onMouseMove);\n      this.renderer.domElement.addEventListener('click', this.onMouseClick);\n      this.renderer.domElement.addEventListener('mousedown', this.onMouseDown);\n      this.renderer.domElement.addEventListener('mouseup', this.onMouseUp);\n      this.renderer.domElement.addEventListener('mouseleave', this.onMouseUp);\n    },\n    onWindowResize() {\n      const container = this.$refs.canvasContainer;\n      const width = container.clientWidth;\n      const height = container.clientHeight;\n      this.camera.aspect = width / height;\n      this.camera.updateProjectionMatrix();\n      this.renderer.setSize(width, height);\n    },\n    onMouseMove(event) {\n      this.mousePosition.x = event.clientX;\n      this.mousePosition.y = event.clientY;\n\n      // 处理拖拽旋转\n      if (this.isDragging) {\n        const deltaX = event.clientX - this.previousMousePosition.x;\n        const deltaY = event.clientY - this.previousMousePosition.y;\n        this.rotationSpeed.y = deltaX * 0.01;\n        this.rotationSpeed.x = -deltaY * 0.01;\n        this.previousMousePosition.x = event.clientX;\n        this.previousMousePosition.y = event.clientY;\n        return;\n      }\n\n      // 射线检测\n      const mouse = new THREE.Vector2();\n      const rect = this.renderer.domElement.getBoundingClientRect();\n      mouse.x = (event.clientX - rect.left) / rect.width * 2 - 1;\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;\n      const raycaster = new THREE.Raycaster();\n      raycaster.setFromCamera(mouse, this.camera);\n      const intersects = raycaster.intersectObjects(this.nodes.map(n => n.mesh));\n      if (intersects.length > 0) {\n        const intersectedNode = this.nodes.find(n => n.mesh === intersects[0].object);\n        this.selectedNode = intersectedNode ? intersectedNode.data : null;\n        this.renderer.domElement.style.cursor = 'pointer';\n      } else {\n        this.selectedNode = null;\n        this.renderer.domElement.style.cursor = this.isDragging ? 'grabbing' : 'grab';\n      }\n    },\n    onMouseClick() {\n      if (this.selectedNode && !this.isDragging) {\n        this.$emit('node-selected', this.selectedNode);\n      }\n    },\n    onMouseDown(event) {\n      this.isDragging = true;\n      this.previousMousePosition.x = event.clientX;\n      this.previousMousePosition.y = event.clientY;\n      this.renderer.domElement.style.cursor = 'grabbing';\n    },\n    onMouseUp() {\n      this.isDragging = false;\n      this.renderer.domElement.style.cursor = 'grab';\n    },\n    cleanup() {\n      if (this.animationId) {\n        cancelAnimationFrame(this.animationId);\n      }\n      window.removeEventListener('resize', this.onWindowResize);\n      if (this.renderer) {\n        this.renderer.dispose();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["THREE", "name", "data", "scene", "camera", "renderer", "globe", "nodes", "selectedNode", "mousePosition", "x", "y", "animationId", "isDragging", "previousMousePosition", "rotationSpeed", "isLoading", "computeNodes", "lat", "lng", "description", "gpuCount", "computePower", "computed", "nodeInfoStyle", "left", "top", "mounted", "initThree", "createGlobe", "createNodes", "createStars", "animate", "addEventListeners", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "cleanup", "methods", "Scene", "container", "$refs", "canvasContainer", "width", "clientWidth", "height", "clientHeight", "PerspectiveCamera", "position", "z", "WebGLRenderer", "antialias", "alpha", "setSize", "setClearColor", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "add", "directionalLight", "DirectionalLight", "set", "geometry", "SphereGeometry", "material", "MeshPhongMaterial", "color", "transparent", "opacity", "shininess", "wireframe", "<PERSON><PERSON>", "wireframeGeometry", "wireframeMaterial", "MeshBasicMaterial", "atmosphereGeometry", "atmosphereMaterial", "side", "BackSide", "atmosphere", "for<PERSON>ach", "nodeData", "node", "createNode", "push", "mesh", "createConnections", "phi", "Math", "PI", "theta", "sin", "cos", "ringGeometry", "RingGeometry", "ringMaterial", "DoubleSide", "ring", "copy", "lookAt", "Vector3", "connections", "startIdx", "endIdx", "startNode", "endNode", "curve", "QuadraticBezierCurve3", "points", "getPoints", "BufferGeometry", "setFromPoints", "LineBasicMaterial", "line", "Line", "starsGeometry", "starsMaterial", "PointsMaterial", "size", "starsVertices", "i", "random", "setAttribute", "Float32BufferAttribute", "stars", "Points", "requestAnimationFrame", "rotation", "abs", "index", "time", "Date", "now", "scale", "setScalar", "render", "window", "addEventListener", "onWindowResize", "onMouseMove", "onMouseClick", "onMouseDown", "onMouseUp", "aspect", "updateProjectionMatrix", "event", "clientX", "clientY", "deltaX", "deltaY", "mouse", "Vector2", "rect", "getBoundingClientRect", "raycaster", "Raycaster", "setFromCamera", "intersects", "intersectObjects", "map", "n", "length", "intersectedNode", "find", "object", "style", "cursor", "$emit", "cancelAnimationFrame", "removeEventListener", "dispose"], "sources": ["src/components/common/Globe3D.vue"], "sourcesContent": ["<template>\n  <div class=\"globe-container\" ref=\"globeContainer\">\n    <div class=\"globe-canvas\" ref=\"canvasContainer\"></div>\n    <div class=\"loading-indicator\" v-if=\"isLoading\">\n      <div class=\"loading-spinner\"></div>\n      <div class=\"loading-text\">正在加载全球算力网络...</div>\n    </div>\n    <div class=\"globe-overlay\" v-if=\"!isLoading\">\n      <div class=\"globe-title\">全球算力布局</div>\n      <div class=\"globe-subtitle\">分布式高性能计算网络</div>\n      <div class=\"interaction-hint\">拖拽旋转 | 点击节点查看详情</div>\n      <div class=\"node-info\" v-if=\"selectedNode\" :style=\"nodeInfoStyle\">\n        <h4>{{ selectedNode.name }}</h4>\n        <p>{{ selectedNode.description }}</p>\n        <div class=\"node-stats\">\n          <span>GPU节点: {{ selectedNode.gpuCount }}</span>\n          <span>算力: {{ selectedNode.computePower }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as THREE from 'three'\n\nexport default {\n  name: 'Globe3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      globe: null,\n      nodes: [],\n      selectedNode: null,\n      mousePosition: { x: 0, y: 0 },\n      animationId: null,\n      isDragging: false,\n      previousMousePosition: { x: 0, y: 0 },\n      rotationSpeed: { x: 0, y: 0.005 },\n      isLoading: true,\n      // 全球算力节点数据\n      computeNodes: [\n        {\n          name: '北京节点',\n          lat: 39.9042,\n          lng: 116.4074,\n          description: '华北地区主要算力中心',\n          gpuCount: '2000+',\n          computePower: '500 PFLOPS'\n        },\n        {\n          name: '上海节点',\n          lat: 31.2304,\n          lng: 121.4737,\n          description: '华东地区核心算力枢纽',\n          gpuCount: '1800+',\n          computePower: '450 PFLOPS'\n        },\n        {\n          name: '深圳节点',\n          lat: 22.3193,\n          lng: 114.1694,\n          description: '华南地区智算中心',\n          gpuCount: '1500+',\n          computePower: '380 PFLOPS'\n        },\n        {\n          name: '成都节点',\n          lat: 30.5728,\n          lng: 104.0668,\n          description: '西南地区算力基地',\n          gpuCount: '1200+',\n          computePower: '300 PFLOPS'\n        },\n        {\n          name: '杭州节点',\n          lat: 30.2741,\n          lng: 120.1551,\n          description: '长三角算力集群',\n          gpuCount: '1000+',\n          computePower: '250 PFLOPS'\n        },\n        {\n          name: '新加坡节点',\n          lat: 1.3521,\n          lng: 103.8198,\n          description: '东南亚算力中心',\n          gpuCount: '800+',\n          computePower: '200 PFLOPS'\n        }\n      ]\n    }\n  },\n  computed: {\n    nodeInfoStyle() {\n      return {\n        left: this.mousePosition.x + 20 + 'px',\n        top: this.mousePosition.y - 50 + 'px'\n      }\n    }\n  },\n  mounted() {\n    this.initThree()\n    this.createGlobe()\n    this.createNodes()\n    this.createStars()\n    this.animate()\n    this.addEventListeners()\n\n    // 延迟隐藏加载指示器\n    setTimeout(() => {\n      this.isLoading = false\n    }, 1500)\n  },\n  beforeDestroy() {\n    this.cleanup()\n  },\n  methods: {\n    initThree() {\n      // 创建场景\n      this.scene = new THREE.Scene()\n      \n      // 创建相机\n      const container = this.$refs.canvasContainer\n      const width = container.clientWidth\n      const height = container.clientHeight\n      \n      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)\n      this.camera.position.z = 3\n      \n      // 创建渲染器\n      this.renderer = new THREE.WebGLRenderer({ \n        antialias: true, \n        alpha: true \n      })\n      this.renderer.setSize(width, height)\n      this.renderer.setClearColor(0x000000, 0)\n      container.appendChild(this.renderer.domElement)\n      \n      // 添加环境光\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)\n      this.scene.add(ambientLight)\n      \n      // 添加方向光\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)\n      directionalLight.position.set(1, 1, 1)\n      this.scene.add(directionalLight)\n    },\n    \n    createGlobe() {\n      // 创建地球几何体\n      const geometry = new THREE.SphereGeometry(1, 64, 64)\n\n      // 创建地球材质 - 使用更真实的地球效果\n      const material = new THREE.MeshPhongMaterial({\n        color: 0x2E86AB,\n        transparent: true,\n        opacity: 0.9,\n        shininess: 100,\n        wireframe: false\n      })\n\n      this.globe = new THREE.Mesh(geometry, material)\n      this.scene.add(this.globe)\n\n      // 添加地球轮廓线 - 经纬线效果\n      const wireframeGeometry = new THREE.SphereGeometry(1.005, 32, 16)\n      const wireframeMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        wireframe: true,\n        transparent: true,\n        opacity: 0.4\n      })\n      const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial)\n      this.scene.add(wireframe)\n\n      // 添加大气层效果\n      const atmosphereGeometry = new THREE.SphereGeometry(1.1, 32, 32)\n      const atmosphereMaterial = new THREE.MeshBasicMaterial({\n        color: 0x4A90FF,\n        transparent: true,\n        opacity: 0.1,\n        side: THREE.BackSide\n      })\n      const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial)\n      this.scene.add(atmosphere)\n    },\n    \n    createNodes() {\n      this.computeNodes.forEach(nodeData => {\n        const node = this.createNode(nodeData)\n        this.nodes.push(node)\n        this.scene.add(node.mesh)\n      })\n\n      // 创建节点间的连接线\n      this.createConnections()\n    },\n    \n    createNode(nodeData) {\n      // 将经纬度转换为3D坐标\n      const phi = (90 - nodeData.lat) * (Math.PI / 180)\n      const theta = (nodeData.lng + 180) * (Math.PI / 180)\n      \n      const x = -(1.05 * Math.sin(phi) * Math.cos(theta))\n      const y = 1.05 * Math.cos(phi)\n      const z = 1.05 * Math.sin(phi) * Math.sin(theta)\n      \n      // 创建节点几何体\n      const geometry = new THREE.SphereGeometry(0.02, 16, 16)\n      const material = new THREE.MeshBasicMaterial({\n        color: 0xFFFFFF,\n        transparent: true\n      })\n      \n      const mesh = new THREE.Mesh(geometry, material)\n      mesh.position.set(x, y, z)\n      \n      // 创建光环效果\n      const ringGeometry = new THREE.RingGeometry(0.03, 0.05, 16)\n      const ringMaterial = new THREE.MeshBasicMaterial({\n        color: 0x1470FF,\n        transparent: true,\n        opacity: 0.6,\n        side: THREE.DoubleSide\n      })\n      const ring = new THREE.Mesh(ringGeometry, ringMaterial)\n      ring.position.copy(mesh.position)\n      ring.lookAt(new THREE.Vector3(0, 0, 0))\n      this.scene.add(ring)\n      \n      return {\n        mesh,\n        ring,\n        data: nodeData,\n        position: { x, y, z }\n      }\n    },\n\n    createConnections() {\n      // 创建主要节点间的连接线\n      const connections = [\n        [0, 1], // 北京-上海\n        [1, 2], // 上海-深圳\n        [0, 3], // 北京-成都\n        [1, 4], // 上海-杭州\n        [2, 5]  // 深圳-新加坡\n      ]\n\n      connections.forEach(([startIdx, endIdx]) => {\n        const startNode = this.nodes[startIdx]\n        const endNode = this.nodes[endIdx]\n\n        if (startNode && endNode) {\n          const curve = new THREE.QuadraticBezierCurve3(\n            new THREE.Vector3(startNode.position.x, startNode.position.y, startNode.position.z),\n            new THREE.Vector3(0, 0, 0), // 控制点在地球中心上方\n            new THREE.Vector3(endNode.position.x, endNode.position.y, endNode.position.z)\n          )\n\n          const points = curve.getPoints(50)\n          const geometry = new THREE.BufferGeometry().setFromPoints(points)\n          const material = new THREE.LineBasicMaterial({\n            color: 0x4A90FF,\n            transparent: true,\n            opacity: 0.6\n          })\n\n          const line = new THREE.Line(geometry, material)\n          this.scene.add(line)\n        }\n      })\n    },\n\n    createStars() {\n      // 创建星空背景\n      const starsGeometry = new THREE.BufferGeometry()\n      const starsMaterial = new THREE.PointsMaterial({\n        color: 0xFFFFFF,\n        size: 2,\n        transparent: true,\n        opacity: 0.8\n      })\n\n      const starsVertices = []\n      for (let i = 0; i < 1000; i++) {\n        const x = (Math.random() - 0.5) * 2000\n        const y = (Math.random() - 0.5) * 2000\n        const z = (Math.random() - 0.5) * 2000\n        starsVertices.push(x, y, z)\n      }\n\n      starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3))\n      const stars = new THREE.Points(starsGeometry, starsMaterial)\n      this.scene.add(stars)\n    },\n    \n    animate() {\n      this.animationId = requestAnimationFrame(this.animate)\n      \n      // 旋转地球\n      if (this.globe) {\n        this.globe.rotation.x += this.rotationSpeed.x\n        this.globe.rotation.y += this.rotationSpeed.y\n\n        // 逐渐减慢拖拽后的旋转\n        this.rotationSpeed.x *= 0.98\n        if (!this.isDragging && Math.abs(this.rotationSpeed.y) > 0.005) {\n          this.rotationSpeed.y *= 0.98\n        } else if (!this.isDragging) {\n          this.rotationSpeed.y = 0.005 // 恢复默认旋转速度\n        }\n      }\n      \n      // 节点脉冲动画\n      this.nodes.forEach((node, index) => {\n        const time = Date.now() * 0.001\n        const scale = 1 + Math.sin(time * 2 + index) * 0.3\n        node.mesh.scale.setScalar(scale)\n        \n        // 光环旋转\n        if (node.ring) {\n          node.ring.rotation.z += 0.02\n        }\n      })\n      \n      this.renderer.render(this.scene, this.camera)\n    },\n    \n    addEventListeners() {\n      window.addEventListener('resize', this.onWindowResize)\n      this.renderer.domElement.addEventListener('mousemove', this.onMouseMove)\n      this.renderer.domElement.addEventListener('click', this.onMouseClick)\n      this.renderer.domElement.addEventListener('mousedown', this.onMouseDown)\n      this.renderer.domElement.addEventListener('mouseup', this.onMouseUp)\n      this.renderer.domElement.addEventListener('mouseleave', this.onMouseUp)\n    },\n    \n    onWindowResize() {\n      const container = this.$refs.canvasContainer\n      const width = container.clientWidth\n      const height = container.clientHeight\n      \n      this.camera.aspect = width / height\n      this.camera.updateProjectionMatrix()\n      this.renderer.setSize(width, height)\n    },\n    \n    onMouseMove(event) {\n      this.mousePosition.x = event.clientX\n      this.mousePosition.y = event.clientY\n\n      // 处理拖拽旋转\n      if (this.isDragging) {\n        const deltaX = event.clientX - this.previousMousePosition.x\n        const deltaY = event.clientY - this.previousMousePosition.y\n\n        this.rotationSpeed.y = deltaX * 0.01\n        this.rotationSpeed.x = -deltaY * 0.01\n\n        this.previousMousePosition.x = event.clientX\n        this.previousMousePosition.y = event.clientY\n        return\n      }\n\n      // 射线检测\n      const mouse = new THREE.Vector2()\n      const rect = this.renderer.domElement.getBoundingClientRect()\n      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1\n\n      const raycaster = new THREE.Raycaster()\n      raycaster.setFromCamera(mouse, this.camera)\n\n      const intersects = raycaster.intersectObjects(this.nodes.map(n => n.mesh))\n\n      if (intersects.length > 0) {\n        const intersectedNode = this.nodes.find(n => n.mesh === intersects[0].object)\n        this.selectedNode = intersectedNode ? intersectedNode.data : null\n        this.renderer.domElement.style.cursor = 'pointer'\n      } else {\n        this.selectedNode = null\n        this.renderer.domElement.style.cursor = this.isDragging ? 'grabbing' : 'grab'\n      }\n    },\n    \n    onMouseClick() {\n      if (this.selectedNode && !this.isDragging) {\n        this.$emit('node-selected', this.selectedNode)\n      }\n    },\n\n    onMouseDown(event) {\n      this.isDragging = true\n      this.previousMousePosition.x = event.clientX\n      this.previousMousePosition.y = event.clientY\n      this.renderer.domElement.style.cursor = 'grabbing'\n    },\n\n    onMouseUp() {\n      this.isDragging = false\n      this.renderer.domElement.style.cursor = 'grab'\n    },\n    \n    cleanup() {\n      if (this.animationId) {\n        cancelAnimationFrame(this.animationId)\n      }\n      \n      window.removeEventListener('resize', this.onWindowResize)\n      \n      if (this.renderer) {\n        this.renderer.dispose()\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.globe-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n\n.globe-canvas {\n  width: 100%;\n  height: 100%;\n  cursor: grab;\n}\n\n.globe-canvas:active {\n  cursor: grabbing;\n}\n\n.globe-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n  color: white;\n}\n\n.globe-title {\n  font-size: 32px;\n  font-weight: 600;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);\n}\n\n.globe-subtitle {\n  font-size: 18px;\n  opacity: 0.9;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n  margin-bottom: 20px;\n}\n\n.interaction-hint {\n  font-size: 14px;\n  opacity: 0.7;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 0.7; }\n  50% { opacity: 1; }\n}\n\n.loading-indicator {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  background: rgba(20, 112, 255, 0.1);\n  backdrop-filter: blur(5px);\n  color: white;\n  z-index: 10;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 3px solid rgba(255, 255, 255, 0.3);\n  border-top: 3px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 16px;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n}\n\n.node-info {\n  position: fixed;\n  background: rgba(20, 112, 255, 0.95);\n  color: white;\n  padding: 15px;\n  border-radius: 8px;\n  box-shadow: 0 4px 15px rgba(0,0,0,0.3);\n  pointer-events: none;\n  z-index: 1000;\n  min-width: 200px;\n  backdrop-filter: blur(10px);\n}\n\n.node-info h4 {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.node-info p {\n  margin: 0 0 10px 0;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.node-stats {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.node-stats span {\n  font-size: 12px;\n  opacity: 0.8;\n}\n\n@media (max-width: 768px) {\n  .globe-title {\n    font-size: 24px;\n  }\n  \n  .globe-subtitle {\n    font-size: 16px;\n  }\n  \n  .node-info {\n    min-width: 180px;\n    padding: 12px;\n  }\n}\n</style>\n"], "mappings": ";AAwBA,YAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,KAAA;MACAC,MAAA;MACAC,QAAA;MACAC,KAAA;MACAC,KAAA;MACAC,YAAA;MACAC,aAAA;QAAAC,CAAA;QAAAC,CAAA;MAAA;MACAC,WAAA;MACAC,UAAA;MACAC,qBAAA;QAAAJ,CAAA;QAAAC,CAAA;MAAA;MACAI,aAAA;QAAAL,CAAA;QAAAC,CAAA;MAAA;MACAK,SAAA;MACA;MACAC,YAAA,GACA;QACAhB,IAAA;QACAiB,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACArB,IAAA;QACAiB,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACArB,IAAA;QACAiB,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACArB,IAAA;QACAiB,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACArB,IAAA;QACAiB,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA,GACA;QACArB,IAAA;QACAiB,GAAA;QACAC,GAAA;QACAC,WAAA;QACAC,QAAA;QACAC,YAAA;MACA;IAEA;EACA;EACAC,QAAA;IACAC,cAAA;MACA;QACAC,IAAA,OAAAhB,aAAA,CAAAC,CAAA;QACAgB,GAAA,OAAAjB,aAAA,CAAAE,CAAA;MACA;IACA;EACA;EACAgB,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,WAAA;IACA,KAAAC,WAAA;IACA,KAAAC,WAAA;IACA,KAAAC,OAAA;IACA,KAAAC,iBAAA;;IAEA;IACAC,UAAA;MACA,KAAAlB,SAAA;IACA;EACA;EACAmB,cAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAT,UAAA;MACA;MACA,KAAAzB,KAAA,OAAAH,KAAA,CAAAsC,KAAA;;MAEA;MACA,MAAAC,SAAA,QAAAC,KAAA,CAAAC,eAAA;MACA,MAAAC,KAAA,GAAAH,SAAA,CAAAI,WAAA;MACA,MAAAC,MAAA,GAAAL,SAAA,CAAAM,YAAA;MAEA,KAAAzC,MAAA,OAAAJ,KAAA,CAAA8C,iBAAA,KAAAJ,KAAA,GAAAE,MAAA;MACA,KAAAxC,MAAA,CAAA2C,QAAA,CAAAC,CAAA;;MAEA;MACA,KAAA3C,QAAA,OAAAL,KAAA,CAAAiD,aAAA;QACAC,SAAA;QACAC,KAAA;MACA;MACA,KAAA9C,QAAA,CAAA+C,OAAA,CAAAV,KAAA,EAAAE,MAAA;MACA,KAAAvC,QAAA,CAAAgD,aAAA;MACAd,SAAA,CAAAe,WAAA,MAAAjD,QAAA,CAAAkD,UAAA;;MAEA;MACA,MAAAC,YAAA,OAAAxD,KAAA,CAAAyD,YAAA;MACA,KAAAtD,KAAA,CAAAuD,GAAA,CAAAF,YAAA;;MAEA;MACA,MAAAG,gBAAA,OAAA3D,KAAA,CAAA4D,gBAAA;MACAD,gBAAA,CAAAZ,QAAA,CAAAc,GAAA;MACA,KAAA1D,KAAA,CAAAuD,GAAA,CAAAC,gBAAA;IACA;IAEA9B,YAAA;MACA;MACA,MAAAiC,QAAA,OAAA9D,KAAA,CAAA+D,cAAA;;MAEA;MACA,MAAAC,QAAA,OAAAhE,KAAA,CAAAiE,iBAAA;QACAC,KAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;MACA;MAEA,KAAAhE,KAAA,OAAAN,KAAA,CAAAuE,IAAA,CAAAT,QAAA,EAAAE,QAAA;MACA,KAAA7D,KAAA,CAAAuD,GAAA,MAAApD,KAAA;;MAEA;MACA,MAAAkE,iBAAA,OAAAxE,KAAA,CAAA+D,cAAA;MACA,MAAAU,iBAAA,OAAAzE,KAAA,CAAA0E,iBAAA;QACAR,KAAA;QACAI,SAAA;QACAH,WAAA;QACAC,OAAA;MACA;MACA,MAAAE,SAAA,OAAAtE,KAAA,CAAAuE,IAAA,CAAAC,iBAAA,EAAAC,iBAAA;MACA,KAAAtE,KAAA,CAAAuD,GAAA,CAAAY,SAAA;;MAEA;MACA,MAAAK,kBAAA,OAAA3E,KAAA,CAAA+D,cAAA;MACA,MAAAa,kBAAA,OAAA5E,KAAA,CAAA0E,iBAAA;QACAR,KAAA;QACAC,WAAA;QACAC,OAAA;QACAS,IAAA,EAAA7E,KAAA,CAAA8E;MACA;MACA,MAAAC,UAAA,OAAA/E,KAAA,CAAAuE,IAAA,CAAAI,kBAAA,EAAAC,kBAAA;MACA,KAAAzE,KAAA,CAAAuD,GAAA,CAAAqB,UAAA;IACA;IAEAjD,YAAA;MACA,KAAAb,YAAA,CAAA+D,OAAA,CAAAC,QAAA;QACA,MAAAC,IAAA,QAAAC,UAAA,CAAAF,QAAA;QACA,KAAA1E,KAAA,CAAA6E,IAAA,CAAAF,IAAA;QACA,KAAA/E,KAAA,CAAAuD,GAAA,CAAAwB,IAAA,CAAAG,IAAA;MACA;;MAEA;MACA,KAAAC,iBAAA;IACA;IAEAH,WAAAF,QAAA;MACA;MACA,MAAAM,GAAA,SAAAN,QAAA,CAAA/D,GAAA,KAAAsE,IAAA,CAAAC,EAAA;MACA,MAAAC,KAAA,IAAAT,QAAA,CAAA9D,GAAA,WAAAqE,IAAA,CAAAC,EAAA;MAEA,MAAA/E,CAAA,YAAA8E,IAAA,CAAAG,GAAA,CAAAJ,GAAA,IAAAC,IAAA,CAAAI,GAAA,CAAAF,KAAA;MACA,MAAA/E,CAAA,UAAA6E,IAAA,CAAAI,GAAA,CAAAL,GAAA;MACA,MAAAvC,CAAA,UAAAwC,IAAA,CAAAG,GAAA,CAAAJ,GAAA,IAAAC,IAAA,CAAAG,GAAA,CAAAD,KAAA;;MAEA;MACA,MAAA5B,QAAA,OAAA9D,KAAA,CAAA+D,cAAA;MACA,MAAAC,QAAA,OAAAhE,KAAA,CAAA0E,iBAAA;QACAR,KAAA;QACAC,WAAA;MACA;MAEA,MAAAkB,IAAA,OAAArF,KAAA,CAAAuE,IAAA,CAAAT,QAAA,EAAAE,QAAA;MACAqB,IAAA,CAAAtC,QAAA,CAAAc,GAAA,CAAAnD,CAAA,EAAAC,CAAA,EAAAqC,CAAA;;MAEA;MACA,MAAA6C,YAAA,OAAA7F,KAAA,CAAA8F,YAAA;MACA,MAAAC,YAAA,OAAA/F,KAAA,CAAA0E,iBAAA;QACAR,KAAA;QACAC,WAAA;QACAC,OAAA;QACAS,IAAA,EAAA7E,KAAA,CAAAgG;MACA;MACA,MAAAC,IAAA,OAAAjG,KAAA,CAAAuE,IAAA,CAAAsB,YAAA,EAAAE,YAAA;MACAE,IAAA,CAAAlD,QAAA,CAAAmD,IAAA,CAAAb,IAAA,CAAAtC,QAAA;MACAkD,IAAA,CAAAE,MAAA,KAAAnG,KAAA,CAAAoG,OAAA;MACA,KAAAjG,KAAA,CAAAuD,GAAA,CAAAuC,IAAA;MAEA;QACAZ,IAAA;QACAY,IAAA;QACA/F,IAAA,EAAA+E,QAAA;QACAlC,QAAA;UAAArC,CAAA;UAAAC,CAAA;UAAAqC;QAAA;MACA;IACA;IAEAsC,kBAAA;MACA;MACA,MAAAe,WAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;;MAEAA,WAAA,CAAArB,OAAA,GAAAsB,QAAA,EAAAC,MAAA;QACA,MAAAC,SAAA,QAAAjG,KAAA,CAAA+F,QAAA;QACA,MAAAG,OAAA,QAAAlG,KAAA,CAAAgG,MAAA;QAEA,IAAAC,SAAA,IAAAC,OAAA;UACA,MAAAC,KAAA,OAAA1G,KAAA,CAAA2G,qBAAA,CACA,IAAA3G,KAAA,CAAAoG,OAAA,CAAAI,SAAA,CAAAzD,QAAA,CAAArC,CAAA,EAAA8F,SAAA,CAAAzD,QAAA,CAAApC,CAAA,EAAA6F,SAAA,CAAAzD,QAAA,CAAAC,CAAA,GACA,IAAAhD,KAAA,CAAAoG,OAAA;UAAA;UACA,IAAApG,KAAA,CAAAoG,OAAA,CAAAK,OAAA,CAAA1D,QAAA,CAAArC,CAAA,EAAA+F,OAAA,CAAA1D,QAAA,CAAApC,CAAA,EAAA8F,OAAA,CAAA1D,QAAA,CAAAC,CAAA,EACA;UAEA,MAAA4D,MAAA,GAAAF,KAAA,CAAAG,SAAA;UACA,MAAA/C,QAAA,OAAA9D,KAAA,CAAA8G,cAAA,GAAAC,aAAA,CAAAH,MAAA;UACA,MAAA5C,QAAA,OAAAhE,KAAA,CAAAgH,iBAAA;YACA9C,KAAA;YACAC,WAAA;YACAC,OAAA;UACA;UAEA,MAAA6C,IAAA,OAAAjH,KAAA,CAAAkH,IAAA,CAAApD,QAAA,EAAAE,QAAA;UACA,KAAA7D,KAAA,CAAAuD,GAAA,CAAAuD,IAAA;QACA;MACA;IACA;IAEAlF,YAAA;MACA;MACA,MAAAoF,aAAA,OAAAnH,KAAA,CAAA8G,cAAA;MACA,MAAAM,aAAA,OAAApH,KAAA,CAAAqH,cAAA;QACAnD,KAAA;QACAoD,IAAA;QACAnD,WAAA;QACAC,OAAA;MACA;MAEA,MAAAmD,aAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,SAAAA,CAAA;QACA,MAAA9G,CAAA,IAAA8E,IAAA,CAAAiC,MAAA;QACA,MAAA9G,CAAA,IAAA6E,IAAA,CAAAiC,MAAA;QACA,MAAAzE,CAAA,IAAAwC,IAAA,CAAAiC,MAAA;QACAF,aAAA,CAAAnC,IAAA,CAAA1E,CAAA,EAAAC,CAAA,EAAAqC,CAAA;MACA;MAEAmE,aAAA,CAAAO,YAAA,iBAAA1H,KAAA,CAAA2H,sBAAA,CAAAJ,aAAA;MACA,MAAAK,KAAA,OAAA5H,KAAA,CAAA6H,MAAA,CAAAV,aAAA,EAAAC,aAAA;MACA,KAAAjH,KAAA,CAAAuD,GAAA,CAAAkE,KAAA;IACA;IAEA5F,QAAA;MACA,KAAApB,WAAA,GAAAkH,qBAAA,MAAA9F,OAAA;;MAEA;MACA,SAAA1B,KAAA;QACA,KAAAA,KAAA,CAAAyH,QAAA,CAAArH,CAAA,SAAAK,aAAA,CAAAL,CAAA;QACA,KAAAJ,KAAA,CAAAyH,QAAA,CAAApH,CAAA,SAAAI,aAAA,CAAAJ,CAAA;;QAEA;QACA,KAAAI,aAAA,CAAAL,CAAA;QACA,UAAAG,UAAA,IAAA2E,IAAA,CAAAwC,GAAA,MAAAjH,aAAA,CAAAJ,CAAA;UACA,KAAAI,aAAA,CAAAJ,CAAA;QACA,iBAAAE,UAAA;UACA,KAAAE,aAAA,CAAAJ,CAAA;QACA;MACA;;MAEA;MACA,KAAAJ,KAAA,CAAAyE,OAAA,EAAAE,IAAA,EAAA+C,KAAA;QACA,MAAAC,IAAA,GAAAC,IAAA,CAAAC,GAAA;QACA,MAAAC,KAAA,OAAA7C,IAAA,CAAAG,GAAA,CAAAuC,IAAA,OAAAD,KAAA;QACA/C,IAAA,CAAAG,IAAA,CAAAgD,KAAA,CAAAC,SAAA,CAAAD,KAAA;;QAEA;QACA,IAAAnD,IAAA,CAAAe,IAAA;UACAf,IAAA,CAAAe,IAAA,CAAA8B,QAAA,CAAA/E,CAAA;QACA;MACA;MAEA,KAAA3C,QAAA,CAAAkI,MAAA,MAAApI,KAAA,OAAAC,MAAA;IACA;IAEA6B,kBAAA;MACAuG,MAAA,CAAAC,gBAAA,gBAAAC,cAAA;MACA,KAAArI,QAAA,CAAAkD,UAAA,CAAAkF,gBAAA,mBAAAE,WAAA;MACA,KAAAtI,QAAA,CAAAkD,UAAA,CAAAkF,gBAAA,eAAAG,YAAA;MACA,KAAAvI,QAAA,CAAAkD,UAAA,CAAAkF,gBAAA,mBAAAI,WAAA;MACA,KAAAxI,QAAA,CAAAkD,UAAA,CAAAkF,gBAAA,iBAAAK,SAAA;MACA,KAAAzI,QAAA,CAAAkD,UAAA,CAAAkF,gBAAA,oBAAAK,SAAA;IACA;IAEAJ,eAAA;MACA,MAAAnG,SAAA,QAAAC,KAAA,CAAAC,eAAA;MACA,MAAAC,KAAA,GAAAH,SAAA,CAAAI,WAAA;MACA,MAAAC,MAAA,GAAAL,SAAA,CAAAM,YAAA;MAEA,KAAAzC,MAAA,CAAA2I,MAAA,GAAArG,KAAA,GAAAE,MAAA;MACA,KAAAxC,MAAA,CAAA4I,sBAAA;MACA,KAAA3I,QAAA,CAAA+C,OAAA,CAAAV,KAAA,EAAAE,MAAA;IACA;IAEA+F,YAAAM,KAAA;MACA,KAAAxI,aAAA,CAAAC,CAAA,GAAAuI,KAAA,CAAAC,OAAA;MACA,KAAAzI,aAAA,CAAAE,CAAA,GAAAsI,KAAA,CAAAE,OAAA;;MAEA;MACA,SAAAtI,UAAA;QACA,MAAAuI,MAAA,GAAAH,KAAA,CAAAC,OAAA,QAAApI,qBAAA,CAAAJ,CAAA;QACA,MAAA2I,MAAA,GAAAJ,KAAA,CAAAE,OAAA,QAAArI,qBAAA,CAAAH,CAAA;QAEA,KAAAI,aAAA,CAAAJ,CAAA,GAAAyI,MAAA;QACA,KAAArI,aAAA,CAAAL,CAAA,IAAA2I,MAAA;QAEA,KAAAvI,qBAAA,CAAAJ,CAAA,GAAAuI,KAAA,CAAAC,OAAA;QACA,KAAApI,qBAAA,CAAAH,CAAA,GAAAsI,KAAA,CAAAE,OAAA;QACA;MACA;;MAEA;MACA,MAAAG,KAAA,OAAAtJ,KAAA,CAAAuJ,OAAA;MACA,MAAAC,IAAA,QAAAnJ,QAAA,CAAAkD,UAAA,CAAAkG,qBAAA;MACAH,KAAA,CAAA5I,CAAA,IAAAuI,KAAA,CAAAC,OAAA,GAAAM,IAAA,CAAA/H,IAAA,IAAA+H,IAAA,CAAA9G,KAAA;MACA4G,KAAA,CAAA3I,CAAA,MAAAsI,KAAA,CAAAE,OAAA,GAAAK,IAAA,CAAA9H,GAAA,IAAA8H,IAAA,CAAA5G,MAAA;MAEA,MAAA8G,SAAA,OAAA1J,KAAA,CAAA2J,SAAA;MACAD,SAAA,CAAAE,aAAA,CAAAN,KAAA,OAAAlJ,MAAA;MAEA,MAAAyJ,UAAA,GAAAH,SAAA,CAAAI,gBAAA,MAAAvJ,KAAA,CAAAwJ,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAA3E,IAAA;MAEA,IAAAwE,UAAA,CAAAI,MAAA;QACA,MAAAC,eAAA,QAAA3J,KAAA,CAAA4J,IAAA,CAAAH,CAAA,IAAAA,CAAA,CAAA3E,IAAA,KAAAwE,UAAA,IAAAO,MAAA;QACA,KAAA5J,YAAA,GAAA0J,eAAA,GAAAA,eAAA,CAAAhK,IAAA;QACA,KAAAG,QAAA,CAAAkD,UAAA,CAAA8G,KAAA,CAAAC,MAAA;MACA;QACA,KAAA9J,YAAA;QACA,KAAAH,QAAA,CAAAkD,UAAA,CAAA8G,KAAA,CAAAC,MAAA,QAAAzJ,UAAA;MACA;IACA;IAEA+H,aAAA;MACA,SAAApI,YAAA,UAAAK,UAAA;QACA,KAAA0J,KAAA,uBAAA/J,YAAA;MACA;IACA;IAEAqI,YAAAI,KAAA;MACA,KAAApI,UAAA;MACA,KAAAC,qBAAA,CAAAJ,CAAA,GAAAuI,KAAA,CAAAC,OAAA;MACA,KAAApI,qBAAA,CAAAH,CAAA,GAAAsI,KAAA,CAAAE,OAAA;MACA,KAAA9I,QAAA,CAAAkD,UAAA,CAAA8G,KAAA,CAAAC,MAAA;IACA;IAEAxB,UAAA;MACA,KAAAjI,UAAA;MACA,KAAAR,QAAA,CAAAkD,UAAA,CAAA8G,KAAA,CAAAC,MAAA;IACA;IAEAlI,QAAA;MACA,SAAAxB,WAAA;QACA4J,oBAAA,MAAA5J,WAAA;MACA;MAEA4H,MAAA,CAAAiC,mBAAA,gBAAA/B,cAAA;MAEA,SAAArI,QAAA;QACA,KAAAA,QAAA,CAAAqK,OAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}