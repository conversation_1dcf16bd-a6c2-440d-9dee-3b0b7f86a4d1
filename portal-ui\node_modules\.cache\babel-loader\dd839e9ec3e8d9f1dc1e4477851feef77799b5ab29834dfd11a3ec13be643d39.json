{"ast": null, "code": "// import Header from \"@/components/common/header/Header\";\nimport Footer from \"@/components/common/footer/Footer\";\nimport Mider from \"@/components/common/mider/Mider\";\nimport chatAi from \"@/components/common/mider/chatAi\";\nexport default {\n  name: \"Layout\",\n  components: {\n    Footer,\n    Mider,\n    chatAi\n  }\n};", "map": {"version": 3, "names": ["Footer", "<PERSON><PERSON>", "chatAi", "name", "components"], "sources": ["src/components/common/Layout.vue"], "sourcesContent": ["<template>\r\n\t<main class=\"page-wrapper\">\r\n\t\t<!-- <Header/> -->\r\n\t\t\t<slot></slot>\r\n    <Mider/>\r\n    <chatAi/>\r\n\t\t<Footer/>\r\n\t</main>\r\n</template>\r\n\r\n<script>\r\n// import Header from \"@/components/common/header/Header\";\r\nimport Footer from \"@/components/common/footer/Footer\";\r\nimport Mider from \"@/components/common/mider/Mider\";\r\nimport chatAi from \"@/components/common/mider/chatAi\";\r\n\r\nexport default {\r\n\tname: \"Layout\",\r\n\tcomponents:{Footer, Mider, chatAi}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.main-content{\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n</style>\r\n"], "mappings": "AAWA;AACA,OAAAA,MAAA;AACA,OAAAC,KAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAJ,MAAA;IAAAC,KAAA;IAAAC;EAAA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}