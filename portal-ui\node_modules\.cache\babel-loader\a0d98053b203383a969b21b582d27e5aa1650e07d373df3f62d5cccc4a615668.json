{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"section\", {\n    staticClass: \"section gpu-comparison-section\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"gpu-comparison-table\"\n  }, [_c(\"table\", [_c(\"thead\", [_c(\"tr\", [_c(\"th\", [_vm._v(\"GPU型号\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"th\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.name))]);\n  })], 2)]), _c(\"tbody\", [_c(\"tr\", [_c(\"td\", [_vm._v(\"架构\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.architecture))]);\n  })], 2), _c(\"tr\", [_c(\"td\", [_vm._v(\"FP16性能\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.fp16Performance))]);\n  })], 2), _c(\"tr\", [_c(\"td\", [_vm._v(\"FP32性能\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.fp32Performance))]);\n  })], 2), _c(\"tr\", [_c(\"td\", [_vm._v(\"显存\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.memory))]);\n  })], 2), _c(\"tr\", [_c(\"td\", [_vm._v(\"显存类型\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.memoryType))]);\n  })], 2), _c(\"tr\", [_c(\"td\", [_vm._v(\"带宽\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c(\"td\", {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.bandwidth))]);\n  })], 2)])])])])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"section-header\"\n  }, [_c(\"h2\", {\n    staticClass: \"section-title\"\n  }, [_vm._v(\"GPU性能对比\")]), _c(\"p\", {\n    staticClass: \"section-description\"\n  }, [_vm._v(\" 专业GPU性能详细对比，助您选择最适合的计算资源 \")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_m", "_v", "_l", "comparisonGpus", "gpu", "key", "name", "_s", "architecture", "fp16Performance", "fp32Performance", "memory", "memoryType", "bandwidth", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/views/Index/GpuComparison.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"section\", { staticClass: \"section gpu-comparison-section\" }, [\n    _c(\"div\", { staticClass: \"container\" }, [\n      _vm._m(0),\n      _c(\"div\", { staticClass: \"gpu-comparison-table\" }, [\n        _c(\"table\", [\n          _c(\"thead\", [\n            _c(\n              \"tr\",\n              [\n                _c(\"th\", [_vm._v(\"GPU型号\")]),\n                _vm._l(_vm.comparisonGpus, function (gpu) {\n                  return _c(\"th\", { key: gpu.name }, [_vm._v(_vm._s(gpu.name))])\n                }),\n              ],\n              2\n            ),\n          ]),\n          _c(\"tbody\", [\n            _c(\n              \"tr\",\n              [\n                _c(\"td\", [_vm._v(\"架构\")]),\n                _vm._l(_vm.comparisonGpus, function (gpu) {\n                  return _c(\"td\", { key: gpu.name }, [\n                    _vm._v(_vm._s(gpu.architecture)),\n                  ])\n                }),\n              ],\n              2\n            ),\n            _c(\n              \"tr\",\n              [\n                _c(\"td\", [_vm._v(\"FP16性能\")]),\n                _vm._l(_vm.comparisonGpus, function (gpu) {\n                  return _c(\"td\", { key: gpu.name }, [\n                    _vm._v(_vm._s(gpu.fp16Performance)),\n                  ])\n                }),\n              ],\n              2\n            ),\n            _c(\n              \"tr\",\n              [\n                _c(\"td\", [_vm._v(\"FP32性能\")]),\n                _vm._l(_vm.comparisonGpus, function (gpu) {\n                  return _c(\"td\", { key: gpu.name }, [\n                    _vm._v(_vm._s(gpu.fp32Performance)),\n                  ])\n                }),\n              ],\n              2\n            ),\n            _c(\n              \"tr\",\n              [\n                _c(\"td\", [_vm._v(\"显存\")]),\n                _vm._l(_vm.comparisonGpus, function (gpu) {\n                  return _c(\"td\", { key: gpu.name }, [\n                    _vm._v(_vm._s(gpu.memory)),\n                  ])\n                }),\n              ],\n              2\n            ),\n            _c(\n              \"tr\",\n              [\n                _c(\"td\", [_vm._v(\"显存类型\")]),\n                _vm._l(_vm.comparisonGpus, function (gpu) {\n                  return _c(\"td\", { key: gpu.name }, [\n                    _vm._v(_vm._s(gpu.memoryType)),\n                  ])\n                }),\n              ],\n              2\n            ),\n            _c(\n              \"tr\",\n              [\n                _c(\"td\", [_vm._v(\"带宽\")]),\n                _vm._l(_vm.comparisonGpus, function (gpu) {\n                  return _c(\"td\", { key: gpu.name }, [\n                    _vm._v(_vm._s(gpu.bandwidth)),\n                  ])\n                }),\n              ],\n              2\n            ),\n          ]),\n        ]),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"section-header\" }, [\n      _c(\"h2\", { staticClass: \"section-title\" }, [_vm._v(\"GPU性能对比\")]),\n      _c(\"p\", { staticClass: \"section-description\" }, [\n        _vm._v(\" 专业GPU性能详细对比，助您选择最适合的计算资源 \"),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAiC,CAAC,EAAE,CACtEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EACTH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC3BL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,cAAc,EAAE,UAAUC,GAAG,EAAE;IACxC,OAAOP,EAAE,CAAC,IAAI,EAAE;MAAEQ,GAAG,EAAED,GAAG,CAACE;IAAK,CAAC,EAAE,CAACV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;EAChE,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CAAC,EACFT,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACxBL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,cAAc,EAAE,UAAUC,GAAG,EAAE;IACxC,OAAOP,EAAE,CAAC,IAAI,EAAE;MAAEQ,GAAG,EAAED,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACI,YAAY,CAAC,CAAC,CACjC,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACDX,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,cAAc,EAAE,UAAUC,GAAG,EAAE;IACxC,OAAOP,EAAE,CAAC,IAAI,EAAE;MAAEQ,GAAG,EAAED,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACK,eAAe,CAAC,CAAC,CACpC,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACDZ,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,cAAc,EAAE,UAAUC,GAAG,EAAE;IACxC,OAAOP,EAAE,CAAC,IAAI,EAAE;MAAEQ,GAAG,EAAED,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACM,eAAe,CAAC,CAAC,CACpC,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACDb,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACxBL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,cAAc,EAAE,UAAUC,GAAG,EAAE;IACxC,OAAOP,EAAE,CAAC,IAAI,EAAE;MAAEQ,GAAG,EAAED,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACO,MAAM,CAAC,CAAC,CAC3B,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACDd,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,cAAc,EAAE,UAAUC,GAAG,EAAE;IACxC,OAAOP,EAAE,CAAC,IAAI,EAAE;MAAEQ,GAAG,EAAED,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACQ,UAAU,CAAC,CAAC,CAC/B,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACDf,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACxBL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,cAAc,EAAE,UAAUC,GAAG,EAAE;IACxC,OAAOP,EAAE,CAAC,IAAI,EAAE;MAAEQ,GAAG,EAAED,GAAG,CAACE;IAAK,CAAC,EAAE,CACjCV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACS,SAAS,CAAC,CAAC,CAC9B,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIlB,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAACH,GAAG,CAACK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAC/DJ,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAC9CH,GAAG,CAACK,EAAE,CAAC,4BAA4B,CAAC,CACrC,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDN,MAAM,CAACoB,aAAa,GAAG,IAAI;AAE3B,SAASpB,MAAM,EAAEmB,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}