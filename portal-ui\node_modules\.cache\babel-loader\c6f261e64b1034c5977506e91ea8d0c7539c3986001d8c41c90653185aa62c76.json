{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    ref: \"globeContainer\",\n    staticClass: \"globe-container\"\n  }, [_c(\"div\", {\n    ref: \"canvasContainer\",\n    staticClass: \"globe-canvas\"\n  }), _c(\"div\", {\n    staticClass: \"globe-overlay\"\n  }, [_c(\"div\", {\n    staticClass: \"globe-title\"\n  }, [_vm._v(\"全球算力布局\")]), _c(\"div\", {\n    staticClass: \"globe-subtitle\"\n  }, [_vm._v(\"分布式高性能计算网络\")]), _vm.selectedNode ? _c(\"div\", {\n    staticClass: \"node-info\",\n    style: _vm.nodeInfoStyle\n  }, [_c(\"h4\", [_vm._v(_vm._s(_vm.selectedNode.name))]), _c(\"p\", [_vm._v(_vm._s(_vm.selectedNode.description))]), _c(\"div\", {\n    staticClass: \"node-stats\"\n  }, [_c(\"span\", [_vm._v(\"GPU节点: \" + _vm._s(_vm.selectedNode.gpuCount))]), _c(\"span\", [_vm._v(\"算力: \" + _vm._s(_vm.selectedNode.computePower))])])]) : _vm._e()])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "ref", "staticClass", "_v", "selectedNode", "style", "nodeInfoStyle", "_s", "name", "description", "gpuCount", "computePower", "_e", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/components/common/Globe3D.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { ref: \"globeContainer\", staticClass: \"globe-container\" }, [\n    _c(\"div\", { ref: \"canvasContainer\", staticClass: \"globe-canvas\" }),\n    _c(\"div\", { staticClass: \"globe-overlay\" }, [\n      _c(\"div\", { staticClass: \"globe-title\" }, [_vm._v(\"全球算力布局\")]),\n      _c(\"div\", { staticClass: \"globe-subtitle\" }, [\n        _vm._v(\"分布式高性能计算网络\"),\n      ]),\n      _vm.selectedNode\n        ? _c(\"div\", { staticClass: \"node-info\", style: _vm.nodeInfoStyle }, [\n            _c(\"h4\", [_vm._v(_vm._s(_vm.selectedNode.name))]),\n            _c(\"p\", [_vm._v(_vm._s(_vm.selectedNode.description))]),\n            _c(\"div\", { staticClass: \"node-stats\" }, [\n              _c(\"span\", [\n                _vm._v(\"GPU节点: \" + _vm._s(_vm.selectedNode.gpuCount)),\n              ]),\n              _c(\"span\", [\n                _vm._v(\"算力: \" + _vm._s(_vm.selectedNode.computePower)),\n              ]),\n            ]),\n          ])\n        : _vm._e(),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,GAAG,EAAE,gBAAgB;IAAEC,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC1EH,EAAE,CAAC,KAAK,EAAE;IAAEE,GAAG,EAAE,iBAAiB;IAAEC,WAAW,EAAE;EAAe,CAAC,CAAC,EAClEH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAc,CAAC,EAAE,CAACJ,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC7DJ,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CJ,GAAG,CAACK,EAAE,CAAC,YAAY,CAAC,CACrB,CAAC,EACFL,GAAG,CAACM,YAAY,GACZL,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE,WAAW;IAAEG,KAAK,EAAEP,GAAG,CAACQ;EAAc,CAAC,EAAE,CAChEP,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACS,EAAE,CAACT,GAAG,CAACM,YAAY,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC,EACjDT,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACS,EAAE,CAACT,GAAG,CAACM,YAAY,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC,EACvDV,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACK,EAAE,CAAC,SAAS,GAAGL,GAAG,CAACS,EAAE,CAACT,GAAG,CAACM,YAAY,CAACM,QAAQ,CAAC,CAAC,CACtD,CAAC,EACFX,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACK,EAAE,CAAC,MAAM,GAAGL,GAAG,CAACS,EAAE,CAACT,GAAG,CAACM,YAAY,CAACO,YAAY,CAAC,CAAC,CACvD,CAAC,CACH,CAAC,CACH,CAAC,GACFb,GAAG,CAACc,EAAE,EAAE,CACb,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBhB,MAAM,CAACiB,aAAa,GAAG,IAAI;AAE3B,SAASjB,MAAM,EAAEgB,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}