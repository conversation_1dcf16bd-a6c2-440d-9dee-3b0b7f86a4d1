{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport Layout from \"@/components/common/Layout-header\";\nimport { getAnyData, postAnyData } from \"@/api/login\";\nimport Cookies from 'js-cookie';\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\nexport default {\n  name: 'PersonalCenter',\n  components: {\n    Layout,\n    SlideNotification\n  },\n  data() {\n    return {\n      activeTab: 'basic',\n      user: {\n        id: null,\n        username: '',\n        nickName: '',\n        password: '',\n        avatarUrl: null,\n        isChangingPassword: false,\n        sex: '',\n        phone: '',\n        email: null,\n        balance: 0,\n        isReal: 0,\n        realName: '',\n        realId: '',\n        realNumber: 0,\n        tenantId: '',\n        isLogin: 0,\n        notificationMessage: '',\n        notificationType: 'info'\n      },\n      showNotification: false,\n      // Password related\n      showPasswordModal: false,\n      currentPassword: '',\n      newPassword: '',\n      confirmPassword: '',\n      passwordError: '',\n      confirmPasswordError: '',\n      // Username related\n      showUsernameModal: false,\n      newUsername: '',\n      // Gender related\n      showGenderModal: false,\n      selectedGender: '',\n      // Phone related\n      showPhoneModal: false,\n      newPhone: '',\n      verifyCode: '',\n      countdown: 0,\n      isCountingDown: false,\n      // ID Verification related\n      realName: '',\n      idCardNumber: '',\n      realNameError: '',\n      idCardError: '',\n      verificationError: '',\n      agreementChecked: false,\n      verificationStatus: '',\n      isVerified: false,\n      // Cookie related\n      userInfoCookieName: 'user_info',\n      userInfoExpiryDays: 7\n    };\n  },\n  computed: {\n    canSubmitVerification() {\n      return this.realName && this.idCardNumber && !this.realNameError && !this.idCardError && this.agreementChecked;\n    }\n  },\n  methods: {\n    switchTab(tab) {\n      this.activeTab = tab;\n      this.$router.push({\n        query: {\n          ...this.$route.query,\n          activeTab: tab\n        }\n      });\n    },\n    userInitial() {\n      return this.user.nickName && this.user.nickName.length > 0 ? this.user.nickName.charAt(0).toUpperCase() : 'N';\n    },\n    openIdVerification() {\n      if (this.user.isReal === 1) {\n        this.$message.info('您已通过实名认证');\n        return;\n      }\n      this.activeTab = 'verification';\n    },\n    // 获取完整用户信息\n    async getUserInfo() {\n      try {\n        const res = await postAnyData(\"/logout/cilent/getInfo\");\n        if (res.data.code === 200) {\n          const userData = res.data.data;\n          this.user = {\n            ...this.user,\n            ...userData\n          };\n          // 根据isReal更新认证状态\n          this.verificationStatus = userData.isReal === 1 ? '已认证' : '未认证';\n          this.saveUserInfoToCookie(userData);\n          return userData;\n        } else {\n          throw new Error(res.data.msg || \"获取用户信息失败\");\n        }\n      } catch (error) {\n        throw error;\n      }\n    },\n    validateNewPassword() {\n      const newPassword = this.newPassword?.trim();\n      this.passwordError = '';\n\n      // 1. 检查是否为空\n      if (!newPassword) {\n        this.passwordError = '请输入新密码';\n        return;\n      }\n\n      // 2. 检查密码长度\n      const hasMinLength = newPassword.length >= 8;\n      if (!hasMinLength) {\n        this.passwordError = '密码长度至少为8位';\n        return;\n      }\n\n      // 3. 检查密码复杂度\n      const hasLetter = /[a-zA-Z]/.test(newPassword);\n      const hasNumber = /[0-9]/.test(newPassword);\n      const hasSymbol = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(newPassword);\n      const hasUpperLower = /[A-Z]/.test(newPassword) && /[a-z]/.test(newPassword);\n      let strengthCount = 0;\n      if (hasLetter && hasNumber) strengthCount++; // 字母+数字组合\n      if (hasSymbol) strengthCount++; // 包含特殊符号\n      if (hasUpperLower) strengthCount++; // 包含大小写字母\n\n      // 4. 最终判断：长度必须 ≥ 8，复杂度至少满足 2 项\n      if (strengthCount < 2) {\n        const missing = [];\n        if (!(hasLetter && hasNumber)) missing.push('包含数字和字母');\n        if (!hasSymbol) missing.push('包含特殊符号');\n        if (!hasUpperLower) missing.push('包含大小写字母');\n        this.passwordError = `密码需至少满足以下两项要求：${missing.join('、')}`;\n        return;\n      }\n\n      // 5. 校验确认密码是否一致\n      if (this.confirmPassword) {\n        this.validateConfirmPassword();\n      }\n    },\n    validateConfirmPassword() {\n      if (!this.confirmPassword) {\n        this.confirmPasswordError = '请确认新密码';\n      } else if (this.confirmPassword !== this.newPassword) {\n        this.confirmPasswordError = '两次输入的密码不一致';\n      } else {\n        this.confirmPasswordError = '';\n      }\n    },\n    async changePassword() {\n      if (!this.currentPassword) {\n        this.showNotificationMessage('请输入当前密码', 'warning');\n        return;\n      }\n      this.validateNewPassword();\n      this.validateConfirmPassword();\n      if (this.passwordError || this.confirmPasswordError) {\n        return;\n      }\n      this.isChangingPassword = true;\n      try {\n        const currentUserInfo = await this.getUserInfo();\n        const params = {\n          ...currentUserInfo,\n          password: this.currentPassword,\n          newPassword: this.newPassword\n        };\n        const res = await postAnyData(\"/logout/cilent/changePwd\", params);\n        if (res.data.code === 200) {\n          this.showNotificationMessage('密码修改成功', 'success');\n          this.showPasswordModal = false;\n          this.currentPassword = '';\n          this.newPassword = '';\n          this.confirmPassword = '';\n        } else {\n          this.$nextTick(() => {\n            this.showNotificationMessage('原密码输入错误，请检查重试', 'error');\n          });\n        }\n      } catch (error) {\n        this.showNotificationMessage('密码修改过程中出错，请稍后重试', 'error');\n      } finally {\n        this.isChangingPassword = false;\n      }\n    },\n    // 显示通知的方法\n    showNotificationMessage(message, type = 'info') {\n      this.notificationMessage = message;\n      this.notificationType = type;\n      this.showNotification = true;\n\n      // 3秒后自动关闭通知\n      setTimeout(() => {\n        this.showNotification = false;\n      }, 3000);\n    },\n    // Username related methods\n    async changeUsername() {\n      if (!this.newUsername.trim()) {\n        this.$message.warning('昵称不能为空');\n        return;\n      }\n      try {\n        const currentUserInfo = await this.getUserInfo();\n        const params = {\n          ...currentUserInfo,\n          nickName: this.newUsername\n        };\n        const res = await postAnyData(\"/logout/cilent/updateInfo\", params);\n        if (res.data.code === 200) {\n          this.user.nickName = this.newUsername;\n          this.saveUserInfoToCookie({\n            ...currentUserInfo,\n            nickName: this.newUsername\n          });\n          this.showUsernameModal = false;\n          this.newUsername = '';\n          this.$message.success('昵称修改成功');\n        } else {\n          this.$message.error(res.data.message || '修改失败');\n        }\n      } catch (error) {\n        this.$message.error('网络错误');\n      }\n    },\n    // Gender related methods\n    async changeGender() {\n      if (!this.selectedGender) {\n        this.$message.warning('请选择性别');\n        return;\n      }\n      try {\n        const currentUserInfo = await this.getUserInfo();\n        const params = {\n          ...currentUserInfo,\n          sex: this.selectedGender\n        };\n        const res = await postAnyData(\"/logout/cilent/updateInfo\", params);\n        if (res.data.code === 200) {\n          this.user.sex = this.selectedGender;\n          this.saveUserInfoToCookie({\n            ...currentUserInfo,\n            sex: this.selectedGender\n            // isReal : 0\n          });\n\n          this.showGenderModal = false;\n          this.$message.success('性别设置成功');\n        } else {\n          this.$message.error(res.data.message || '修改失败');\n        }\n      } catch (error) {\n        this.$message.error('网络错误');\n      }\n    },\n    // 姓名脱敏处理（保留姓氏，名字用*代替）\n    desensitizeName(name) {\n      if (!name) return '';\n      if (name.length <= 1) return name;\n      return name.charAt(0) + '*'.repeat(name.length - 1);\n    },\n    // 身份证号脱敏处理（显示前4位和后4位，中间用*代替）\n    desensitizeIdCard(idCard) {\n      if (!idCard) return '';\n      if (idCard.length <= 8) return idCard;\n      return idCard.substring(0, 4) + '********' + idCard.substring(idCard.length - 4);\n    },\n    // Phone related methods\n    getVerifyCode() {\n      if (!this.newPhone) {\n        this.$message.warning('请输入手机号');\n        return;\n      }\n\n      // 模拟发送验证码\n      this.isCountingDown = true;\n      this.countdown = 60;\n      const timer = setInterval(() => {\n        this.countdown--;\n        if (this.countdown <= 0) {\n          clearInterval(timer);\n          this.isCountingDown = false;\n        }\n      }, 1000);\n      this.$message.success('验证码已发送');\n    },\n    async changePhone() {\n      if (!this.newPhone) {\n        this.$message.warning('请输入新手机号');\n        return;\n      }\n      if (!this.verifyCode) {\n        this.$message.warning('请输入验证码');\n        return;\n      }\n      try {\n        const currentUserInfo = await this.getUserInfo();\n        const params = {\n          ...currentUserInfo,\n          phone: this.newPhone\n        };\n        const res = await postAnyData(\"/logout/cilent/updateInfo\", params);\n        if (res.data.code === 200) {\n          this.user.phone = this.newPhone;\n          this.saveUserInfoToCookie({\n            ...currentUserInfo,\n            phone: this.newPhone\n          });\n          this.showPhoneModal = false;\n          this.newPhone = '';\n          this.verifyCode = '';\n          this.$message.success('手机号修改成功');\n        } else {\n          this.$message.error(res.data.message || '修改失败');\n        }\n      } catch (error) {\n        this.$message.error('网络错误');\n      }\n    },\n    // ID Verification methods\n    validateRealName() {\n      if (!this.realName) {\n        this.realNameError = '请输入正确的姓名';\n        return false;\n      }\n      const nameRegex = /^[\\u4e00-\\u9fa5]{2,10}$/;\n      if (!nameRegex.test(this.realName)) {\n        this.realNameError = '请输入正确的姓名';\n        return false;\n      }\n      this.realNameError = '';\n      return true;\n    },\n    validateIdCard() {\n      if (!this.idCardNumber) {\n        this.idCardError = '请输入正确的身份证号码';\n        return false;\n      }\n      const idCardRegex = /(^\\d{15$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\n      if (!idCardRegex.test(this.idCardNumber)) {\n        this.idCardError = '请输入正确的身份证号码';\n        return false;\n      }\n      this.idCardError = '';\n      return true;\n    },\n    async submitIdVerification() {\n      const isNameValid = this.validateRealName();\n      const isIdCardValid = this.validateIdCard();\n      if (!isNameValid || !isIdCardValid || !this.agreementChecked) {\n        return;\n      }\n      const currentUserInfo = await this.getUserInfo();\n      const verificationData = {\n        name: this.realName,\n        id: this.idCardNumber,\n        username: currentUserInfo.username,\n        userId: currentUserInfo.id\n      };\n      const res = await postAnyData(\"/idVerification/verify\", verificationData);\n      if (res.data.code === 200) {\n        this.user.realName = this.realName;\n        this.user.realId = this.idCardNumber;\n        this.user.isReal = 1;\n        this.saveUserInfoToCookie({\n          ...currentUserInfo,\n          realName: this.realName,\n          realId: this.idCardNumber,\n          isReal: 1\n        });\n\n        // this.verificationStatus = res.data.msg;\n        this.showNotificationMessage(res.data.msg, 'success');\n        // setTimeout(() => {\n        //   location.reload(true);\n        // }, 3000);\n      } else {\n        // this.verificationError = res.data.msg || '实名认证失败，请检查信息是否正确';\n        this.showNotificationMessage(res.data.msg, 'error');\n      }\n    },\n    // Cookie related methods\n    getUserInfoFromCookie() {\n      const userInfoStr = Cookies.get(this.userInfoCookieName);\n      if (userInfoStr) {\n        try {\n          return JSON.parse(userInfoStr);\n        } catch (e) {\n          return null;\n        }\n      }\n      return null;\n    },\n    saveUserInfoToCookie(userInfo) {\n      const infoToSave = {\n        ...userInfo,\n        timestamp: new Date().getTime()\n      };\n      Cookies.set(this.userInfoCookieName, JSON.stringify(infoToSave), {\n        expires: this.userInfoExpiryDays,\n        secure: true,\n        sameSite: 'strict'\n      });\n    },\n    isCacheValid(cachedData) {\n      if (!cachedData || !cachedData.timestamp) return false;\n      const oneDay = 24 * 60 * 60 * 1000; // 1 day in milliseconds\n      return new Date().getTime() - cachedData.timestamp < oneDay;\n    },\n    async fetchUserInfo() {\n      const cachedUserInfo = this.getUserInfoFromCookie();\n      if (cachedUserInfo && this.isCacheValid(cachedUserInfo)) {\n        this.updateLocalUserData(cachedUserInfo);\n        return;\n      }\n      await this.getUserInfo();\n    },\n    updateLocalUserData(userData) {\n      this.user = {\n        id: userData.id || this.user.id,\n        username: userData.username || this.user.username,\n        nickName: userData.nickName || this.user.nickName,\n        password: this.currentPassword || this.user.password,\n        avatarUrl: userData.avatarUrl || this.user.avatarUrl,\n        sex: userData.sex || this.user.sex,\n        phone: userData.phone || this.user.phone,\n        email: userData.email || this.user.email,\n        balance: userData.balance || this.user.balance,\n        isReal: userData.isReal !== undefined ? userData.isReal : this.user.isReal,\n        realName: userData.realName || this.user.realName,\n        realId: userData.realId || this.user.realId,\n        realNumber: userData.realNumber || this.user.realNumber,\n        tenantId: userData.tenantId || this.user.tenantId,\n        isLogin: userData.isLogin || this.user.isLogin\n      };\n      // 根据isReal更新认证状态\n      this.verificationStatus = this.user.isReal === 1 ? '已认证' : '未认证';\n    }\n  },\n  watch: {\n    realName() {\n      this.validateRealName();\n    },\n    idCardNumber() {\n      this.validateIdCard();\n    },\n    newPassword() {\n      this.validateNewPassword();\n    },\n    confirmPassword() {\n      this.validateConfirmPassword();\n    }\n  },\n  async created() {\n    this.user = {\n      username: '',\n      phone: ''\n    };\n    try {\n      await this.fetchUserInfo();\n      this.selectedGender = this.user.sex;\n\n      // 添加这部分代码，从URL参数中读取activeTab\n      if (this.$route.query.activeTab === 'verification') {\n        this.activeTab = 'verification';\n      }\n    } catch (error) {\n      // console.error(error);\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "getAnyData", "postAnyData", "Cookies", "SlideNotification", "name", "components", "data", "activeTab", "user", "id", "username", "nick<PERSON><PERSON>", "password", "avatarUrl", "isChangingPassword", "sex", "phone", "email", "balance", "isReal", "realName", "realId", "realNumber", "tenantId", "is<PERSON>ogin", "notificationMessage", "notificationType", "showNotification", "showPasswordModal", "currentPassword", "newPassword", "confirmPassword", "passwordError", "confirmPasswordError", "showUsernameModal", "newUsername", "showGenderModal", "selected<PERSON><PERSON>", "showPhoneModal", "newPhone", "verifyCode", "countdown", "isCountingDown", "idCardNumber", "realNameError", "idCardError", "verificationError", "agreementChecked", "verificationStatus", "isVerified", "userInfoCookieName", "userInfoExpiryDays", "computed", "canSubmitVerification", "methods", "switchTab", "tab", "$router", "push", "query", "$route", "userInitial", "length", "char<PERSON>t", "toUpperCase", "openIdVerification", "$message", "info", "getUserInfo", "res", "code", "userData", "saveUserInfoToCookie", "Error", "msg", "error", "validateNewPassword", "trim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasLetter", "test", "hasNumber", "hasSymbol", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "strengthCount", "missing", "join", "validateConfirmPassword", "changePassword", "showNotificationMessage", "currentUserInfo", "params", "$nextTick", "message", "type", "setTimeout", "changeUsername", "warning", "success", "changeGender", "desensitizeName", "repeat", "desensitizeIdCard", "idCard", "substring", "getVerifyCode", "timer", "setInterval", "clearInterval", "changePhone", "validateRealName", "nameRegex", "validateIdCard", "idCardRegex", "submitIdVerification", "isNameValid", "isIdCardValid", "verificationData", "userId", "getUserInfoFromCookie", "userInfoStr", "get", "JSON", "parse", "e", "userInfo", "infoToSave", "timestamp", "Date", "getTime", "set", "stringify", "expires", "secure", "sameSite", "isCache<PERSON><PERSON>d", "cachedData", "oneDay", "fetchUserInfo", "cachedUserInfo", "updateLocalUserData", "undefined", "watch", "created"], "sources": ["src/views/Personal/personal.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"personal-center\">\r\n    <SlideNotification\r\n        v-if=\"showNotification\"\r\n        :message=\"notificationMessage\"\r\n        :type=\"notificationType\"\r\n        @close=\"showNotification = false\"\r\n    />\r\n    <div class=\"content-wrapper\">\r\n      <!-- Left Navigation Menu -->\r\n      <div class=\"left-navigation\">\r\n        <div class=\"center-title\">个人中心</div>\r\n        <div class=\"nav-menu\">\r\n          <div class=\"nav-item1\"\r\n               :class=\"{ active: activeTab === 'basic' }\"\r\n               @click=\"switchTab('basic')\">\r\n            基本信息\r\n          </div>\r\n          <div class=\"nav-item1\"\r\n               :class=\"{ active: activeTab === 'verification' }\"\r\n               @click=\"switchTab('verification')\">\r\n            实名认证\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Main Content Container -->\r\n      <div class=\"main-container\">\r\n        <!-- Basic Information Tab -->\r\n        <div v-if=\"activeTab === 'basic'\" class=\"tab-content\">\r\n          <div class=\"section-header\">\r\n            <h2>基本信息</h2>\r\n          </div>\r\n\r\n          <div class=\"user-info-container\">\r\n            <!-- User Profile Card -->\r\n            <div class=\"profile-card\">\r\n              <h3 class=\"card-title\">用户信息</h3>\r\n              <div class=\"user-avatar-section\">\r\n                <div class=\"avatar\">\r\n                  <img v-if=\"user.avatarUrl\" :src=\"user.avatarUrl\" class=\"avatar-img\">\r\n                  <span v-else class=\"avatar-text\">{{userInitial()}}</span>\r\n                </div>\r\n                <div class=\"username-section\">\r\n                  <div class=\"username\">\r\n                    {{user.nickName || '未设置'}}\r\n                    <span class=\"edit-icon\" @click=\"showUsernameModal = true\">🖊</span>\r\n                  </div>\r\n                  <div class=\"user-info-item\">\r\n                    <span class=\"info-label\">手机号</span>\r\n                    <span>{{user.phone || '未绑定'}}</span>\r\n                  </div>\r\n                  <div class=\"user-info-item\">\r\n                    <span class=\"info-label\">性别</span>\r\n                    <span>{{user.sex || '未设置'}}</span>\r\n                    <span class=\"edit-icon\" @click=\"showGenderModal = true\">🖊</span>\r\n                  </div>\r\n                  <div class=\"user-info-item\">\r\n                    <span class=\"info-label\">余额</span>\r\n                    <span>¥{{user.balance?.toFixed(2) || '0.00'}}</span>\r\n                  </div>\r\n                  <div class=\"verification-badge\" @click=\"openIdVerification\">\r\n                    <span class=\"badge\">\r\n                      <span class=\"check-icon\"></span> 个人认证\r\n                      <span v-if=\"verificationStatus\" class=\"status-text\">({{verificationStatus}})</span>\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Login Information Card -->\r\n            <div class=\"login-card\">\r\n              <h3 class=\"card-title\">登录信息</h3>\r\n\r\n              <!-- Account & Password Section -->\r\n              <div class=\"login-section\">\r\n                <h4 class=\"section-subtitle\">账号密码</h4>\r\n\r\n                <div class=\"login-item\">\r\n                  <div class=\"login-label\">账号</div>\r\n                  <div class=\"login-value\">\r\n                    {{user.username}}\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"login-item\">\r\n                  <div class=\"login-label\">密码</div>\r\n                  <div class=\"login-content\">\r\n                    <div class=\"login-description\">设置密码后可通过账号登录</div>\r\n                    <div class=\"login-value\">\r\n                      ••••••••\r\n                      <button class=\"edit-btn\" @click=\"showPasswordModal = true\">修改</button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Security Phone Section -->\r\n              <div class=\"login-section\">\r\n                <h4 class=\"section-subtitle\">安全手机</h4>\r\n\r\n                <div class=\"login-item\">\r\n                  <div class=\"login-label\">手机号</div>\r\n                  <div class=\"login-value\">\r\n                    {{user.phone}}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Email Section -->\r\n              <div class=\"login-section\" v-if=\"user.email\">\r\n                <h4 class=\"section-subtitle\">电子邮箱</h4>\r\n                <div class=\"login-item\">\r\n                  <div class=\"login-label\">邮箱</div>\r\n                  <div class=\"login-value\">\r\n                    {{user.email || '未绑定'}}\r\n                    <button class=\"edit-btn\" @click=\"showEmailModal = true\">修改</button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- ID Verification Tab -->\r\n        <div v-if=\"activeTab === 'verification'\" class=\"tab-content\">\r\n          <div class=\"section-header\">\r\n            <h2>个人认证</h2>\r\n            <div v-if=\"verificationError\" class=\"verification-error\">\r\n              {{verificationError}}\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"verification-container\">\r\n            <div v-if=\"user.isReal === 1\" class=\"verified-info\">\r\n              <div class=\"verified-status\">\r\n                <i class=\"el-icon-success\"></i> 已认证\r\n              </div>\r\n              <div class=\"verified-item\">\r\n                <span class=\"verified-label\">真实姓名：</span>\r\n                <span>{{ desensitizeName(user.realName) }}</span>\r\n              </div>\r\n              <div class=\"verified-item\">\r\n                <span class=\"verified-label\">身份证号：</span>\r\n                <span>{{ desensitizeIdCard(user.realId) }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div v-else class=\"verification-form\">\r\n              <div class=\"form-group\">\r\n                <label>真实姓名</label>\r\n                <input\r\n                    type=\"text\"\r\n                    v-model=\"realName\"\r\n                    placeholder=\"请输入真实姓名\"\r\n                    :class=\"{'error-input': realNameError}\"\r\n                >\r\n                <div v-if=\"realNameError\" class=\"error-text\">\r\n                  <i class=\"error-icon\"></i> {{realNameError}}\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label>身份证号码</label>\r\n                <input\r\n                    type=\"text\"\r\n                    v-model=\"idCardNumber\"\r\n                    placeholder=\"请输入身份证号码\"\r\n                    :class=\"{'error-input': idCardError}\"\r\n                >\r\n                <div v-if=\"idCardError\" class=\"error-text\">\r\n                  <i class=\"error-icon\"></i> {{idCardError}}\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"agreement-checkbox\">\r\n                <input type=\"checkbox\" id=\"agreement\" v-model=\"agreementChecked\">\r\n                <label for=\"agreement\">\r\n                  我已阅读并同意 天工开物的\r\n                  <!-- <a href=\"#\" class=\"link\">服务条款</a> 和\r\n                  <a href=\"#\" class=\"link\">隐私政策</a> -->\r\n                  <router-link to=\"/help/user-agreement\" class=\"link\">服务条款</router-link> 和\r\n                  <router-link to=\"/help/privacy-policy\" class=\"link\">隐私政策</router-link>\r\n\r\n                </label>\r\n              </div>\r\n\r\n              <button class=\"submit-btn\" :disabled=\"!canSubmitVerification\" @click=\"submitIdVerification\">提交</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Password Modal -->\r\n    <div v-if=\"showPasswordModal\" class=\"modal\">\r\n      <div class=\"modal-content\">\r\n        <div class=\"modal-header\">\r\n          <h3>修改密码</h3>\r\n          <span class=\"close-btn\" @click=\"showPasswordModal = false\">&times;</span>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n          <div class=\"form-group\">\r\n            <label>当前密码</label>\r\n            <input\r\n                type=\"password\"\r\n                v-model=\"currentPassword\"\r\n                placeholder=\"请输入当前密码\"\r\n            >\r\n          </div>\r\n          <div class=\"form-group\">\r\n            <label>新密码</label>\r\n            <input\r\n                type=\"password\"\r\n                v-model=\"newPassword\"\r\n                placeholder=\"请输入新密码\"\r\n                @blur=\"validateNewPassword\"\r\n            >\r\n            <div v-if=\"passwordError\" class=\"error-text\">\r\n              <i class=\"error-icon\"></i> {{passwordError}}\r\n            </div>\r\n          </div>\r\n          <div class=\"form-group\">\r\n            <label>确认新密码</label>\r\n            <input\r\n                type=\"password\"\r\n                v-model=\"confirmPassword\"\r\n                placeholder=\"请再次输入新密码\"\r\n                @blur=\"validateConfirmPassword\"\r\n            >\r\n            <div v-if=\"confirmPasswordError\" class=\"error-text\">\r\n              <i class=\"error-icon\"></i> {{confirmPasswordError}}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"modal-footer\">\r\n          <button class=\"cancel-btn\" @click=\"showPasswordModal = false\">取消</button>\r\n          <button\r\n              class=\"confirm-btn\"\r\n              @click=\"changePassword\"\r\n          >\r\n            确认\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Username Modal -->\r\n    <div v-if=\"showUsernameModal\" class=\"modal\">\r\n      <div class=\"modal-content\">\r\n        <div class=\"modal-header\">\r\n          <h3>修改昵称</h3>\r\n          <span class=\"close-btn\" @click=\"showUsernameModal = false\">&times;</span>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n          <div class=\"form-group\">\r\n            <label>新昵称</label>\r\n            <input type=\"text\" v-model=\"newUsername\" placeholder=\"请输入新昵称\">\r\n          </div>\r\n        </div>\r\n        <div class=\"modal-footer\">\r\n          <button class=\"cancel-btn\" @click=\"showUsernameModal = false\">取消</button>\r\n          <button class=\"confirm-btn\" @click=\"changeUsername\">确认</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Gender Modal -->\r\n    <div v-if=\"showGenderModal\" class=\"modal\">\r\n      <div class=\"modal-content\">\r\n        <div class=\"modal-header\">\r\n          <h3>设置性别</h3>\r\n          <span class=\"close-btn\" @click=\"showGenderModal = false\">&times;</span>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n          <div class=\"gender-options\">\r\n            <div class=\"gender-option\">\r\n              <input type=\"radio\" id=\"male\" name=\"gender\" value=\"男\" v-model=\"selectedGender\">\r\n              <label for=\"male\">男</label>\r\n            </div>\r\n            <div class=\"gender-option\">\r\n              <input type=\"radio\" id=\"female\" name=\"gender\" value=\"女\" v-model=\"selectedGender\">\r\n              <label for=\"female\">女</label>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"modal-footer\">\r\n          <button class=\"cancel-btn\" @click=\"showGenderModal = false\">取消</button>\r\n          <button class=\"confirm-btn\" @click=\"changeGender\">确认</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Phone Modal -->\r\n    <div v-if=\"showPhoneModal\" class=\"modal\">\r\n      <div class=\"modal-content\">\r\n        <div class=\"modal-header\">\r\n          <h3>修改手机号</h3>\r\n          <span class=\"close-btn\" @click=\"showPhoneModal = false\">&times;</span>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n          <div class=\"form-group\">\r\n            <label>新手机号</label>\r\n            <input type=\"text\" v-model=\"newPhone\" placeholder=\"请输入新手机号\">\r\n          </div>\r\n          <div class=\"form-group\">\r\n            <label>验证码</label>\r\n            <div class=\"verify-code-input\">\r\n              <input type=\"text\" v-model=\"verifyCode\" placeholder=\"请输入验证码\">\r\n              <button class=\"get-code-btn\" @click=\"getVerifyCode\" :disabled=\"isCountingDown\">\r\n                {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"modal-footer\">\r\n          <button class=\"cancel-btn\" @click=\"showPhoneModal = false\">取消</button>\r\n          <button class=\"confirm-btn\" @click=\"changePhone\">确认</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout-header\";\r\nimport {getAnyData, postAnyData} from \"@/api/login\";\r\nimport Cookies from 'js-cookie';\r\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\r\n\r\n\r\nexport default {\r\n  name: 'PersonalCenter',\r\n  components: { Layout,SlideNotification },\r\n  data() {\r\n    return {\r\n      activeTab: 'basic',\r\n      user: {\r\n        id: null,\r\n        username: '',\r\n        nickName: '',\r\n        password: '',\r\n        avatarUrl: null,\r\n        isChangingPassword: false,\r\n\r\n        sex: '',\r\n        phone: '',\r\n        email: null,\r\n        balance: 0,\r\n        isReal: 0,\r\n        realName: '',\r\n        realId: '',\r\n        realNumber: 0,\r\n        tenantId: '',\r\n        isLogin: 0,\r\n        notificationMessage: '',\r\n        notificationType: 'info',\r\n      },\r\n      showNotification: false,\r\n      // Password related\r\n      showPasswordModal: false,\r\n      currentPassword: '',\r\n      newPassword: '',\r\n      confirmPassword: '',\r\n      passwordError: '',\r\n      confirmPasswordError: '',\r\n\r\n      // Username related\r\n      showUsernameModal: false,\r\n      newUsername: '',\r\n\r\n      // Gender related\r\n      showGenderModal: false,\r\n      selectedGender: '',\r\n\r\n      // Phone related\r\n      showPhoneModal: false,\r\n      newPhone: '',\r\n      verifyCode: '',\r\n      countdown: 0,\r\n      isCountingDown: false,\r\n\r\n      // ID Verification related\r\n      realName: '',\r\n      idCardNumber: '',\r\n      realNameError: '',\r\n      idCardError: '',\r\n      verificationError: '',\r\n      agreementChecked: false,\r\n      verificationStatus: '',\r\n      isVerified: false,\r\n\r\n      // Cookie related\r\n      userInfoCookieName: 'user_info',\r\n      userInfoExpiryDays: 7\r\n    }\r\n  },\r\n  computed: {\r\n    canSubmitVerification() {\r\n      return this.realName &&\r\n          this.idCardNumber &&\r\n          !this.realNameError &&\r\n          !this.idCardError &&\r\n          this.agreementChecked;\r\n    }\r\n  },\r\n  methods: {\r\n\r\n    switchTab(tab) {\r\n      this.activeTab = tab;\r\n      this.$router.push({ query: { ...this.$route.query, activeTab: tab } });\r\n    },\r\n    userInitial() {\r\n      return this.user.nickName && this.user.nickName.length > 0\r\n          ? this.user.nickName.charAt(0).toUpperCase()\r\n          : 'N';\r\n    },\r\n\r\n    openIdVerification() {\r\n      if (this.user.isReal === 1) {\r\n        this.$message.info('您已通过实名认证');\r\n        return;\r\n      }\r\n      this.activeTab = 'verification';\r\n    },\r\n\r\n    // 获取完整用户信息\r\n    async getUserInfo() {\r\n      try {\r\n        const res = await postAnyData(\"/logout/cilent/getInfo\");\r\n        if (res.data.code === 200) {\r\n          const userData = res.data.data;\r\n          this.user = {\r\n            ...this.user,\r\n            ...userData\r\n          };\r\n          // 根据isReal更新认证状态\r\n          this.verificationStatus = userData.isReal === 1 ? '已认证' : '未认证';\r\n          this.saveUserInfoToCookie(userData);\r\n          return userData;\r\n        } else {\r\n          throw new Error(res.data.msg || \"获取用户信息失败\");\r\n        }\r\n      } catch (error) {\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    validateNewPassword() {\r\n      const newPassword = this.newPassword?.trim();\r\n      this.passwordError = '';\r\n\r\n      // 1. 检查是否为空\r\n      if (!newPassword) {\r\n        this.passwordError = '请输入新密码';\r\n        return;\r\n      }\r\n\r\n      // 2. 检查密码长度\r\n      const hasMinLength = newPassword.length >= 8;\r\n      if (!hasMinLength) {\r\n        this.passwordError = '密码长度至少为8位';\r\n        return;\r\n      }\r\n\r\n      // 3. 检查密码复杂度\r\n      const hasLetter = /[a-zA-Z]/.test(newPassword);\r\n      const hasNumber = /[0-9]/.test(newPassword);\r\n      const hasSymbol = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(newPassword);\r\n      const hasUpperLower = /[A-Z]/.test(newPassword) && /[a-z]/.test(newPassword);\r\n\r\n      let strengthCount = 0;\r\n      if (hasLetter && hasNumber) strengthCount++; // 字母+数字组合\r\n      if (hasSymbol) strengthCount++;              // 包含特殊符号\r\n      if (hasUpperLower) strengthCount++;          // 包含大小写字母\r\n\r\n      // 4. 最终判断：长度必须 ≥ 8，复杂度至少满足 2 项\r\n      if (strengthCount < 2) {\r\n        const missing = [];\r\n        if (!(hasLetter && hasNumber)) missing.push('包含数字和字母');\r\n        if (!hasSymbol) missing.push('包含特殊符号');\r\n        if (!hasUpperLower) missing.push('包含大小写字母');\r\n        this.passwordError = `密码需至少满足以下两项要求：${missing.join('、')}`;\r\n        return;\r\n      }\r\n\r\n      // 5. 校验确认密码是否一致\r\n      if (this.confirmPassword) {\r\n        this.validateConfirmPassword();\r\n      }\r\n    },\r\n\r\n\r\n    validateConfirmPassword() {\r\n      if (!this.confirmPassword) {\r\n        this.confirmPasswordError = '请确认新密码';\r\n      } else if (this.confirmPassword !== this.newPassword) {\r\n        this.confirmPasswordError = '两次输入的密码不一致';\r\n      } else {\r\n        this.confirmPasswordError = '';\r\n      }\r\n    },\r\n\r\n    async changePassword() {\r\n      if (!this.currentPassword) {\r\n        this.showNotificationMessage('请输入当前密码', 'warning');\r\n        return;\r\n      }\r\n\r\n      this.validateNewPassword();\r\n      this.validateConfirmPassword();\r\n\r\n      if (this.passwordError || this.confirmPasswordError) {\r\n        return;\r\n      }\r\n\r\n      this.isChangingPassword = true;\r\n      try {\r\n        const currentUserInfo = await this.getUserInfo();\r\n        const params = {\r\n          ...currentUserInfo,\r\n          password: this.currentPassword,\r\n          newPassword: this.newPassword,\r\n        };\r\n\r\n\r\n        const res = await postAnyData(\"/logout/cilent/changePwd\", params);\r\n        if (res.data.code === 200) {\r\n          this.showNotificationMessage('密码修改成功', 'success');\r\n          this.showPasswordModal = false;\r\n          this.currentPassword = '';\r\n          this.newPassword = '';\r\n          this.confirmPassword = '';\r\n        } else {\r\n          this.$nextTick(()=>{\r\n            this.showNotificationMessage('原密码输入错误，请检查重试', 'error');\r\n\r\n          })\r\n        }\r\n      } catch (error) {\r\n        this.showNotificationMessage('密码修改过程中出错，请稍后重试', 'error');\r\n      }\r\n      finally {\r\n        this.isChangingPassword = false;\r\n      }\r\n    },\r\n    // 显示通知的方法\r\n    showNotificationMessage(message, type = 'info') {\r\n      this.notificationMessage = message;\r\n      this.notificationType = type;\r\n      this.showNotification = true;\r\n\r\n      // 3秒后自动关闭通知\r\n      setTimeout(() => {\r\n        this.showNotification = false;\r\n      }, 3000);\r\n    },\r\n    // Username related methods\r\n    async changeUsername() {\r\n      if (!this.newUsername.trim()) {\r\n        this.$message.warning('昵称不能为空');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const currentUserInfo = await this.getUserInfo();\r\n\r\n        const params = {\r\n          ...currentUserInfo,\r\n          nickName: this.newUsername\r\n        };\r\n\r\n        const res = await postAnyData(\"/logout/cilent/updateInfo\", params);\r\n        if (res.data.code === 200) {\r\n          this.user.nickName = this.newUsername;\r\n          this.saveUserInfoToCookie({\r\n            ...currentUserInfo,\r\n            nickName: this.newUsername\r\n          });\r\n\r\n          this.showUsernameModal = false;\r\n          this.newUsername = '';\r\n          this.$message.success('昵称修改成功');\r\n        } else {\r\n          this.$message.error(res.data.message || '修改失败');\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('网络错误');\r\n      }\r\n    },\r\n\r\n    // Gender related methods\r\n    async changeGender() {\r\n      if (!this.selectedGender) {\r\n        this.$message.warning('请选择性别');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const currentUserInfo = await this.getUserInfo();\r\n\r\n        const params = {\r\n          ...currentUserInfo,\r\n          sex: this.selectedGender\r\n        };\r\n\r\n        const res = await postAnyData(\"/logout/cilent/updateInfo\", params);\r\n        if (res.data.code === 200) {\r\n          this.user.sex = this.selectedGender;\r\n          this.saveUserInfoToCookie({\r\n            ...currentUserInfo,\r\n            sex: this.selectedGender,\r\n            // isReal : 0\r\n          });\r\n\r\n          this.showGenderModal = false;\r\n          this.$message.success('性别设置成功');\r\n        } else {\r\n          this.$message.error(res.data.message || '修改失败');\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('网络错误');\r\n      }\r\n    },\r\n\r\n    // 姓名脱敏处理（保留姓氏，名字用*代替）\r\n    desensitizeName(name) {\r\n      if (!name) return '';\r\n      if (name.length <= 1) return name;\r\n      return name.charAt(0) + '*'.repeat(name.length - 1);\r\n    },\r\n\r\n    // 身份证号脱敏处理（显示前4位和后4位，中间用*代替）\r\n    desensitizeIdCard(idCard) {\r\n      if (!idCard) return '';\r\n      if (idCard.length <= 8) return idCard;\r\n      return idCard.substring(0, 4) + '********' + idCard.substring(idCard.length - 4);\r\n    },\r\n\r\n    // Phone related methods\r\n    getVerifyCode() {\r\n      if (!this.newPhone) {\r\n        this.$message.warning('请输入手机号');\r\n        return;\r\n      }\r\n\r\n      // 模拟发送验证码\r\n      this.isCountingDown = true;\r\n      this.countdown = 60;\r\n      const timer = setInterval(() => {\r\n        this.countdown--;\r\n        if (this.countdown <= 0) {\r\n          clearInterval(timer);\r\n          this.isCountingDown = false;\r\n        }\r\n      }, 1000);\r\n\r\n      this.$message.success('验证码已发送');\r\n    },\r\n\r\n    async changePhone() {\r\n      if (!this.newPhone) {\r\n        this.$message.warning('请输入新手机号');\r\n        return;\r\n      }\r\n\r\n      if (!this.verifyCode) {\r\n        this.$message.warning('请输入验证码');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const currentUserInfo = await this.getUserInfo();\r\n\r\n        const params = {\r\n          ...currentUserInfo,\r\n          phone: this.newPhone\r\n        };\r\n\r\n        const res = await postAnyData(\"/logout/cilent/updateInfo\", params);\r\n        if (res.data.code === 200) {\r\n          this.user.phone = this.newPhone;\r\n          this.saveUserInfoToCookie({\r\n            ...currentUserInfo,\r\n            phone: this.newPhone\r\n          });\r\n\r\n          this.showPhoneModal = false;\r\n          this.newPhone = '';\r\n          this.verifyCode = '';\r\n          this.$message.success('手机号修改成功');\r\n        } else {\r\n          this.$message.error(res.data.message || '修改失败');\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('网络错误');\r\n      }\r\n    },\r\n\r\n    // ID Verification methods\r\n    validateRealName() {\r\n      if (!this.realName) {\r\n        this.realNameError = '请输入正确的姓名';\r\n        return false;\r\n      }\r\n\r\n      const nameRegex = /^[\\u4e00-\\u9fa5]{2,10}$/;\r\n      if (!nameRegex.test(this.realName)) {\r\n        this.realNameError = '请输入正确的姓名';\r\n        return false;\r\n      }\r\n\r\n      this.realNameError = '';\r\n      return true;\r\n    },\r\n\r\n    validateIdCard() {\r\n      if (!this.idCardNumber) {\r\n        this.idCardError = '请输入正确的身份证号码';\r\n        return false;\r\n      }\r\n\r\n      const idCardRegex = /(^\\d{15$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n      if (!idCardRegex.test(this.idCardNumber)) {\r\n        this.idCardError = '请输入正确的身份证号码';\r\n        return false;\r\n      }\r\n\r\n      this.idCardError = '';\r\n      return true;\r\n    },\r\n\r\n    async submitIdVerification() {\r\n      const isNameValid = this.validateRealName();\r\n      const isIdCardValid = this.validateIdCard();\r\n\r\n      if (!isNameValid || !isIdCardValid || !this.agreementChecked) {\r\n        return;\r\n      }\r\n\r\n        const currentUserInfo = await this.getUserInfo();\r\n\r\n        const verificationData = {\r\n          name: this.realName,\r\n          id: this.idCardNumber,\r\n          username: currentUserInfo.username,\r\n          userId: currentUserInfo.id\r\n        };\r\n\r\n        const res = await postAnyData(\"/idVerification/verify\", verificationData);\r\n        if (res.data.code === 200) {\r\n          this.user.realName = this.realName;\r\n          this.user.realId = this.idCardNumber;\r\n          this.user.isReal = 1;\r\n\r\n          this.saveUserInfoToCookie({\r\n            ...currentUserInfo,\r\n            realName: this.realName,\r\n            realId: this.idCardNumber,\r\n            isReal: 1\r\n          });\r\n\r\n          // this.verificationStatus = res.data.msg;\r\n          this.showNotificationMessage(res.data.msg, 'success');\r\n          // setTimeout(() => {\r\n          //   location.reload(true);\r\n          // }, 3000);\r\n        } else {\r\n          // this.verificationError = res.data.msg || '实名认证失败，请检查信息是否正确';\r\n          this.showNotificationMessage(res.data.msg, 'error');\r\n        }\r\n\r\n    },\r\n\r\n    // Cookie related methods\r\n    getUserInfoFromCookie() {\r\n      const userInfoStr = Cookies.get(this.userInfoCookieName);\r\n      if (userInfoStr) {\r\n        try {\r\n          return JSON.parse(userInfoStr);\r\n        } catch (e) {\r\n          return null;\r\n        }\r\n      }\r\n      return null;\r\n    },\r\n\r\n    saveUserInfoToCookie(userInfo) {\r\n      const infoToSave = {\r\n        ...userInfo,\r\n        timestamp: new Date().getTime()\r\n      };\r\n      Cookies.set(this.userInfoCookieName, JSON.stringify(infoToSave), {\r\n        expires: this.userInfoExpiryDays,\r\n        secure: true,\r\n        sameSite: 'strict'\r\n      });\r\n    },\r\n\r\n    isCacheValid(cachedData) {\r\n      if (!cachedData || !cachedData.timestamp) return false;\r\n      const oneDay = 24 * 60 * 60 * 1000; // 1 day in milliseconds\r\n      return (new Date().getTime() - cachedData.timestamp) < oneDay;\r\n    },\r\n\r\n    async fetchUserInfo() {\r\n      const cachedUserInfo = this.getUserInfoFromCookie();\r\n\r\n      if (cachedUserInfo && this.isCacheValid(cachedUserInfo)) {\r\n        this.updateLocalUserData(cachedUserInfo);\r\n        return;\r\n      }\r\n\r\n      await this.getUserInfo();\r\n    },\r\n\r\n    updateLocalUserData(userData) {\r\n      this.user = {\r\n        id: userData.id || this.user.id,\r\n        username: userData.username || this.user.username,\r\n        nickName: userData.nickName || this.user.nickName,\r\n        password: this.currentPassword || this.user.password,\r\n        avatarUrl: userData.avatarUrl || this.user.avatarUrl,\r\n        sex: userData.sex || this.user.sex,\r\n        phone: userData.phone || this.user.phone,\r\n        email: userData.email || this.user.email,\r\n        balance: userData.balance || this.user.balance,\r\n        isReal: userData.isReal !== undefined ? userData.isReal : this.user.isReal,\r\n        realName: userData.realName || this.user.realName,\r\n        realId: userData.realId || this.user.realId,\r\n        realNumber: userData.realNumber || this.user.realNumber,\r\n        tenantId: userData.tenantId || this.user.tenantId,\r\n        isLogin: userData.isLogin || this.user.isLogin\r\n      };\r\n      // 根据isReal更新认证状态\r\n      this.verificationStatus = this.user.isReal === 1 ? '已认证' : '未认证';\r\n    }\r\n  },\r\n  watch: {\r\n    realName() {\r\n      this.validateRealName();\r\n    },\r\n    idCardNumber() {\r\n      this.validateIdCard();\r\n    },\r\n    newPassword() {\r\n      this.validateNewPassword();\r\n    },\r\n    confirmPassword() {\r\n      this.validateConfirmPassword();\r\n    }\r\n  },\r\n  async created() {\r\n    this.user = {\r\n      username: '',\r\n      phone: ''\r\n    };\r\n\r\n    try {\r\n      await this.fetchUserInfo();\r\n      this.selectedGender = this.user.sex;\r\n\r\n      // 添加这部分代码，从URL参数中读取activeTab\r\n      if (this.$route.query.activeTab === 'verification') {\r\n        this.activeTab = 'verification';\r\n      }\r\n    } catch (error) {\r\n      // console.error(error);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* Main Layout */\r\n.personal-center {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n  width: 100%;\r\n}\r\n\r\n.content-wrapper {\r\n  display: flex;\r\n  width: 100%;\r\n  min-height: calc(100vh - 64px);\r\n}\r\n\r\n/* Left Navigation Styles */\r\n.left-navigation {\r\n  min-width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #eee;\r\n}\r\n\r\n.center-title {\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #303133;\r\n  padding: 20px 16px;\r\n  margin: 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.nav-menu {\r\n  width: 100%;\r\n}\r\n\r\n.nav-item1 {\r\n  padding: 14px 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.nav-item1 a {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #103680;\r\n  text-decoration: none;\r\n}\r\n\r\n.nav-item1 i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.nav-item1.active {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.nav-item1.active a {\r\n  color: #409eff !important;\r\n}\r\n\r\n.nav-item1:hover {\r\n  background-color: #f5f7fa !important;\r\n}\r\n\r\n/* Main Container Styles */\r\n.main-container {\r\n  flex: 1;\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.tab-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n}\r\n\r\n.section-header h2 {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin: 0;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.verification-error {\r\n  color: #f56c6c;\r\n  font-size: 14px;\r\n  margin-top: 10px;\r\n}\r\n\r\n/* User Info Container */\r\n.user-info-container {\r\n  display: grid;\r\n  grid-template-columns: 1fr 2fr;\r\n  gap: 20px;\r\n}\r\n\r\n/* Card Styles */\r\n.profile-card, .login-card {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  padding: 20px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-top: 0;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n/* User Avatar Section */\r\n.user-avatar-section {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.avatar {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  background-color: #f2f0ff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n  overflow: hidden;\r\n}\r\n\r\n.avatar-img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.avatar-text {\r\n  font-size: 36px;\r\n  color: #1890ff;\r\n}\r\n\r\n.username-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.username {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-info-item {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.info-label {\r\n  background-color: #f0f0f0;\r\n  padding: 2px 5px;\r\n  border-radius: 4px;\r\n  margin-right: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.edit-icon {\r\n  margin-left: 5px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n/* Badge Styles */\r\n.verification-badge {\r\n  margin-top: 10px;\r\n}\r\n\r\n.badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  background-color: #e6f7ff;\r\n  color: #1890ff;\r\n  padding: 5px 10px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n}\r\n\r\n.check-icon {\r\n  display: inline-block;\r\n  width: 12px;\r\n  height: 12px;\r\n  background-color: #1890ff;\r\n  margin-right: 4px;\r\n  border-radius: 50%;\r\n}\r\n\r\n/* Verified Info */\r\n.verified-info {\r\n  padding: 20px;\r\n  background-color: #f6ffed;\r\n  border: 1px solid #b7eb8f;\r\n  border-radius: 4px;\r\n}\r\n\r\n.verified-item {\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.verified-label {\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.verified-status {\r\n  color: #52c41a;\r\n  font-weight: 500;\r\n  margin-top: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.verified-status i {\r\n  margin-right: 5px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* Login Info Styles */\r\n.login-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-subtitle {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin: 15px 0;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.login-item {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.login-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.login-content {\r\n  margin-top: 5px;\r\n}\r\n\r\n.login-description {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.login-value {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n/* Verification Form */\r\n.verification-container {\r\n  max-width: 300px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.verification-form {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.form-group input {\r\n  width: 100%;\r\n  padding: 10px 12px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.form-group input:focus {\r\n  border-color: #1890ff;\r\n  outline: none;\r\n}\r\n\r\n.verified-info {\r\n  padding: 20px;\r\n  background-color: #f6ffed;\r\n  border: 1px solid #b7eb8f;\r\n  border-radius: 4px;\r\n}\r\n\r\n.verified-item {\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.verified-label {\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.verified-status {\r\n  color: #52c41a;\r\n  font-weight: 500;\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.verified-status i {\r\n  margin-right: 5px;\r\n  font-size: 16px;\r\n}\r\n\r\n.error-input {\r\n  border-color: #f5222d !important;\r\n}\r\n\r\n.error-text {\r\n  color: #f5222d;\r\n  font-size: 12px;\r\n  margin-top: 5px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.error-icon {\r\n  display: inline-block;\r\n  width: 14px;\r\n  height: 14px;\r\n  background-color: #f5222d;\r\n  border-radius: 50%;\r\n  margin-right: 5px;\r\n}\r\n\r\n.agreement-checkbox {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.agreement-checkbox input {\r\n  margin-right: 8px;\r\n  margin-top: 3px;\r\n}\r\n\r\n.agreement-checkbox label {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.5;\r\n}\r\n\r\n.link {\r\n  color: #1890ff;\r\n  text-decoration: none;\r\n}\r\n\r\n.submit-btn {\r\n  width: 100%;\r\n  padding: 12px;\r\n  background-color: #1890ff;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.submit-btn:hover {\r\n  background-color: #40a9ff;\r\n}\r\n\r\n.submit-btn:disabled {\r\n  background-color: #d9d9d9;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Button Styles */\r\n.edit-btn {\r\n  color: #1890ff;\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n/* Modal Styles */\r\n.modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  width: 400px;\r\n  max-width: 90%;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.modal-header {\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.modal-header h3 {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin: 0;\r\n}\r\n\r\n.close-btn {\r\n  font-size: 24px;\r\n  color: #999;\r\n  cursor: pointer;\r\n}\r\n\r\n.modal-body {\r\n  padding: 20px;\r\n}\r\n\r\n.modal-footer {\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.cancel-btn, .confirm-btn {\r\n  padding: 8px 15px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n}\r\n\r\n.cancel-btn {\r\n  background-color: #f7f7f7;\r\n  border: 1px solid #ddd;\r\n  color: #666;\r\n  margin-right: 10px;\r\n}\r\n\r\n.confirm-btn {\r\n  background-color: #1890ff;\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n/* Gender Options */\r\n.gender-options {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n}\r\n\r\n.gender-option {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.gender-option input {\r\n  margin-right: 10px;\r\n}\r\n\r\n/* Phone Verification Code Input */\r\n.verify-code-input {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.verify-code-input input {\r\n  flex: 1;\r\n}\r\n\r\n.get-code-btn {\r\n  padding: 0 15px;\r\n  background-color: #1890ff;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  white-space: nowrap;\r\n}\r\n\r\n.get-code-btn:disabled {\r\n  background-color: #d9d9d9;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Responsive Styles */\r\n@media (max-width: 768px) {\r\n  .content-wrapper {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .left-navigation {\r\n    width: 100%;\r\n    min-height: auto;\r\n  }\r\n\r\n  .user-info-container {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .modal-content {\r\n    width: 90%;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .user-avatar-section {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .avatar {\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .verify-code-input {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .get-code-btn {\r\n    padding: 10px;\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AAwUA,OAAAA,MAAA;AACA,SAAAC,UAAA,EAAAC,WAAA;AACA,OAAAC,OAAA;AACA,OAAAC,iBAAA;AAGA;EACAC,IAAA;EACAC,UAAA;IAAAN,MAAA;IAAAI;EAAA;EACAG,KAAA;IACA;MACAC,SAAA;MACAC,IAAA;QACAC,EAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,kBAAA;QAEAC,GAAA;QACAC,KAAA;QACAC,KAAA;QACAC,OAAA;QACAC,MAAA;QACAC,QAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,OAAA;QACAC,mBAAA;QACAC,gBAAA;MACA;MACAC,gBAAA;MACA;MACAC,iBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,eAAA;MACAC,aAAA;MACAC,oBAAA;MAEA;MACAC,iBAAA;MACAC,WAAA;MAEA;MACAC,eAAA;MACAC,cAAA;MAEA;MACAC,cAAA;MACAC,QAAA;MACAC,UAAA;MACAC,SAAA;MACAC,cAAA;MAEA;MACAtB,QAAA;MACAuB,YAAA;MACAC,aAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,kBAAA;MACAC,UAAA;MAEA;MACAC,kBAAA;MACAC,kBAAA;IACA;EACA;EACAC,QAAA;IACAC,sBAAA;MACA,YAAAjC,QAAA,IACA,KAAAuB,YAAA,IACA,MAAAC,aAAA,IACA,MAAAC,WAAA,IACA,KAAAE,gBAAA;IACA;EACA;EACAO,OAAA;IAEAC,UAAAC,GAAA;MACA,KAAAjD,SAAA,GAAAiD,GAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,KAAA;UAAA,QAAAC,MAAA,CAAAD,KAAA;UAAApD,SAAA,EAAAiD;QAAA;MAAA;IACA;IACAK,YAAA;MACA,YAAArD,IAAA,CAAAG,QAAA,SAAAH,IAAA,CAAAG,QAAA,CAAAmD,MAAA,OACA,KAAAtD,IAAA,CAAAG,QAAA,CAAAoD,MAAA,IAAAC,WAAA,KACA;IACA;IAEAC,mBAAA;MACA,SAAAzD,IAAA,CAAAW,MAAA;QACA,KAAA+C,QAAA,CAAAC,IAAA;QACA;MACA;MACA,KAAA5D,SAAA;IACA;IAEA;IACA,MAAA6D,YAAA;MACA;QACA,MAAAC,GAAA,SAAApE,WAAA;QACA,IAAAoE,GAAA,CAAA/D,IAAA,CAAAgE,IAAA;UACA,MAAAC,QAAA,GAAAF,GAAA,CAAA/D,IAAA,CAAAA,IAAA;UACA,KAAAE,IAAA;YACA,QAAAA,IAAA;YACA,GAAA+D;UACA;UACA;UACA,KAAAvB,kBAAA,GAAAuB,QAAA,CAAApD,MAAA;UACA,KAAAqD,oBAAA,CAAAD,QAAA;UACA,OAAAA,QAAA;QACA;UACA,UAAAE,KAAA,CAAAJ,GAAA,CAAA/D,IAAA,CAAAoE,GAAA;QACA;MACA,SAAAC,KAAA;QACA,MAAAA,KAAA;MACA;IACA;IAEAC,oBAAA;MACA,MAAA9C,WAAA,QAAAA,WAAA,EAAA+C,IAAA;MACA,KAAA7C,aAAA;;MAEA;MACA,KAAAF,WAAA;QACA,KAAAE,aAAA;QACA;MACA;;MAEA;MACA,MAAA8C,YAAA,GAAAhD,WAAA,CAAAgC,MAAA;MACA,KAAAgB,YAAA;QACA,KAAA9C,aAAA;QACA;MACA;;MAEA;MACA,MAAA+C,SAAA,cAAAC,IAAA,CAAAlD,WAAA;MACA,MAAAmD,SAAA,WAAAD,IAAA,CAAAlD,WAAA;MACA,MAAAoD,SAAA,2CAAAF,IAAA,CAAAlD,WAAA;MACA,MAAAqD,aAAA,WAAAH,IAAA,CAAAlD,WAAA,aAAAkD,IAAA,CAAAlD,WAAA;MAEA,IAAAsD,aAAA;MACA,IAAAL,SAAA,IAAAE,SAAA,EAAAG,aAAA;MACA,IAAAF,SAAA,EAAAE,aAAA;MACA,IAAAD,aAAA,EAAAC,aAAA;;MAEA;MACA,IAAAA,aAAA;QACA,MAAAC,OAAA;QACA,MAAAN,SAAA,IAAAE,SAAA,GAAAI,OAAA,CAAA3B,IAAA;QACA,KAAAwB,SAAA,EAAAG,OAAA,CAAA3B,IAAA;QACA,KAAAyB,aAAA,EAAAE,OAAA,CAAA3B,IAAA;QACA,KAAA1B,aAAA,oBAAAqD,OAAA,CAAAC,IAAA;QACA;MACA;;MAEA;MACA,SAAAvD,eAAA;QACA,KAAAwD,uBAAA;MACA;IACA;IAGAA,wBAAA;MACA,UAAAxD,eAAA;QACA,KAAAE,oBAAA;MACA,gBAAAF,eAAA,UAAAD,WAAA;QACA,KAAAG,oBAAA;MACA;QACA,KAAAA,oBAAA;MACA;IACA;IAEA,MAAAuD,eAAA;MACA,UAAA3D,eAAA;QACA,KAAA4D,uBAAA;QACA;MACA;MAEA,KAAAb,mBAAA;MACA,KAAAW,uBAAA;MAEA,SAAAvD,aAAA,SAAAC,oBAAA;QACA;MACA;MAEA,KAAAnB,kBAAA;MACA;QACA,MAAA4E,eAAA,cAAAtB,WAAA;QACA,MAAAuB,MAAA;UACA,GAAAD,eAAA;UACA9E,QAAA,OAAAiB,eAAA;UACAC,WAAA,OAAAA;QACA;QAGA,MAAAuC,GAAA,SAAApE,WAAA,6BAAA0F,MAAA;QACA,IAAAtB,GAAA,CAAA/D,IAAA,CAAAgE,IAAA;UACA,KAAAmB,uBAAA;UACA,KAAA7D,iBAAA;UACA,KAAAC,eAAA;UACA,KAAAC,WAAA;UACA,KAAAC,eAAA;QACA;UACA,KAAA6D,SAAA;YACA,KAAAH,uBAAA;UAEA;QACA;MACA,SAAAd,KAAA;QACA,KAAAc,uBAAA;MACA,UACA;QACA,KAAA3E,kBAAA;MACA;IACA;IACA;IACA2E,wBAAAI,OAAA,EAAAC,IAAA;MACA,KAAArE,mBAAA,GAAAoE,OAAA;MACA,KAAAnE,gBAAA,GAAAoE,IAAA;MACA,KAAAnE,gBAAA;;MAEA;MACAoE,UAAA;QACA,KAAApE,gBAAA;MACA;IACA;IACA;IACA,MAAAqE,eAAA;MACA,UAAA7D,WAAA,CAAA0C,IAAA;QACA,KAAAX,QAAA,CAAA+B,OAAA;QACA;MACA;MAEA;QACA,MAAAP,eAAA,cAAAtB,WAAA;QAEA,MAAAuB,MAAA;UACA,GAAAD,eAAA;UACA/E,QAAA,OAAAwB;QACA;QAEA,MAAAkC,GAAA,SAAApE,WAAA,8BAAA0F,MAAA;QACA,IAAAtB,GAAA,CAAA/D,IAAA,CAAAgE,IAAA;UACA,KAAA9D,IAAA,CAAAG,QAAA,QAAAwB,WAAA;UACA,KAAAqC,oBAAA;YACA,GAAAkB,eAAA;YACA/E,QAAA,OAAAwB;UACA;UAEA,KAAAD,iBAAA;UACA,KAAAC,WAAA;UACA,KAAA+B,QAAA,CAAAgC,OAAA;QACA;UACA,KAAAhC,QAAA,CAAAS,KAAA,CAAAN,GAAA,CAAA/D,IAAA,CAAAuF,OAAA;QACA;MACA,SAAAlB,KAAA;QACA,KAAAT,QAAA,CAAAS,KAAA;MACA;IACA;IAEA;IACA,MAAAwB,aAAA;MACA,UAAA9D,cAAA;QACA,KAAA6B,QAAA,CAAA+B,OAAA;QACA;MACA;MAEA;QACA,MAAAP,eAAA,cAAAtB,WAAA;QAEA,MAAAuB,MAAA;UACA,GAAAD,eAAA;UACA3E,GAAA,OAAAsB;QACA;QAEA,MAAAgC,GAAA,SAAApE,WAAA,8BAAA0F,MAAA;QACA,IAAAtB,GAAA,CAAA/D,IAAA,CAAAgE,IAAA;UACA,KAAA9D,IAAA,CAAAO,GAAA,QAAAsB,cAAA;UACA,KAAAmC,oBAAA;YACA,GAAAkB,eAAA;YACA3E,GAAA,OAAAsB;YACA;UACA;;UAEA,KAAAD,eAAA;UACA,KAAA8B,QAAA,CAAAgC,OAAA;QACA;UACA,KAAAhC,QAAA,CAAAS,KAAA,CAAAN,GAAA,CAAA/D,IAAA,CAAAuF,OAAA;QACA;MACA,SAAAlB,KAAA;QACA,KAAAT,QAAA,CAAAS,KAAA;MACA;IACA;IAEA;IACAyB,gBAAAhG,IAAA;MACA,KAAAA,IAAA;MACA,IAAAA,IAAA,CAAA0D,MAAA,cAAA1D,IAAA;MACA,OAAAA,IAAA,CAAA2D,MAAA,UAAAsC,MAAA,CAAAjG,IAAA,CAAA0D,MAAA;IACA;IAEA;IACAwC,kBAAAC,MAAA;MACA,KAAAA,MAAA;MACA,IAAAA,MAAA,CAAAzC,MAAA,cAAAyC,MAAA;MACA,OAAAA,MAAA,CAAAC,SAAA,sBAAAD,MAAA,CAAAC,SAAA,CAAAD,MAAA,CAAAzC,MAAA;IACA;IAEA;IACA2C,cAAA;MACA,UAAAlE,QAAA;QACA,KAAA2B,QAAA,CAAA+B,OAAA;QACA;MACA;;MAEA;MACA,KAAAvD,cAAA;MACA,KAAAD,SAAA;MACA,MAAAiE,KAAA,GAAAC,WAAA;QACA,KAAAlE,SAAA;QACA,SAAAA,SAAA;UACAmE,aAAA,CAAAF,KAAA;UACA,KAAAhE,cAAA;QACA;MACA;MAEA,KAAAwB,QAAA,CAAAgC,OAAA;IACA;IAEA,MAAAW,YAAA;MACA,UAAAtE,QAAA;QACA,KAAA2B,QAAA,CAAA+B,OAAA;QACA;MACA;MAEA,UAAAzD,UAAA;QACA,KAAA0B,QAAA,CAAA+B,OAAA;QACA;MACA;MAEA;QACA,MAAAP,eAAA,cAAAtB,WAAA;QAEA,MAAAuB,MAAA;UACA,GAAAD,eAAA;UACA1E,KAAA,OAAAuB;QACA;QAEA,MAAA8B,GAAA,SAAApE,WAAA,8BAAA0F,MAAA;QACA,IAAAtB,GAAA,CAAA/D,IAAA,CAAAgE,IAAA;UACA,KAAA9D,IAAA,CAAAQ,KAAA,QAAAuB,QAAA;UACA,KAAAiC,oBAAA;YACA,GAAAkB,eAAA;YACA1E,KAAA,OAAAuB;UACA;UAEA,KAAAD,cAAA;UACA,KAAAC,QAAA;UACA,KAAAC,UAAA;UACA,KAAA0B,QAAA,CAAAgC,OAAA;QACA;UACA,KAAAhC,QAAA,CAAAS,KAAA,CAAAN,GAAA,CAAA/D,IAAA,CAAAuF,OAAA;QACA;MACA,SAAAlB,KAAA;QACA,KAAAT,QAAA,CAAAS,KAAA;MACA;IACA;IAEA;IACAmC,iBAAA;MACA,UAAA1F,QAAA;QACA,KAAAwB,aAAA;QACA;MACA;MAEA,MAAAmE,SAAA;MACA,KAAAA,SAAA,CAAA/B,IAAA,MAAA5D,QAAA;QACA,KAAAwB,aAAA;QACA;MACA;MAEA,KAAAA,aAAA;MACA;IACA;IAEAoE,eAAA;MACA,UAAArE,YAAA;QACA,KAAAE,WAAA;QACA;MACA;MAEA,MAAAoE,WAAA;MACA,KAAAA,WAAA,CAAAjC,IAAA,MAAArC,YAAA;QACA,KAAAE,WAAA;QACA;MACA;MAEA,KAAAA,WAAA;MACA;IACA;IAEA,MAAAqE,qBAAA;MACA,MAAAC,WAAA,QAAAL,gBAAA;MACA,MAAAM,aAAA,QAAAJ,cAAA;MAEA,KAAAG,WAAA,KAAAC,aAAA,UAAArE,gBAAA;QACA;MACA;MAEA,MAAA2C,eAAA,cAAAtB,WAAA;MAEA,MAAAiD,gBAAA;QACAjH,IAAA,OAAAgB,QAAA;QACAX,EAAA,OAAAkC,YAAA;QACAjC,QAAA,EAAAgF,eAAA,CAAAhF,QAAA;QACA4G,MAAA,EAAA5B,eAAA,CAAAjF;MACA;MAEA,MAAA4D,GAAA,SAAApE,WAAA,2BAAAoH,gBAAA;MACA,IAAAhD,GAAA,CAAA/D,IAAA,CAAAgE,IAAA;QACA,KAAA9D,IAAA,CAAAY,QAAA,QAAAA,QAAA;QACA,KAAAZ,IAAA,CAAAa,MAAA,QAAAsB,YAAA;QACA,KAAAnC,IAAA,CAAAW,MAAA;QAEA,KAAAqD,oBAAA;UACA,GAAAkB,eAAA;UACAtE,QAAA,OAAAA,QAAA;UACAC,MAAA,OAAAsB,YAAA;UACAxB,MAAA;QACA;;QAEA;QACA,KAAAsE,uBAAA,CAAApB,GAAA,CAAA/D,IAAA,CAAAoE,GAAA;QACA;QACA;QACA;MACA;QACA;QACA,KAAAe,uBAAA,CAAApB,GAAA,CAAA/D,IAAA,CAAAoE,GAAA;MACA;IAEA;IAEA;IACA6C,sBAAA;MACA,MAAAC,WAAA,GAAAtH,OAAA,CAAAuH,GAAA,MAAAvE,kBAAA;MACA,IAAAsE,WAAA;QACA;UACA,OAAAE,IAAA,CAAAC,KAAA,CAAAH,WAAA;QACA,SAAAI,CAAA;UACA;QACA;MACA;MACA;IACA;IAEApD,qBAAAqD,QAAA;MACA,MAAAC,UAAA;QACA,GAAAD,QAAA;QACAE,SAAA,MAAAC,IAAA,GAAAC,OAAA;MACA;MACA/H,OAAA,CAAAgI,GAAA,MAAAhF,kBAAA,EAAAwE,IAAA,CAAAS,SAAA,CAAAL,UAAA;QACAM,OAAA,OAAAjF,kBAAA;QACAkF,MAAA;QACAC,QAAA;MACA;IACA;IAEAC,aAAAC,UAAA;MACA,KAAAA,UAAA,KAAAA,UAAA,CAAAT,SAAA;MACA,MAAAU,MAAA;MACA,WAAAT,IAAA,GAAAC,OAAA,KAAAO,UAAA,CAAAT,SAAA,GAAAU,MAAA;IACA;IAEA,MAAAC,cAAA;MACA,MAAAC,cAAA,QAAApB,qBAAA;MAEA,IAAAoB,cAAA,SAAAJ,YAAA,CAAAI,cAAA;QACA,KAAAC,mBAAA,CAAAD,cAAA;QACA;MACA;MAEA,WAAAvE,WAAA;IACA;IAEAwE,oBAAArE,QAAA;MACA,KAAA/D,IAAA;QACAC,EAAA,EAAA8D,QAAA,CAAA9D,EAAA,SAAAD,IAAA,CAAAC,EAAA;QACAC,QAAA,EAAA6D,QAAA,CAAA7D,QAAA,SAAAF,IAAA,CAAAE,QAAA;QACAC,QAAA,EAAA4D,QAAA,CAAA5D,QAAA,SAAAH,IAAA,CAAAG,QAAA;QACAC,QAAA,OAAAiB,eAAA,SAAArB,IAAA,CAAAI,QAAA;QACAC,SAAA,EAAA0D,QAAA,CAAA1D,SAAA,SAAAL,IAAA,CAAAK,SAAA;QACAE,GAAA,EAAAwD,QAAA,CAAAxD,GAAA,SAAAP,IAAA,CAAAO,GAAA;QACAC,KAAA,EAAAuD,QAAA,CAAAvD,KAAA,SAAAR,IAAA,CAAAQ,KAAA;QACAC,KAAA,EAAAsD,QAAA,CAAAtD,KAAA,SAAAT,IAAA,CAAAS,KAAA;QACAC,OAAA,EAAAqD,QAAA,CAAArD,OAAA,SAAAV,IAAA,CAAAU,OAAA;QACAC,MAAA,EAAAoD,QAAA,CAAApD,MAAA,KAAA0H,SAAA,GAAAtE,QAAA,CAAApD,MAAA,QAAAX,IAAA,CAAAW,MAAA;QACAC,QAAA,EAAAmD,QAAA,CAAAnD,QAAA,SAAAZ,IAAA,CAAAY,QAAA;QACAC,MAAA,EAAAkD,QAAA,CAAAlD,MAAA,SAAAb,IAAA,CAAAa,MAAA;QACAC,UAAA,EAAAiD,QAAA,CAAAjD,UAAA,SAAAd,IAAA,CAAAc,UAAA;QACAC,QAAA,EAAAgD,QAAA,CAAAhD,QAAA,SAAAf,IAAA,CAAAe,QAAA;QACAC,OAAA,EAAA+C,QAAA,CAAA/C,OAAA,SAAAhB,IAAA,CAAAgB;MACA;MACA;MACA,KAAAwB,kBAAA,QAAAxC,IAAA,CAAAW,MAAA;IACA;EACA;EACA2H,KAAA;IACA1H,SAAA;MACA,KAAA0F,gBAAA;IACA;IACAnE,aAAA;MACA,KAAAqE,cAAA;IACA;IACAlF,YAAA;MACA,KAAA8C,mBAAA;IACA;IACA7C,gBAAA;MACA,KAAAwD,uBAAA;IACA;EACA;EACA,MAAAwD,QAAA;IACA,KAAAvI,IAAA;MACAE,QAAA;MACAM,KAAA;IACA;IAEA;MACA,WAAA0H,aAAA;MACA,KAAArG,cAAA,QAAA7B,IAAA,CAAAO,GAAA;;MAEA;MACA,SAAA6C,MAAA,CAAAD,KAAA,CAAApD,SAAA;QACA,KAAAA,SAAA;MACA;IACA,SAAAoE,KAAA;MACA;IAAA;EAEA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}