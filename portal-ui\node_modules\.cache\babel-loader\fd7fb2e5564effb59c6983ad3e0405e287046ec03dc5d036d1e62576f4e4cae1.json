{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"doc-content\"\n  }, [_vm.loading ? _c(\"div\", {\n    staticClass: \"loading\"\n  }, [_vm._v(\"文档加载中...\")]) : _vm.error ? _c(\"div\", {\n    staticClass: \"error\"\n  }, [_vm._v(\"文档加载失败: \" + _vm._s(_vm.error))]) : _c(\"div\", [_c(\"div\", {\n    ref: \"contentRef\",\n    domProps: {\n      innerHTML: _vm._s(_vm.markdownContent)\n    }\n  }), _c(\"div\", {\n    staticClass: \"page-navigation\"\n  }, [_c(\"div\", {\n    staticClass: \"prev-next-container\"\n  }, [_vm.prevPage ? _c(\"router-link\", {\n    staticClass: \"prev-page\",\n    attrs: {\n      to: _vm.prevPage.path\n    }\n  }, [_c(\"div\", {\n    staticClass: \"nav-label\"\n  }, [_vm._v(\"上一篇\")]), _c(\"div\", {\n    staticClass: \"nav-title\"\n  }, [_vm._v(_vm._s(_vm.prevPage.name))])]) : _c(\"div\", {\n    staticClass: \"prev-page empty\"\n  }), _vm.nextPage ? _c(\"router-link\", {\n    staticClass: \"next-page\",\n    attrs: {\n      to: _vm.nextPage.path\n    }\n  }, [_c(\"div\", {\n    staticClass: \"nav-label\"\n  }, [_vm._v(\"下一篇\")]), _c(\"div\", {\n    staticClass: \"nav-title\"\n  }, [_vm._v(_vm._s(_vm.nextPage.name))])]) : _c(\"div\", {\n    staticClass: \"next-page empty\"\n  })], 1)])])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "loading", "_v", "error", "_s", "ref", "domProps", "innerHTML", "markdownContent", "prevPage", "attrs", "to", "path", "name", "nextPage", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/views/HelpContent.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"doc-content\" }, [\n    _vm.loading\n      ? _c(\"div\", { staticClass: \"loading\" }, [_vm._v(\"文档加载中...\")])\n      : _vm.error\n      ? _c(\"div\", { staticClass: \"error\" }, [\n          _vm._v(\"文档加载失败: \" + _vm._s(_vm.error)),\n        ])\n      : _c(\"div\", [\n          _c(\"div\", {\n            ref: \"contentRef\",\n            domProps: { innerHTML: _vm._s(_vm.markdownContent) },\n          }),\n          _c(\"div\", { staticClass: \"page-navigation\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"prev-next-container\" },\n              [\n                _vm.prevPage\n                  ? _c(\n                      \"router-link\",\n                      {\n                        staticClass: \"prev-page\",\n                        attrs: { to: _vm.prevPage.path },\n                      },\n                      [\n                        _c(\"div\", { staticClass: \"nav-label\" }, [\n                          _vm._v(\"上一篇\"),\n                        ]),\n                        _c(\"div\", { staticClass: \"nav-title\" }, [\n                          _vm._v(_vm._s(_vm.prevPage.name)),\n                        ]),\n                      ]\n                    )\n                  : _c(\"div\", { staticClass: \"prev-page empty\" }),\n                _vm.nextPage\n                  ? _c(\n                      \"router-link\",\n                      {\n                        staticClass: \"next-page\",\n                        attrs: { to: _vm.nextPage.path },\n                      },\n                      [\n                        _c(\"div\", { staticClass: \"nav-label\" }, [\n                          _vm._v(\"下一篇\"),\n                        ]),\n                        _c(\"div\", { staticClass: \"nav-title\" }, [\n                          _vm._v(_vm._s(_vm.nextPage.name)),\n                        ]),\n                      ]\n                    )\n                  : _c(\"div\", { staticClass: \"next-page empty\" }),\n              ],\n              1\n            ),\n          ]),\n        ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC/CH,GAAG,CAACI,OAAO,GACPH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CAACH,GAAG,CAACK,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,GAC3DL,GAAG,CAACM,KAAK,GACTL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCH,GAAG,CAACK,EAAE,CAAC,UAAU,GAAGL,GAAG,CAACO,EAAE,CAACP,GAAG,CAACM,KAAK,CAAC,CAAC,CACvC,CAAC,GACFL,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,KAAK,EAAE;IACRO,GAAG,EAAE,YAAY;IACjBC,QAAQ,EAAE;MAAEC,SAAS,EAAEV,GAAG,CAACO,EAAE,CAACP,GAAG,CAACW,eAAe;IAAE;EACrD,CAAC,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtC,CACEH,GAAG,CAACY,QAAQ,GACRX,EAAE,CACA,aAAa,EACb;IACEE,WAAW,EAAE,WAAW;IACxBU,KAAK,EAAE;MAAEC,EAAE,EAAEd,GAAG,CAACY,QAAQ,CAACG;IAAK;EACjC,CAAC,EACD,CACEd,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAACK,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,EACFJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAACK,EAAE,CAACL,GAAG,CAACO,EAAE,CAACP,GAAG,CAACY,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC,CAAC,CACH,CACF,GACDf,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,CAAC,EACjDH,GAAG,CAACiB,QAAQ,GACRhB,EAAE,CACA,aAAa,EACb;IACEE,WAAW,EAAE,WAAW;IACxBU,KAAK,EAAE;MAAEC,EAAE,EAAEd,GAAG,CAACiB,QAAQ,CAACF;IAAK;EACjC,CAAC,EACD,CACEd,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAACK,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,EACFJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAACK,EAAE,CAACL,GAAG,CAACO,EAAE,CAACP,GAAG,CAACiB,QAAQ,CAACD,IAAI,CAAC,CAAC,CAClC,CAAC,CACH,CACF,GACDf,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,CAAC,CAClD,EACD,CAAC,CACF,CACF,CAAC,CACH,CAAC,CACP,CAAC;AACJ,CAAC;AACD,IAAIe,eAAe,GAAG,EAAE;AACxBnB,MAAM,CAACoB,aAAa,GAAG,IAAI;AAE3B,SAASpB,MAAM,EAAEmB,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}