{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"Layout\", [_c(\"div\", {\n    staticClass: \"about-banner\"\n  }, [_c(\"div\", {\n    staticClass: \"about-banner-bg\",\n    staticStyle: {\n      background: \"url('/images/back3.webp') center/cover\",\n      opacity: \"0.2\",\n      \"z-index\": \"1\",\n      position: \"absolute\",\n      top: \"0\",\n      left: \"0\",\n      right: \"0\",\n      bottom: \"0\"\n    }\n  }), _c(\"div\", {\n    staticClass: \"grid-background\"\n  }), _c(\"div\", {\n    staticClass: \"particles-container\"\n  }, _vm._l(50, function (n) {\n    return _c(\"div\", {\n      key: n,\n      staticClass: \"particle\",\n      style: _vm.getParticleStyle()\n    });\n  }), 0), _c(\"div\", {\n    staticClass: \"data-streams\"\n  }, _vm._l(8, function (n) {\n    return _c(\"div\", {\n      key: n,\n      staticClass: \"data-stream\"\n    });\n  }), 0), _c(\"div\", {\n    staticClass: \"network-nodes\"\n  }, [_vm._l(12, function (n) {\n    return _c(\"div\", {\n      key: n,\n      staticClass: \"node\",\n      style: _vm.getNodeStyle()\n    }, [_c(\"div\", {\n      staticClass: \"node-pulse\"\n    })]);\n  }), _c(\"svg\", {\n    staticClass: \"connection-lines\",\n    attrs: {\n      viewBox: \"0 0 100 100\"\n    }\n  }, _vm._l(_vm.connectionLines, function (line) {\n    return _c(\"line\", {\n      key: line.id,\n      staticClass: \"connection-line\",\n      attrs: {\n        x1: line.x1,\n        y1: line.y1,\n        x2: line.x2,\n        y2: line.y2\n      }\n    });\n  }), 0)], 2), _c(\"div\", {\n    staticClass: \"light-effects\"\n  }, [_c(\"div\", {\n    staticClass: \"light-beam light-beam-1\"\n  }), _c(\"div\", {\n    staticClass: \"light-beam light-beam-2\"\n  }), _c(\"div\", {\n    staticClass: \"light-beam light-beam-3\"\n  })]), _c(\"div\", {\n    staticClass: \"banner-content\"\n  }, [_c(\"h1\", {\n    staticClass: \"banner-title\"\n  }, [_c(\"span\", {\n    staticClass: \"title-word\",\n    attrs: {\n      \"data-text\": \"重塑算力想象\"\n    }\n  }, [_vm._v(\"重塑算力想象\")]), _c(\"span\", {\n    staticClass: \"title-separator\"\n  }, [_vm._v(\"，\")]), _c(\"span\", {\n    staticClass: \"title-word\",\n    attrs: {\n      \"data-text\": \"让智能无处不在\"\n    }\n  }, [_vm._v(\"让智能无处不在\")])]), _c(\"div\", {\n    staticClass: \"banner-subtitle-container\"\n  }, [_c(\"p\", {\n    staticClass: \"banner-subtitle typing-effect\"\n  }, [_vm._v(\"我们相信\")]), _c(\"p\", {\n    staticClass: \"banner-subtitle typing-effect\",\n    staticStyle: {\n      \"animation-delay\": \"1s\"\n    }\n  }, [_vm._v(\"人类无需再围成一台机器\")]), _c(\"p\", {\n    staticClass: \"banner-subtitle typing-effect\",\n    staticStyle: {\n      \"animation-delay\": \"2s\"\n    }\n  }, [_vm._v(\"而是用智能连接彼此，释放算力的真正价值\")])]), _c(\"div\", {\n    staticClass: \"computing-stats\"\n  }, [_c(\"div\", {\n    staticClass: \"stat-item\"\n  }, [_c(\"div\", {\n    staticClass: \"stat-number\",\n    attrs: {\n      \"data-target\": \"10000\"\n    }\n  }, [_vm._v(\"0\")]), _c(\"div\", {\n    staticClass: \"stat-label\"\n  }, [_vm._v(\"GPU核心\")])]), _c(\"div\", {\n    staticClass: \"stat-item\"\n  }, [_c(\"div\", {\n    staticClass: \"stat-number\",\n    attrs: {\n      \"data-target\": \"99.9\"\n    }\n  }, [_vm._v(\"0\")]), _c(\"div\", {\n    staticClass: \"stat-label\"\n  }, [_vm._v(\"可用性%\")])]), _c(\"div\", {\n    staticClass: \"stat-item\"\n  }, [_c(\"div\", {\n    staticClass: \"stat-number\",\n    attrs: {\n      \"data-target\": \"24\"\n    }\n  }, [_vm._v(\"0\")]), _c(\"div\", {\n    staticClass: \"stat-label\"\n  }, [_vm._v(\"全天候服务\")])])])])]), _c(\"section\", {\n    staticClass: \"about-section\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"div\", {\n    staticClass: \"am-g\"\n  }, [_c(\"div\", {\n    staticClass: \"am-u-md-6\"\n  }, [_c(\"div\", {\n    staticClass: \"our-company-text\"\n  }, [_c(\"h3\", [_vm._v(\"关于我们\")]), _c(\"p\", [_vm._v(' 天工开物智能科技（苏州）有限公司，致力于打造面向企业级用户的高性能计算解决方案， 围绕\"高效调度、低门槛使用、专业保障\"的核心理念，为 AI、大模型、图形渲染、科研计算等场景提供灵活、稳定、弹性的算力支持。 ')]), _c(\"p\", [_vm._v(\" 我们基于全国分布式算力网络，自主构建智算调度平台，整合GPU资源与数据中心节点， 为企业提供从算力资源租用、模型部署优化到全流程运维服务的一站式专业方案。 \")]), _c(\"p\", [_vm._v(\" 在智能时代的浪潮中，天工开物致力于打造企业级专业算力服务平台， 以全国分布式高性能计算网络为基础，提供稳定、高效、灵活可控的算力解决方案。 \")])])]), _c(\"div\", {\n    staticClass: \"am-u-md-6\"\n  }, [_c(\"div\", {\n    staticClass: \"our-company-quote\"\n  }, [_c(\"div\", {\n    staticClass: \"our-company-img\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: \"/images/tiangonghead.jpeg\",\n      alt: \"天工开物智能科技\"\n    }\n  })])])])])])]), _c(\"section\", {\n    staticClass: \"our-mission\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"div\", {\n    staticClass: \"section--header\"\n  }, [_c(\"h2\", {\n    staticClass: \"section--title\"\n  }, [_vm._v(\"选择我们的理由\")])]), _c(\"div\", {\n    staticClass: \"am-g\"\n  }, [_c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item_media\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-server\",\n    staticStyle: {\n      \"font-size\": \"48px\",\n      color: \"#1470FF\"\n    }\n  })]), _c(\"h4\", {\n    staticClass: \"our_mission--item_title\"\n  }, [_vm._v(\"企业级专业服务\")]), _c(\"div\", {\n    staticClass: \"our_mission--item_body\"\n  }, [_c(\"p\", [_vm._v(\"为AI、科研、图形渲染、工业仿真等场景，提供稳定高效的高性能计算支持\")])])])]), _c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item_media\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-globe\",\n    staticStyle: {\n      \"font-size\": \"48px\",\n      color: \"#1470FF\"\n    }\n  })]), _c(\"h4\", {\n    staticClass: \"our_mission--item_title\"\n  }, [_vm._v(\"全国分布式节点布局\")]), _c(\"div\", {\n    staticClass: \"our_mission--item_body\"\n  }, [_c(\"p\", [_vm._v(\"多地部署，动态调度，资源灵活，负载均衡，响应迅速\")])])])]), _c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item_media\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-cogs\",\n    staticStyle: {\n      \"font-size\": \"48px\",\n      color: \"#1470FF\"\n    }\n  })]), _c(\"h4\", {\n    staticClass: \"our_mission--item_title\"\n  }, [_vm._v(\"灵活弹性 + 高性价比\")]), _c(\"div\", {\n    staticClass: \"our_mission--item_body\"\n  }, [_c(\"p\", [_vm._v(\"自研调度平台，支持定制、按需计费与大客户深度合作\")])])])]), _c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item_media\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-users\",\n    staticStyle: {\n      \"font-size\": \"48px\",\n      color: \"#1470FF\"\n    }\n  })]), _c(\"h4\", {\n    staticClass: \"our_mission--item_title\"\n  }, [_vm._v(\"更懂企业的算力伙伴\")]), _c(\"div\", {\n    staticClass: \"our_mission--item_body\"\n  }, [_c(\"p\", [_vm._v(\"从需求对接、技术支持到运维保障，全流程一对一服务\")])])])])])])]), _c(\"section\", {\n    staticClass: \"our-team\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"div\", {\n    staticClass: \"section--header\"\n  }, [_c(\"h2\", {\n    staticClass: \"section--title\"\n  }, [_vm._v(\"核心团队\")]), _c(\"p\", {\n    staticClass: \"section--description\"\n  }, [_vm._v(\" 核心团队来自知名AI云计算厂商、IDC运维专家与高校科研机构 \")])]), _c(\"div\", {\n    staticClass: \"am-g\"\n  }, [_c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"team-box\"\n  }, [_c(\"div\", {\n    staticClass: \"our-team-img\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: \"/images/back1.webp\",\n      alt: \"技术团队\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"team_member--body\"\n  }, [_c(\"h4\", {\n    staticClass: \"team_member--name\"\n  }, [_vm._v(\"技术研发\")]), _c(\"span\", {\n    staticClass: \"team_member--position\"\n  }, [_vm._v(\"专业的研发团队，深耕AI算力调度与优化\")])])])]), _c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"team-box\"\n  }, [_c(\"div\", {\n    staticClass: \"our-team-img\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: \"/images/back1.webp\",\n      alt: \"运维团队\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"team_member--body\"\n  }, [_c(\"h4\", {\n    staticClass: \"team_member--name\"\n  }, [_vm._v(\"运维保障\")]), _c(\"span\", {\n    staticClass: \"team_member--position\"\n  }, [_vm._v(\"5x8小时专业运维，确保服务稳定可靠\")])])])]), _c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"team-box\"\n  }, [_c(\"div\", {\n    staticClass: \"our-team-img\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: \"/images/back1.webp\",\n      alt: \"客服团队\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"team_member--body\"\n  }, [_c(\"h4\", {\n    staticClass: \"team_member--name\"\n  }, [_vm._v(\"客户服务\")]), _c(\"span\", {\n    staticClass: \"team_member--position\"\n  }, [_vm._v(\"专业客户经理，提供一对一贴心服务\")])])])])])])]), _c(\"section\", {\n    staticClass: \"contact-section\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"div\", {\n    staticClass: \"am-g\"\n  }, [_c(\"div\", {\n    staticClass: \"am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"contact-item\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-phone\"\n  }), _c(\"h4\", [_vm._v(\"联系电话\")]), _c(\"p\", [_vm._v(\"400-XXX-XXXX\")])])]), _c(\"div\", {\n    staticClass: \"am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"contact-item\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-envelope\"\n  }), _c(\"h4\", [_vm._v(\"邮箱地址\")]), _c(\"p\", [_vm._v(\"<EMAIL>\")])])]), _c(\"div\", {\n    staticClass: \"am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"contact-item\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-map-marker\"\n  }), _c(\"h4\", [_vm._v(\"公司地址\")]), _c(\"p\", [_vm._v(\"江苏省苏州市\")])])])])])]), _c(\"section\", {\n    staticClass: \"cta-section\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"div\", {\n    staticClass: \"cta-content\"\n  }, [_c(\"h2\", [_vm._v(\"立即开启智算之旅\")]), _c(\"p\", [_vm._v(\"连接智算未来，让高性能计算像水电一样可得、可控、可负担\")]), _c(\"div\", {\n    staticClass: \"cta-buttons\"\n  }, [_c(\"button\", {\n    staticClass: \"am-btn am-btn-primary am-btn-lg\",\n    on: {\n      click: _vm.startTrial\n    }\n  }, [_vm._v(\"立即开始\")])])])])])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "background", "opacity", "position", "top", "left", "right", "bottom", "_l", "n", "key", "style", "getParticleStyle", "getNodeStyle", "attrs", "viewBox", "connectionLines", "line", "id", "x1", "y1", "x2", "y2", "_v", "src", "alt", "color", "on", "click", "startTrial", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/views/About/AboutView.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"Layout\", [\n    _c(\"div\", { staticClass: \"about-banner\" }, [\n      _c(\"div\", {\n        staticClass: \"about-banner-bg\",\n        staticStyle: {\n          background: \"url('/images/back3.webp') center/cover\",\n          opacity: \"0.2\",\n          \"z-index\": \"1\",\n          position: \"absolute\",\n          top: \"0\",\n          left: \"0\",\n          right: \"0\",\n          bottom: \"0\",\n        },\n      }),\n      _c(\"div\", { staticClass: \"grid-background\" }),\n      _c(\n        \"div\",\n        { staticClass: \"particles-container\" },\n        _vm._l(50, function (n) {\n          return _c(\"div\", {\n            key: n,\n            staticClass: \"particle\",\n            style: _vm.getParticleStyle(),\n          })\n        }),\n        0\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"data-streams\" },\n        _vm._l(8, function (n) {\n          return _c(\"div\", { key: n, staticClass: \"data-stream\" })\n        }),\n        0\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"network-nodes\" },\n        [\n          _vm._l(12, function (n) {\n            return _c(\n              \"div\",\n              { key: n, staticClass: \"node\", style: _vm.getNodeStyle() },\n              [_c(\"div\", { staticClass: \"node-pulse\" })]\n            )\n          }),\n          _c(\n            \"svg\",\n            {\n              staticClass: \"connection-lines\",\n              attrs: { viewBox: \"0 0 100 100\" },\n            },\n            _vm._l(_vm.connectionLines, function (line) {\n              return _c(\"line\", {\n                key: line.id,\n                staticClass: \"connection-line\",\n                attrs: { x1: line.x1, y1: line.y1, x2: line.x2, y2: line.y2 },\n              })\n            }),\n            0\n          ),\n        ],\n        2\n      ),\n      _c(\"div\", { staticClass: \"light-effects\" }, [\n        _c(\"div\", { staticClass: \"light-beam light-beam-1\" }),\n        _c(\"div\", { staticClass: \"light-beam light-beam-2\" }),\n        _c(\"div\", { staticClass: \"light-beam light-beam-3\" }),\n      ]),\n      _c(\"div\", { staticClass: \"banner-content\" }, [\n        _c(\"h1\", { staticClass: \"banner-title\" }, [\n          _c(\n            \"span\",\n            {\n              staticClass: \"title-word\",\n              attrs: { \"data-text\": \"重塑算力想象\" },\n            },\n            [_vm._v(\"重塑算力想象\")]\n          ),\n          _c(\"span\", { staticClass: \"title-separator\" }, [_vm._v(\"，\")]),\n          _c(\n            \"span\",\n            {\n              staticClass: \"title-word\",\n              attrs: { \"data-text\": \"让智能无处不在\" },\n            },\n            [_vm._v(\"让智能无处不在\")]\n          ),\n        ]),\n        _c(\"div\", { staticClass: \"banner-subtitle-container\" }, [\n          _c(\"p\", { staticClass: \"banner-subtitle typing-effect\" }, [\n            _vm._v(\"我们相信\"),\n          ]),\n          _c(\n            \"p\",\n            {\n              staticClass: \"banner-subtitle typing-effect\",\n              staticStyle: { \"animation-delay\": \"1s\" },\n            },\n            [_vm._v(\"人类无需再围成一台机器\")]\n          ),\n          _c(\n            \"p\",\n            {\n              staticClass: \"banner-subtitle typing-effect\",\n              staticStyle: { \"animation-delay\": \"2s\" },\n            },\n            [_vm._v(\"而是用智能连接彼此，释放算力的真正价值\")]\n          ),\n        ]),\n        _c(\"div\", { staticClass: \"computing-stats\" }, [\n          _c(\"div\", { staticClass: \"stat-item\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"stat-number\", attrs: { \"data-target\": \"10000\" } },\n              [_vm._v(\"0\")]\n            ),\n            _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"GPU核心\")]),\n          ]),\n          _c(\"div\", { staticClass: \"stat-item\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"stat-number\", attrs: { \"data-target\": \"99.9\" } },\n              [_vm._v(\"0\")]\n            ),\n            _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"可用性%\")]),\n          ]),\n          _c(\"div\", { staticClass: \"stat-item\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"stat-number\", attrs: { \"data-target\": \"24\" } },\n              [_vm._v(\"0\")]\n            ),\n            _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"全天候服务\")]),\n          ]),\n        ]),\n      ]),\n    ]),\n    _c(\"section\", { staticClass: \"about-section\" }, [\n      _c(\"div\", { staticClass: \"container\" }, [\n        _c(\"div\", { staticClass: \"am-g\" }, [\n          _c(\"div\", { staticClass: \"am-u-md-6\" }, [\n            _c(\"div\", { staticClass: \"our-company-text\" }, [\n              _c(\"h3\", [_vm._v(\"关于我们\")]),\n              _c(\"p\", [\n                _vm._v(\n                  ' 天工开物智能科技（苏州）有限公司，致力于打造面向企业级用户的高性能计算解决方案， 围绕\"高效调度、低门槛使用、专业保障\"的核心理念，为 AI、大模型、图形渲染、科研计算等场景提供灵活、稳定、弹性的算力支持。 '\n                ),\n              ]),\n              _c(\"p\", [\n                _vm._v(\n                  \" 我们基于全国分布式算力网络，自主构建智算调度平台，整合GPU资源与数据中心节点， 为企业提供从算力资源租用、模型部署优化到全流程运维服务的一站式专业方案。 \"\n                ),\n              ]),\n              _c(\"p\", [\n                _vm._v(\n                  \" 在智能时代的浪潮中，天工开物致力于打造企业级专业算力服务平台， 以全国分布式高性能计算网络为基础，提供稳定、高效、灵活可控的算力解决方案。 \"\n                ),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-md-6\" }, [\n            _c(\"div\", { staticClass: \"our-company-quote\" }, [\n              _c(\"div\", { staticClass: \"our-company-img\" }, [\n                _c(\"img\", {\n                  attrs: {\n                    src: \"/images/tiangonghead.jpeg\",\n                    alt: \"天工开物智能科技\",\n                  },\n                }),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n    ]),\n    _c(\"section\", { staticClass: \"our-mission\" }, [\n      _c(\"div\", { staticClass: \"container\" }, [\n        _c(\"div\", { staticClass: \"section--header\" }, [\n          _c(\"h2\", { staticClass: \"section--title\" }, [\n            _vm._v(\"选择我们的理由\"),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"am-g\" }, [\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\" }, [\n            _c(\"div\", { staticClass: \"our_mission--item\" }, [\n              _c(\"div\", { staticClass: \"our_mission--item_media\" }, [\n                _c(\"i\", {\n                  staticClass: \"am-icon-server\",\n                  staticStyle: { \"font-size\": \"48px\", color: \"#1470FF\" },\n                }),\n              ]),\n              _c(\"h4\", { staticClass: \"our_mission--item_title\" }, [\n                _vm._v(\"企业级专业服务\"),\n              ]),\n              _c(\"div\", { staticClass: \"our_mission--item_body\" }, [\n                _c(\"p\", [\n                  _vm._v(\n                    \"为AI、科研、图形渲染、工业仿真等场景，提供稳定高效的高性能计算支持\"\n                  ),\n                ]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\" }, [\n            _c(\"div\", { staticClass: \"our_mission--item\" }, [\n              _c(\"div\", { staticClass: \"our_mission--item_media\" }, [\n                _c(\"i\", {\n                  staticClass: \"am-icon-globe\",\n                  staticStyle: { \"font-size\": \"48px\", color: \"#1470FF\" },\n                }),\n              ]),\n              _c(\"h4\", { staticClass: \"our_mission--item_title\" }, [\n                _vm._v(\"全国分布式节点布局\"),\n              ]),\n              _c(\"div\", { staticClass: \"our_mission--item_body\" }, [\n                _c(\"p\", [\n                  _vm._v(\"多地部署，动态调度，资源灵活，负载均衡，响应迅速\"),\n                ]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\" }, [\n            _c(\"div\", { staticClass: \"our_mission--item\" }, [\n              _c(\"div\", { staticClass: \"our_mission--item_media\" }, [\n                _c(\"i\", {\n                  staticClass: \"am-icon-cogs\",\n                  staticStyle: { \"font-size\": \"48px\", color: \"#1470FF\" },\n                }),\n              ]),\n              _c(\"h4\", { staticClass: \"our_mission--item_title\" }, [\n                _vm._v(\"灵活弹性 + 高性价比\"),\n              ]),\n              _c(\"div\", { staticClass: \"our_mission--item_body\" }, [\n                _c(\"p\", [\n                  _vm._v(\"自研调度平台，支持定制、按需计费与大客户深度合作\"),\n                ]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\" }, [\n            _c(\"div\", { staticClass: \"our_mission--item\" }, [\n              _c(\"div\", { staticClass: \"our_mission--item_media\" }, [\n                _c(\"i\", {\n                  staticClass: \"am-icon-users\",\n                  staticStyle: { \"font-size\": \"48px\", color: \"#1470FF\" },\n                }),\n              ]),\n              _c(\"h4\", { staticClass: \"our_mission--item_title\" }, [\n                _vm._v(\"更懂企业的算力伙伴\"),\n              ]),\n              _c(\"div\", { staticClass: \"our_mission--item_body\" }, [\n                _c(\"p\", [\n                  _vm._v(\"从需求对接、技术支持到运维保障，全流程一对一服务\"),\n                ]),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n    ]),\n    _c(\"section\", { staticClass: \"our-team\" }, [\n      _c(\"div\", { staticClass: \"container\" }, [\n        _c(\"div\", { staticClass: \"section--header\" }, [\n          _c(\"h2\", { staticClass: \"section--title\" }, [_vm._v(\"核心团队\")]),\n          _c(\"p\", { staticClass: \"section--description\" }, [\n            _vm._v(\" 核心团队来自知名AI云计算厂商、IDC运维专家与高校科研机构 \"),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"am-g\" }, [\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"team-box\" }, [\n              _c(\"div\", { staticClass: \"our-team-img\" }, [\n                _c(\"img\", {\n                  attrs: { src: \"/images/back1.webp\", alt: \"技术团队\" },\n                }),\n              ]),\n              _c(\"div\", { staticClass: \"team_member--body\" }, [\n                _c(\"h4\", { staticClass: \"team_member--name\" }, [\n                  _vm._v(\"技术研发\"),\n                ]),\n                _c(\"span\", { staticClass: \"team_member--position\" }, [\n                  _vm._v(\"专业的研发团队，深耕AI算力调度与优化\"),\n                ]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"team-box\" }, [\n              _c(\"div\", { staticClass: \"our-team-img\" }, [\n                _c(\"img\", {\n                  attrs: { src: \"/images/back1.webp\", alt: \"运维团队\" },\n                }),\n              ]),\n              _c(\"div\", { staticClass: \"team_member--body\" }, [\n                _c(\"h4\", { staticClass: \"team_member--name\" }, [\n                  _vm._v(\"运维保障\"),\n                ]),\n                _c(\"span\", { staticClass: \"team_member--position\" }, [\n                  _vm._v(\"5x8小时专业运维，确保服务稳定可靠\"),\n                ]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"team-box\" }, [\n              _c(\"div\", { staticClass: \"our-team-img\" }, [\n                _c(\"img\", {\n                  attrs: { src: \"/images/back1.webp\", alt: \"客服团队\" },\n                }),\n              ]),\n              _c(\"div\", { staticClass: \"team_member--body\" }, [\n                _c(\"h4\", { staticClass: \"team_member--name\" }, [\n                  _vm._v(\"客户服务\"),\n                ]),\n                _c(\"span\", { staticClass: \"team_member--position\" }, [\n                  _vm._v(\"专业客户经理，提供一对一贴心服务\"),\n                ]),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n    ]),\n    _c(\"section\", { staticClass: \"contact-section\" }, [\n      _c(\"div\", { staticClass: \"container\" }, [\n        _c(\"div\", { staticClass: \"am-g\" }, [\n          _c(\"div\", { staticClass: \"am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"contact-item\" }, [\n              _c(\"i\", { staticClass: \"am-icon-phone\" }),\n              _c(\"h4\", [_vm._v(\"联系电话\")]),\n              _c(\"p\", [_vm._v(\"400-XXX-XXXX\")]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"contact-item\" }, [\n              _c(\"i\", { staticClass: \"am-icon-envelope\" }),\n              _c(\"h4\", [_vm._v(\"邮箱地址\")]),\n              _c(\"p\", [_vm._v(\"<EMAIL>\")]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"contact-item\" }, [\n              _c(\"i\", { staticClass: \"am-icon-map-marker\" }),\n              _c(\"h4\", [_vm._v(\"公司地址\")]),\n              _c(\"p\", [_vm._v(\"江苏省苏州市\")]),\n            ]),\n          ]),\n        ]),\n      ]),\n    ]),\n    _c(\"section\", { staticClass: \"cta-section\" }, [\n      _c(\"div\", { staticClass: \"container\" }, [\n        _c(\"div\", { staticClass: \"cta-content\" }, [\n          _c(\"h2\", [_vm._v(\"立即开启智算之旅\")]),\n          _c(\"p\", [\n            _vm._v(\"连接智算未来，让高性能计算像水电一样可得、可控、可负担\"),\n          ]),\n          _c(\"div\", { staticClass: \"cta-buttons\" }, [\n            _c(\n              \"button\",\n              {\n                staticClass: \"am-btn am-btn-primary am-btn-lg\",\n                on: { click: _vm.startTrial },\n              },\n              [_vm._v(\"立即开始\")]\n            ),\n          ]),\n        ]),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,QAAQ,EAAE,CAClBA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,iBAAiB;IAC9BC,WAAW,EAAE;MACXC,UAAU,EAAE,wCAAwC;MACpDC,OAAO,EAAE,KAAK;MACd,SAAS,EAAE,GAAG;MACdC,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE;IACV;EACF,CAAC,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC7CF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtCH,GAAG,CAACY,EAAE,CAAC,EAAE,EAAE,UAAUC,CAAC,EAAE;IACtB,OAAOZ,EAAE,CAAC,KAAK,EAAE;MACfa,GAAG,EAAED,CAAC;MACNV,WAAW,EAAE,UAAU;MACvBY,KAAK,EAAEf,GAAG,CAACgB,gBAAgB;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CAAC,CACF,EACDf,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/BH,GAAG,CAACY,EAAE,CAAC,CAAC,EAAE,UAAUC,CAAC,EAAE;IACrB,OAAOZ,EAAE,CAAC,KAAK,EAAE;MAAEa,GAAG,EAAED,CAAC;MAAEV,WAAW,EAAE;IAAc,CAAC,CAAC;EAC1D,CAAC,CAAC,EACF,CAAC,CACF,EACDF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEH,GAAG,CAACY,EAAE,CAAC,EAAE,EAAE,UAAUC,CAAC,EAAE;IACtB,OAAOZ,EAAE,CACP,KAAK,EACL;MAAEa,GAAG,EAAED,CAAC;MAAEV,WAAW,EAAE,MAAM;MAAEY,KAAK,EAAEf,GAAG,CAACiB,YAAY;IAAG,CAAC,EAC1D,CAAChB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,CAAC,CAAC,CAC3C;EACH,CAAC,CAAC,EACFF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,kBAAkB;IAC/Be,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAc;EAClC,CAAC,EACDnB,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACoB,eAAe,EAAE,UAAUC,IAAI,EAAE;IAC1C,OAAOpB,EAAE,CAAC,MAAM,EAAE;MAChBa,GAAG,EAAEO,IAAI,CAACC,EAAE;MACZnB,WAAW,EAAE,iBAAiB;MAC9Be,KAAK,EAAE;QAAEK,EAAE,EAAEF,IAAI,CAACE,EAAE;QAAEC,EAAE,EAAEH,IAAI,CAACG,EAAE;QAAEC,EAAE,EAAEJ,IAAI,CAACI,EAAE;QAAEC,EAAE,EAAEL,IAAI,CAACK;MAAG;IAC9D,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CAAC,CACF,CACF,EACD,CAAC,CACF,EACDzB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,CAAC,EACrDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,CAAC,EACrDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,CAAC,CACtD,CAAC,EACFF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACxCF,EAAE,CACA,MAAM,EACN;IACEE,WAAW,EAAE,YAAY;IACzBe,KAAK,EAAE;MAAE,WAAW,EAAE;IAAS;EACjC,CAAC,EACD,CAAClB,GAAG,CAAC2B,EAAE,CAAC,QAAQ,CAAC,CAAC,CACnB,EACD1B,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAACH,GAAG,CAAC2B,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAC7D1B,EAAE,CACA,MAAM,EACN;IACEE,WAAW,EAAE,YAAY;IACzBe,KAAK,EAAE;MAAE,WAAW,EAAE;IAAU;EAClC,CAAC,EACD,CAAClB,GAAG,CAAC2B,EAAE,CAAC,SAAS,CAAC,CAAC,CACpB,CACF,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA4B,CAAC,EAAE,CACtDF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAgC,CAAC,EAAE,CACxDH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACF1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,+BAA+B;IAC5CC,WAAW,EAAE;MAAE,iBAAiB,EAAE;IAAK;EACzC,CAAC,EACD,CAACJ,GAAG,CAAC2B,EAAE,CAAC,aAAa,CAAC,CAAC,CACxB,EACD1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,+BAA+B;IAC5CC,WAAW,EAAE;MAAE,iBAAiB,EAAE;IAAK;EACzC,CAAC,EACD,CAACJ,GAAG,CAAC2B,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAChC,CACF,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,aAAa;IAAEe,KAAK,EAAE;MAAE,aAAa,EAAE;IAAQ;EAAE,CAAC,EACjE,CAAClB,GAAG,CAAC2B,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACD1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAC5D,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,aAAa;IAAEe,KAAK,EAAE;MAAE,aAAa,EAAE;IAAO;EAAE,CAAC,EAChE,CAAClB,GAAG,CAAC2B,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACD1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC3D,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,aAAa;IAAEe,KAAK,EAAE;MAAE,aAAa,EAAE;IAAK;EAAE,CAAC,EAC9D,CAAClB,GAAG,CAAC2B,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACD1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAC5D,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1B1B,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAAC2B,EAAE,CACJ,2GAA2G,CAC5G,CACF,CAAC,EACF1B,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAAC2B,EAAE,CACJ,iFAAiF,CAClF,CACF,CAAC,EACF1B,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAAC2B,EAAE,CACJ,yEAAyE,CAC1E,CACF,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IACRiB,KAAK,EAAE;MACLU,GAAG,EAAE,2BAA2B;MAChCC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF5B,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAAC2B,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiC,CAAC,EAAE,CAC3DF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACpDF,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,gBAAgB;IAC7BC,WAAW,EAAE;MAAE,WAAW,EAAE,MAAM;MAAE0B,KAAK,EAAE;IAAU;EACvD,CAAC,CAAC,CACH,CAAC,EACF7B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACnDH,GAAG,CAAC2B,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDF,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAAC2B,EAAE,CACJ,oCAAoC,CACrC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiC,CAAC,EAAE,CAC3DF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACpDF,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE;MAAE,WAAW,EAAE,MAAM;MAAE0B,KAAK,EAAE;IAAU;EACvD,CAAC,CAAC,CACH,CAAC,EACF7B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACnDH,GAAG,CAAC2B,EAAE,CAAC,WAAW,CAAC,CACpB,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDF,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAAC2B,EAAE,CAAC,0BAA0B,CAAC,CACnC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiC,CAAC,EAAE,CAC3DF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACpDF,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE;MAAE,WAAW,EAAE,MAAM;MAAE0B,KAAK,EAAE;IAAU;EACvD,CAAC,CAAC,CACH,CAAC,EACF7B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACnDH,GAAG,CAAC2B,EAAE,CAAC,aAAa,CAAC,CACtB,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDF,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAAC2B,EAAE,CAAC,0BAA0B,CAAC,CACnC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiC,CAAC,EAAE,CAC3DF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACpDF,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE;MAAE,WAAW,EAAE,MAAM;MAAE0B,KAAK,EAAE;IAAU;EACvD,CAAC,CAAC,CACH,CAAC,EACF7B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACnDH,GAAG,CAAC2B,EAAE,CAAC,WAAW,CAAC,CACpB,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDF,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAAC2B,EAAE,CAAC,0BAA0B,CAAC,CACnC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAACH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC7D1B,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CAC/CH,GAAG,CAAC2B,EAAE,CAAC,kCAAkC,CAAC,CAC3C,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRiB,KAAK,EAAE;MAAEU,GAAG,EAAE,oBAAoB;MAAEC,GAAG,EAAE;IAAO;EAClD,CAAC,CAAC,CACH,CAAC,EACF5B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC7CH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACF1B,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,EAAE,CACnDH,GAAG,CAAC2B,EAAE,CAAC,qBAAqB,CAAC,CAC9B,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRiB,KAAK,EAAE;MAAEU,GAAG,EAAE,oBAAoB;MAAEC,GAAG,EAAE;IAAO;EAClD,CAAC,CAAC,CACH,CAAC,EACF5B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC7CH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACF1B,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,EAAE,CACnDH,GAAG,CAAC2B,EAAE,CAAC,oBAAoB,CAAC,CAC7B,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRiB,KAAK,EAAE;MAAEU,GAAG,EAAE,oBAAoB;MAAEC,GAAG,EAAE;IAAO;EAClD,CAAC,CAAC,CACH,CAAC,EACF5B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC7CH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACF1B,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,EAAE,CACnDH,GAAG,CAAC2B,EAAE,CAAC,kBAAkB,CAAC,CAC3B,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAChDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,CAAC,EACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1B1B,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAClC,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,CAAC,EAC5CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1B1B,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAC/C,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,EAC9CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1B1B,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAC5B,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAC9B1B,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAAC2B,EAAE,CAAC,6BAA6B,CAAC,CACtC,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,iCAAiC;IAC9C4B,EAAE,EAAE;MAAEC,KAAK,EAAEhC,GAAG,CAACiC;IAAW;EAC9B,CAAC,EACD,CAACjC,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIO,eAAe,GAAG,EAAE;AACxBnC,MAAM,CAACoC,aAAa,GAAG,IAAI;AAE3B,SAASpC,MAAM,EAAEmC,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}