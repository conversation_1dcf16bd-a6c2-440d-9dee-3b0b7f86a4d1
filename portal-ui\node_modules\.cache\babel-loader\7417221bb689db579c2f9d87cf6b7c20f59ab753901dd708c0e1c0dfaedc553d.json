{"ast": null, "code": "import Layout from \"@/components/common/Layout-header\";\nexport default {\n  components: {\n    Layout\n  },\n  data() {\n    return {\n      headerKey: 0,\n      showLayout: true\n    };\n  },\n  methods: {\n    hidenLayout() {\n      this.showLayout = !this.showLayout;\n    },\n    // 需要刷新 Header 时调用\n    refreshHeader() {\n      console.log(\"刷新了\");\n      this.headerKey += 1;\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "components", "data", "<PERSON><PERSON><PERSON>", "showLayout", "methods", "hidenLayout", "refreshHeader", "console", "log"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\r\n    <div id=\"app\">\r\n\t<!-- :key=\"$route.fullPath\" 解决了路由前缀相同时跳转不刷新 -->\r\n        <Layout v-if=\"showLayout\"  :key=\"headerKey\">\r\n        </Layout>\r\n        <router-view @refresh-header=\"refreshHeader\" @hiden-layout=\"hidenLayout\" :key=\"$route.fullPath\"/>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import Layout from \"@/components/common/Layout-header\";\r\n    export default {\r\n        components: {Layout},\r\n        data() {\r\n            return {\r\n                headerKey: 0,\r\n                showLayout:true\r\n            }\r\n        },\r\n        methods: {\r\n            hidenLayout(){\r\n                this.showLayout = !this.showLayout\r\n            },\r\n            // 需要刷新 Header 时调用\r\n            refreshHeader() {\r\n                console.log(\"刷新了\")\r\n                this.headerKey += 1\r\n            }\r\n        }\r\n    }\r\n</script>\r\n<style>\r\n/* 隐藏垂直滚动条但保留滚动功能 */\r\nhtml {\r\n  overflow-y: scroll; /* 强制显示滚动条占位防止内容跳动 */\r\n  scrollbar-width: none; /* Firefox 隐藏滚动条 */\r\n  -ms-overflow-style: none; /* IE 10+ 隐藏滚动条 */\r\n}\r\n\r\n/* Chrome/Safari/Edge 隐藏滚动条 */\r\nhtml::-webkit-scrollbar,\r\nbody::-webkit-scrollbar {\r\n  width: 0 !important;\r\n  height: 0 !important;\r\n  display: none !important;\r\n}\r\n\r\nbody {\r\n  overflow: -moz-scrollbars-none; /* Firefox 旧版 */\r\n  -webkit-overflow-scrolling: touch; /* 启用惯性滚动 */\r\n  scrollbar-width: none; /* 新版 Firefox */\r\n}\r\n\r\n/* 所有可滚动容器统一处理 */\r\n* {\r\n  scrollbar-width: none !important; /* Firefox */\r\n  -ms-overflow-style: none !important; /* IE/Edge */\r\n}\r\n\r\n*::-webkit-scrollbar {\r\n  display: none !important; /* WebKit 内核 */\r\n}\r\n</style>\r\n\r\n"], "mappings": "AAUA,OAAAA,MAAA;AACA;EACAC,UAAA;IAAAD;EAAA;EACAE,KAAA;IACA;MACAC,SAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA;MACA,KAAAF,UAAA,SAAAA,UAAA;IACA;IACA;IACAG,cAAA;MACAC,OAAA,CAAAC,GAAA;MACA,KAAAN,SAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}