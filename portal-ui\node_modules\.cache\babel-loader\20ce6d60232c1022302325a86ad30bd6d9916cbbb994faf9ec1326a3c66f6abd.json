{"ast": null, "code": "export default {\n  name: 'Mider',\n  data() {\n    return {\n      activePopup: null,\n      showModal: false,\n      showErrors: false,\n      showFeedbackTooltip: false,\n      showConfirmation: false,\n      wechatQRCode: require('@/assets/images/footer/wechat.jpg'),\n      // 替换为实际路径\n      contactQRCode: require('@/assets/images/footer/wechat.jpg'),\n      // 替换为实际路径\n      feedback: {\n        type: '',\n        description: '',\n        instanceId: '',\n        image: null,\n        imagePreview: null\n      }\n    };\n  },\n  methods: {\n    showPopup(type) {\n      this.activePopup = type;\n    },\n    hidePopup(type) {\n      if (this.activePopup === type) {\n        this.activePopup = null;\n      }\n    },\n    showTooltip() {\n      this.showFeedbackTooltip = true;\n    },\n    hideTooltip() {\n      this.showFeedbackTooltip = false;\n    },\n    showFeedbackModal() {\n      this.showModal = true;\n      this.showErrors = false;\n    },\n    closeFeedbackModal() {\n      this.showModal = false;\n      // Reset form\n      this.feedback = {\n        type: '',\n        description: '',\n        instanceId: '',\n        image: null,\n        imagePreview: null\n      };\n      this.showErrors = false;\n    },\n    triggerFileUpload() {\n      this.$refs.fileInput.click();\n    },\n    onFileChange(event) {\n      const file = event.target.files[0];\n      if (file) {\n        this.processImage(file);\n      }\n    },\n    onFileDrop(event) {\n      const file = event.dataTransfer.files[0];\n      if (file && file.type.match('image.*')) {\n        this.processImage(file);\n      }\n    },\n    processImage(file) {\n      if (file && file.type.match('image.*')) {\n        this.feedback.image = file;\n\n        // Create preview\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.feedback.imagePreview = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      }\n    },\n    removeImage() {\n      this.feedback.image = null;\n      this.feedback.imagePreview = null;\n    },\n    confirmSubmit() {\n      // Validate form\n      this.showErrors = true;\n      if (!this.feedback.type || !this.feedback.description) {\n        return;\n      }\n\n      // Submit feedback\n      this.submitFeedback();\n    },\n    submitFeedback() {\n      // Here you would typically send the data to your backend\n\n      // Create FormData object if there's an image\n      if (this.feedback.image) {\n        const formData = new FormData();\n        formData.append('type', this.feedback.type);\n        formData.append('description', this.feedback.description);\n        formData.append('instanceId', this.feedback.instanceId);\n        formData.append('image', this.feedback.image);\n\n        // Send formData to your API\n        // this.$axios.post('/api/feedback', formData)\n      }\n\n      // Close feedback modal and show confirmation dialog\n      this.showModal = false;\n      this.showConfirmation = true;\n\n      // Reset form data\n      this.feedback = {\n        type: '',\n        description: '',\n        instanceId: '',\n        image: null,\n        imagePreview: null\n      };\n    },\n    closeConfirmation() {\n      this.showConfirmation = false;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "activePopup", "showModal", "showErrors", "showFeedbackTooltip", "showConfirmation", "wechatQRCode", "require", "contactQRCode", "feedback", "type", "description", "instanceId", "image", "imagePreview", "methods", "showPopup", "hidePopup", "showTooltip", "hideTooltip", "showFeedbackModal", "closeFeedbackModal", "triggerFileUpload", "$refs", "fileInput", "click", "onFileChange", "event", "file", "target", "files", "processImage", "onFileDrop", "dataTransfer", "match", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "removeImage", "confirmSubmit", "submitFeedback", "formData", "FormData", "append", "closeConfirmation"], "sources": ["src/components/common/mider/Mider.vue"], "sourcesContent": ["<template>\r\n  <div class=\"mider-container\">\r\n    <!-- Floating sidebar with icons -->\r\n    <div class=\"mider-sidebar\">\r\n<!--      <div class=\"coupon-tag\">-->\r\n<!--        <span>领</span>-->\r\n<!--        <span>优</span>-->\r\n<!--        <span>惠</span>-->\r\n<!--        <span>券</span>-->\r\n<!--      </div>-->\r\n\r\n      <div class=\"icon-wrapper\">\r\n        <div class=\"icon-item\" @mouseenter=\"showPopup('wechat')\" @mouseleave=\"hidePopup('wechat')\">\r\n          <i class=\"iconfont icon-wechat\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5-2.9 10.5-4.6 16.3-4.6 3 0 5.9 0.5 8.7 1.4 35.9 10.5 74.6 16.2 114.2 16.2 9.8 0 19.5-0.3 29-1C624.9 597 702.1 497.6 702.1 378.1c0-0.2-0.1-0.4-0.1-0.7 0 0-0.1-0.3-0.1-0.5-0.8 0-1.8-0.1-2.8-0.1-3.1 0-6.2 0.2-9 0.6z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M380.5 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7zM534.3 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M683.5 673.6c82.1 0 151.2-56 171.5-131.8 4.1-15.2-9.3-29.5-24.9-26.6-3.5 0.6-7.1 1-10.7 1-34.4 0-62.4-28-62.4-62.4 0-1.6 0.1-3.3 0.2-4.9 0.8-19.3-24.5-28.7-35.7-13.5-39.1 53-102.7 87.5-174.5 87.5-12.3 0-24.4-1-36.1-3-5.2-0.9-10.5 0.8-14.1 4.5-4.6 4.6-16.7 17.2-16.7 17.2l-0.6 0.6c-5.4 5.4-8.7 12.3-9.5 19.8-0.9 8.5 1.9 17.1 7.7 23.6l1.3 1.3c6.3 7.5 15.6 11.7 25.6 11.7 7.1 0 14.1-2.3 19.7-6.5l10.6-7.8c6.2-4.5 14.8-3.3 19.3 2.8 4.3 5.8 3.4 14-2.1 18.8-14.4 12.6-32.9 19.7-51.9 19.7-4.3 0-8.7-0.3-13-1-21.3-3.5-40.2-14.4-54.3-31.3l-0.3-0.3c-12.9-15.7-19.4-34.9-18.3-54.5 0.8-14.5 6.4-28.5 16.1-39.7 1.2-1.4 2.5-2.6 3.8-3.9l31.5-31.5c1.9-1.9 3.8-3.9 5.4-6.1 5.8-7.7 8.9-17 8.9-26.8 0-11.3-4.2-22.1-11.8-30.4-12.4-13.5-31.5-15.2-48.5-11.7-91.3 18.9-160.7 100.1-160.8 196.9 0 109.5 89.1 198.6 198.6 198.6 36.7 0 71.9-9.9 102.8-28.5 5.7-3.4 12.8-3.3 18.5 0.2 36.7 22.7 79.1 35.3 123.2 35.3z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M770.9 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M602.4 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\" fill=\"#82c91e\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- WeChat QR code popup -->\r\n          <div class=\"popup-container wechat-popup\" v-show=\"activePopup === 'wechat'\">\r\n            <div class=\"popup-content\">\r\n              <h3>微信扫码咨询客服</h3>\r\n              <div class=\"qr-code\">\r\n                <img :src=\"wechatQRCode\" alt=\"微信客服二维码\">\r\n              </div>\r\n<!--              <div class=\"popup-footer\">-->\r\n<!--                <button class=\"btn-consult\">桌面版微信点击咨询客服</button>-->\r\n<!--              </div>-->\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"icon-item\" @mouseenter=\"showPopup('contact')\" @mouseleave=\"hidePopup('contact')\">\r\n          <i class=\"iconfont icon-phone\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7c-20.6 47.8-49.6 90.6-86.4 127.3-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3-18.5 0-35.8 7.2-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-0.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4z\" fill=\"#1677ff\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- Contact information popup -->\r\n          <div class=\"popup-container contact-popup\" v-show=\"activePopup === 'contact'\">\r\n            <div class=\"popup-content\">\r\n              <h3>商务合作请联系电话</h3>\r\n              <p class=\"phone-number\">13913283376</p>\r\n              <p>使用问题请咨询微信客服</p>\r\n              <div class=\"qr-code\">\r\n                <img :src=\"contactQRCode\" alt=\"联系电话二维码\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"icon-item\" @click=\"showFeedbackModal\" @mouseenter=\"showTooltip\" @mouseleave=\"hideTooltip\">\r\n          <i class=\"iconfont icon-feedback\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zM293 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z\" fill=\"#fa8c16\"></path>\r\n              <path d=\"M894 345c-48.1-66-115.3-110.1-189-130v0.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l0.8 132.6c0 3.2 0.5 6.4 1.5 9.4 5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.4 11.9 68.4 18 104.7 18 72.2 0 143.1-21.4 205.3-61.8 57.4-37.3 104-89.9 131.8-150.1 23.9-52 35.2-106.8 33.6-160.9 28.8-29.9 50.4-64.7 63.6-103 17.8-52.7 13.1-108.7-14-155.1z m-68 106.7c-25.8 53.9-71.3 89.7-126.2 99.8-5.7 1-9.7 3.9-13 10.3-8.1 16.1-19.1 30.6-32.7 43.1-11.8 10.9-24.9 20.5-42.9 14.6-9.7-3.2-15.2-9.4-19.1-20.8-6.6-19.2-12.9-18.2-27.2-9.9-81.5 47.5-165.2 37.3-232.5-25.3-30.5-28.6-54.2-65.1-62.7-109-8.4-44 0.4-89.6 26.7-130.5 22.9-35.6 52.4-62 89.9-79.3 24.8-11.5 50.9-18.7 78.5-20.5 101.5-6.2 197.2 41.2 244.1 132.2 12.8 24.8 25.6 60.7 30.4 87.3 5.5 30.3 2.9 53.5-13.3 87z\" fill=\"#fa8c16\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- Tooltip for feedback icon -->\r\n          <div class=\"tooltip\" v-show=\"showFeedbackTooltip\">\r\n            反馈与建议\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- Feedback modal - Reduced size -->\r\n    <div class=\"modal-overlay\" v-if=\"showModal\" @click.self=\"closeFeedbackModal\">\r\n      <div class=\"feedback-modal\">\r\n        <div class=\"modal-header\">\r\n          <h3>反馈与建议</h3>\r\n          <span class=\"close-btn\" @click=\"closeFeedbackModal\">×</span>\r\n        </div>\r\n\r\n        <div class=\"modal-body\">\r\n          <div class=\"alert alert-warning\">\r\n            <i class=\"iconfont icon-warning\">\r\n              <svg viewBox=\"0 0 1024 1024\" width=\"16\" height=\"16\">\r\n                <path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z\" fill=\"#faad14\"></path>\r\n              </svg>\r\n            </i>\r\n            您的反馈我们将认真对待，不断优化产品功能和体验\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required\">问题类型：</label>\r\n            <div class=\"select-wrapper\">\r\n              <select v-model=\"feedback.type\" required>\r\n                <option value=\"\">请选择</option>\r\n                <option value=\"功能建议\">功能建议</option>\r\n                <option value=\"产品故障\">产品故障</option>\r\n                <option value=\"体验不佳\">体验不佳</option>\r\n                <option value=\"账户相关\">账户相关</option>\r\n                <option value=\"其他\">其他</option>\r\n              </select>\r\n            </div>\r\n            <p class=\"error-text\" v-if=\"!feedback.type && showErrors\">请选择问题类型</p>\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required\">问题描述：</label>\r\n            <textarea v-model=\"feedback.description\" placeholder=\"请输入\" required></textarea>\r\n            <p class=\"error-text\" v-if=\"!feedback.description && showErrors\">请输入问题描述</p>\r\n          </div>\r\n\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required1\">问题截图：</label>\r\n            <div\r\n                class=\"image-uploader\"\r\n                @click=\"triggerFileUpload\"\r\n                @dragover.prevent\r\n                @drop.prevent=\"onFileDrop\"\r\n            >\r\n              <input\r\n                  type=\"file\"\r\n                  ref=\"fileInput\"\r\n                  accept=\"image/*\"\r\n                  @change=\"onFileChange\"\r\n                  style=\"display: none\"\r\n              >\r\n              <div v-if=\"!feedback.image\" class=\"upload-placeholder\">\r\n                <i class=\"iconfont icon-upload\">\r\n                  <svg viewBox=\"0 0 1024 1024\" width=\"28\" height=\"28\">\r\n                    <path d=\"M518.3 459c-3.2-4.1-9.4-4.1-12.6 0l-112 141.7c-4.1 5.2-0.4 12.9 6.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z\" fill=\"#bfbfbf\"></path>\r\n                    <path d=\"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c26.1 6.9 49.6 22.5 66.3 43.8 16.4 21 25.4 45.9 26.3 72.3 1.1 33.9-11.4 66.5-34.9 90.7-23.6 24.4-55.3 37.7-89 37.7h-40c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C758.5 760 848 670.5 848 560c0-92.7-63.1-170.7-148.6-193.3z\" fill=\"#bfbfbf\"></path>\r\n                  </svg>\r\n                </i>\r\n                <p>点击/拖拽至此处添加图片</p>\r\n              </div>\r\n              <div v-else class=\"preview-container\">\r\n                <img :src=\"feedback.imagePreview\" alt=\"问题截图预览\" class=\"image-preview\">\r\n                <div class=\"remove-image\" @click.stop=\"removeImage\">×</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"modal-footer\">\r\n          <button class=\"btn btn-cancel\" @click=\"closeFeedbackModal\">取消</button>\r\n          <button class=\"btn btn-submit\" @click=\"confirmSubmit\">提交</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Confirmation Dialog -->\r\n    <div class=\"modal-overlay\" v-if=\"showConfirmation\">\r\n      <div class=\"confirmation-dialog\">\r\n        <div class=\"confirmation-icon\">\r\n          <svg viewBox=\"0 0 1024 1024\" width=\"32\" height=\"32\">\r\n            <path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z\" fill=\"#52c41a\"></path>\r\n          </svg>\r\n        </div>\r\n        <div class=\"confirmation-title\">提交成功</div>\r\n        <div class=\"confirmation-message\">感谢您的反馈，我们会尽快处理</div>\r\n        <div class=\"confirmation-actions\">\r\n          <button class=\"btn btn-primary\" @click=\"closeConfirmation\">确定</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Mider',\r\n  data() {\r\n    return {\r\n      activePopup: null,\r\n      showModal: false,\r\n      showErrors: false,\r\n      showFeedbackTooltip: false,\r\n      showConfirmation: false,\r\n      wechatQRCode: require('@/assets/images/footer/wechat.jpg'), // 替换为实际路径\r\n      contactQRCode: require('@/assets/images/footer/wechat.jpg'), // 替换为实际路径\r\n      feedback: {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    showPopup(type) {\r\n      this.activePopup = type;\r\n    },\r\n    hidePopup(type) {\r\n      if (this.activePopup === type) {\r\n        this.activePopup = null;\r\n      }\r\n    },\r\n    showTooltip() {\r\n      this.showFeedbackTooltip = true;\r\n    },\r\n    hideTooltip() {\r\n      this.showFeedbackTooltip = false;\r\n    },\r\n    showFeedbackModal() {\r\n      this.showModal = true;\r\n      this.showErrors = false;\r\n    },\r\n    closeFeedbackModal() {\r\n      this.showModal = false;\r\n      // Reset form\r\n      this.feedback = {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      };\r\n      this.showErrors = false;\r\n    },\r\n    triggerFileUpload() {\r\n      this.$refs.fileInput.click();\r\n    },\r\n    onFileChange(event) {\r\n      const file = event.target.files[0];\r\n      if (file) {\r\n        this.processImage(file);\r\n      }\r\n    },\r\n    onFileDrop(event) {\r\n      const file = event.dataTransfer.files[0];\r\n      if (file && file.type.match('image.*')) {\r\n        this.processImage(file);\r\n      }\r\n    },\r\n    processImage(file) {\r\n      if (file && file.type.match('image.*')) {\r\n        this.feedback.image = file;\r\n\r\n        // Create preview\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => {\r\n          this.feedback.imagePreview = e.target.result;\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    },\r\n    removeImage() {\r\n      this.feedback.image = null;\r\n      this.feedback.imagePreview = null;\r\n    },\r\n    confirmSubmit() {\r\n      // Validate form\r\n      this.showErrors = true;\r\n      if (!this.feedback.type || !this.feedback.description) {\r\n        return;\r\n      }\r\n\r\n      // Submit feedback\r\n      this.submitFeedback();\r\n    },\r\n    submitFeedback() {\r\n      // Here you would typically send the data to your backend\r\n\r\n      // Create FormData object if there's an image\r\n      if (this.feedback.image) {\r\n        const formData = new FormData();\r\n        formData.append('type', this.feedback.type);\r\n        formData.append('description', this.feedback.description);\r\n        formData.append('instanceId', this.feedback.instanceId);\r\n        formData.append('image', this.feedback.image);\r\n\r\n        // Send formData to your API\r\n        // this.$axios.post('/api/feedback', formData)\r\n      }\r\n\r\n      // Close feedback modal and show confirmation dialog\r\n      this.showModal = false;\r\n      this.showConfirmation = true;\r\n\r\n      // Reset form data\r\n      this.feedback = {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      };\r\n    },\r\n    closeConfirmation() {\r\n      this.showConfirmation = false;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.mider-container {\r\n  position: relative;\r\n\r\n}\r\n\r\n/* Sidebar styles */\r\n.mider-sidebar {\r\n  /* 原有定位属性 */\r\n  position: fixed;\r\n  right: 0;\r\n  top: 85%;             /* 当前纵向定位 */\r\n  transform: translateY(-50%);\r\n  z-index: 1000;\r\n\r\n  /* 新增尺寸控制 */\r\n  width: 42px;         /* 固定宽度 */\r\n  height: 50vh;         /* 视口高度的50% */\r\n  max-width: 90%;       /* 防溢出保护 */\r\n  max-height: 80vh;     /* 高度上限 */\r\n  min-height: 200px;    /* 高度下限 */\r\n  /*box-sizing: 0; !* 包含内边距 *!*/\r\n\r\n  /* 布局优化 */\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n\r\n.coupon-tag {\r\n  background-color: #ff6b6b;\r\n  color: white;\r\n  padding: 8px 12px;\r\n  border-radius: 8px 0 0 8px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.icon-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: white;\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.icon-item {\r\n  position: relative;\r\n  padding: 15px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.icon-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.icon-item i {\r\n  font-size: 24px;\r\n  color: #666;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.icon-item:hover i {\r\n  color: #1890ff;\r\n}\r\n\r\n.icon-item:hover i svg path {\r\n  fill: #1890ff;\r\n}\r\n\r\n/* Tooltip styles */\r\n.tooltip {\r\n  position: absolute;\r\n  left: -90px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background-color: #fff;\r\n  color: #333;\r\n  padding: 6px 10px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  z-index: 1000;\r\n}\r\n\r\n.tooltip:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: -6px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-width: 6px 0 6px 6px;\r\n  border-style: solid;\r\n  border-color: transparent transparent transparent white;\r\n}\r\n\r\n/* Popup styles */\r\n.popup-container {\r\n  position: absolute;\r\n  right: 60px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  width: 240px;\r\n  z-index: 1000;\r\n}\r\n\r\n.popup-container:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: -10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-width: 10px 0 10px 10px;\r\n  border-style: solid;\r\n  border-color: transparent transparent transparent white;\r\n}\r\n\r\n.popup-content {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.popup-content h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n  font-size: 16px;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.qr-code {\r\n  margin: 10px 0;\r\n}\r\n\r\n.qr-code img {\r\n  width: 150px;\r\n  height: 150px;\r\n}\r\n\r\n.phone-number {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin: 5px 0;\r\n}\r\n\r\n.popup-footer {\r\n  margin-top: 10px;\r\n}\r\n\r\n.btn-consult {\r\n  background-color: #f5f5f5;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 8px 16px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  color: #333;\r\n  width: 100%;\r\n}\r\n\r\n/* Modal styles - Reduced size */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1001;\r\n}\r\n\r\n.feedback-modal {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  width: 500px;\r\n  max-width: 100vw;\r\n  max-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.modal-header {\r\n  padding: 14px 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.modal-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  color: #333;\r\n}\r\n\r\n.close-btn {\r\n  cursor: pointer;\r\n  font-size: 20px;\r\n  color: #999;\r\n}\r\n\r\n.modal-body {\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.alert {\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 13px;\r\n}\r\n\r\n.alert-warning {\r\n  background-color: #fffbe6;\r\n  border: 1px solid #ffe58f;\r\n  font-size: 10px;\r\n  color: #d48806;\r\n}\r\n\r\n.alert i {\r\n  margin-right: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 6px;\r\n  font-size: 13px;\r\n  color: #333;\r\n}\r\n\r\n.required:before {\r\n  content: '*';\r\n  color: #ff4d4f;\r\n  margin-left: 4px;\r\n}\r\n.required1:before {\r\n  content: '';\r\n  color: #ff4d4f;\r\n  margin-left: 9px;\r\n}\r\n\r\ninput, select, textarea {\r\n  width: 100%;\r\n  padding: 8px 12px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  font-size: 13px;\r\n  transition: all 0.3s;\r\n}\r\n\r\ninput:focus, select:focus, textarea:focus {\r\n  outline: none;\r\n  border-color: #40a9ff;\r\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\ntextarea {\r\n  min-height: 70px;\r\n  resize: vertical;\r\n}\r\n\r\n.select-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.select-wrapper:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 12px;\r\n  color: #999;\r\n  pointer-events: none;\r\n}\r\n\r\n.error-text {\r\n  color: #ff4d4f;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.image-uploader {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 4px;\r\n  padding: 20px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  background-color: #fafafa;\r\n  min-height: 120px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.image-uploader:hover {\r\n  border-color: #40a9ff;\r\n}\r\n\r\n.upload-placeholder {\r\n  color: #999;\r\n}\r\n\r\n.upload-placeholder i {\r\n  font-size: 32px;\r\n  display: block;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.preview-container {\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.image-preview {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.remove-image {\r\n  position: absolute;\r\n  top: -10px;\r\n  right: -10px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.modal-footer {\r\n  padding: 10px 24px 24px;\r\n  text-align: right;\r\n}\r\n\r\n.btn {\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  border: none;\r\n}\r\n\r\n.btn-cancel {\r\n  background-color: white;\r\n  border: 1px solid #d9d9d9;\r\n  color: #333;\r\n  margin-right: 12px;\r\n}\r\n\r\n.btn-cancel:hover {\r\n  color: #40a9ff;\r\n  border-color: #40a9ff;\r\n}\r\n\r\n.btn-submit {\r\n  background-color: #1890ff;\r\n  color: white;\r\n}\r\n\r\n.btn-submit:hover {\r\n  background-color: #40a9ff;\r\n}\r\n\r\n/* For responsiveness */\r\n@media (max-width: 768px) {\r\n  .popup-container {\r\n    width: 200px;\r\n  }\r\n\r\n  .qr-code img {\r\n    width: 120px;\r\n    height: 120px;\r\n  }\r\n}\r\n\r\n.confirmation-dialog {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  /*margin-left: 50%;*/\r\n  /*text-align: left;*/\r\n  width: 500px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.confirmation-icon {\r\n  margin-left: 1%;\r\n  margin-bottom: -37px;\r\n}\r\n\r\n.confirmation-title {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  margin-left: 10%;\r\n  color: #333;\r\n}\r\n\r\n.confirmation-message {\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin-left: 10%;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.confirmation-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.btn-primary {\r\n  background-color: #1677ff;\r\n  /*margin-right: 2px;*/\r\n  color: white;\r\n  cursor: pointer;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 8px 32px;\r\n  font-size: 14px;\r\n  margin-left: 80%;\r\n}\r\n\r\n.btn-primary:hover {\r\n  background-color: #4096ff;\r\n}\r\n\r\n/* Form styles */\r\n.form-group {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.required:after {\r\n  content: '*';\r\n  color: #f5222d;\r\n  margin-left: 4px;\r\n}\r\n\r\n.select-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.select-wrapper:after {\r\n  content: '▼';\r\n  font-size: 10px;\r\n  color: #999;\r\n  position: absolute;\r\n  right: 10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  pointer-events: none;\r\n}\r\n\r\nselect {\r\n  width: 100%;\r\n  padding: 8px 10px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  background-color: white;\r\n  font-size: 14px;\r\n  appearance: none;\r\n}\r\n\r\ntextarea {\r\n  width: 100%;\r\n  padding: 8px 10px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  min-height: 100px;\r\n  resize: vertical;\r\n  font-size: 14px;\r\n}\r\n\r\n.error-text {\r\n  color: #f5222d;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.image-uploader {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  background-color: #fafafa;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.image-uploader:hover {\r\n  border-color: #1890ff;\r\n}\r\n\r\n.upload-placeholder {\r\n  color: #bfbfbf;\r\n}\r\n\r\n.upload-placeholder p {\r\n  margin-top: 8px;\r\n  font-size: 13px;\r\n}\r\n\r\n.preview-container {\r\n  position: relative;\r\n}\r\n\r\n.image-preview {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n}\r\n\r\n.remove-image {\r\n  position: absolute;\r\n  top: -10px;\r\n  right: -10px;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.modal-footer {\r\n  padding: 16px 20px;\r\n  border-top: 1px solid #f0f0f0;\r\n  text-align: right;\r\n}\r\n\r\n.btn {\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.btn-cancel {\r\n  background-color: white;\r\n  border: 1px solid #d9d9d9;\r\n  color: #666;\r\n  margin-right: 8px;\r\n}\r\n\r\n.btn-cancel:hover {\r\n  color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n.btn-submit {\r\n  background-color: #1677ff;\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.btn-submit:hover {\r\n  background-color: #4096ff;\r\n}\r\n</style>"], "mappings": "AAyKA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,UAAA;MACAC,mBAAA;MACAC,gBAAA;MACAC,YAAA,EAAAC,OAAA;MAAA;MACAC,aAAA,EAAAD,OAAA;MAAA;MACAE,QAAA;QACAC,IAAA;QACAC,WAAA;QACAC,UAAA;QACAC,KAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,UAAAN,IAAA;MACA,KAAAT,WAAA,GAAAS,IAAA;IACA;IACAO,UAAAP,IAAA;MACA,SAAAT,WAAA,KAAAS,IAAA;QACA,KAAAT,WAAA;MACA;IACA;IACAiB,YAAA;MACA,KAAAd,mBAAA;IACA;IACAe,YAAA;MACA,KAAAf,mBAAA;IACA;IACAgB,kBAAA;MACA,KAAAlB,SAAA;MACA,KAAAC,UAAA;IACA;IACAkB,mBAAA;MACA,KAAAnB,SAAA;MACA;MACA,KAAAO,QAAA;QACAC,IAAA;QACAC,WAAA;QACAC,UAAA;QACAC,KAAA;QACAC,YAAA;MACA;MACA,KAAAX,UAAA;IACA;IACAmB,kBAAA;MACA,KAAAC,KAAA,CAAAC,SAAA,CAAAC,KAAA;IACA;IACAC,aAAAC,KAAA;MACA,MAAAC,IAAA,GAAAD,KAAA,CAAAE,MAAA,CAAAC,KAAA;MACA,IAAAF,IAAA;QACA,KAAAG,YAAA,CAAAH,IAAA;MACA;IACA;IACAI,WAAAL,KAAA;MACA,MAAAC,IAAA,GAAAD,KAAA,CAAAM,YAAA,CAAAH,KAAA;MACA,IAAAF,IAAA,IAAAA,IAAA,CAAAlB,IAAA,CAAAwB,KAAA;QACA,KAAAH,YAAA,CAAAH,IAAA;MACA;IACA;IACAG,aAAAH,IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAlB,IAAA,CAAAwB,KAAA;QACA,KAAAzB,QAAA,CAAAI,KAAA,GAAAe,IAAA;;QAEA;QACA,MAAAO,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,MAAA,GAAAC,CAAA;UACA,KAAA7B,QAAA,CAAAK,YAAA,GAAAwB,CAAA,CAAAT,MAAA,CAAAU,MAAA;QACA;QACAJ,MAAA,CAAAK,aAAA,CAAAZ,IAAA;MACA;IACA;IACAa,YAAA;MACA,KAAAhC,QAAA,CAAAI,KAAA;MACA,KAAAJ,QAAA,CAAAK,YAAA;IACA;IACA4B,cAAA;MACA;MACA,KAAAvC,UAAA;MACA,UAAAM,QAAA,CAAAC,IAAA,UAAAD,QAAA,CAAAE,WAAA;QACA;MACA;;MAEA;MACA,KAAAgC,cAAA;IACA;IACAA,eAAA;MACA;;MAEA;MACA,SAAAlC,QAAA,CAAAI,KAAA;QACA,MAAA+B,QAAA,OAAAC,QAAA;QACAD,QAAA,CAAAE,MAAA,cAAArC,QAAA,CAAAC,IAAA;QACAkC,QAAA,CAAAE,MAAA,qBAAArC,QAAA,CAAAE,WAAA;QACAiC,QAAA,CAAAE,MAAA,oBAAArC,QAAA,CAAAG,UAAA;QACAgC,QAAA,CAAAE,MAAA,eAAArC,QAAA,CAAAI,KAAA;;QAEA;QACA;MACA;;MAEA;MACA,KAAAX,SAAA;MACA,KAAAG,gBAAA;;MAEA;MACA,KAAAI,QAAA;QACAC,IAAA;QACAC,WAAA;QACAC,UAAA;QACAC,KAAA;QACAC,YAAA;MACA;IACA;IACAiC,kBAAA;MACA,KAAA1C,gBAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}