{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport Layout from \"@/components/common/Layout-header\";\nimport chatAi from \"@/components/common/mider/chatAi\";\nimport { postAnyData, getNotAuth, postNotAuth, getAnyData, postJsonData } from \"@/api/login\";\nimport { formatDateTime } from \"@/utils/component\";\nimport { getToken } from '@/utils/auth';\nimport orderDetail from \"@/views/Product/OrderDetail\";\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\nexport default {\n  name: \"ProductView\",\n  components: {\n    Layout,\n    orderDetail,\n    chatAi,\n    SlideNotification\n  },\n  data() {\n    return {\n      orderTime: null,\n      price: null,\n      //记录当前用户选择计费方式\n      selectedBillingMethod: 'priceHour',\n      serverss: {},\n      showComingSoon: false,\n      notificationMessage: \"\",\n      showDetail: false,\n      showindex: 'priceHour',\n      // 筛选区域可见性\n      isFilterVisible: true,\n      // 当前悬浮的服务器ID\n      hoveredServer: null,\n      // 区域数据\n      regions: [{\n        id: '宁夏-B',\n        name: '宁夏-B'\n      }, {\n        id: '四川-A',\n        name: '四川-A'\n      }, {\n        id: '广东-B',\n        name: '广东-B'\n      }],\n      // GPU型号数据 - 作为所有可能的GPU型号库\n      allGpuModels: [{\n        id: 'a100-80g-nvlink',\n        name: 'A100-80G NVLink'\n      }, {\n        id: 'a800-80g-pcie',\n        name: 'A800-80G PCIe'\n      }, {\n        id: 'rtx4090-24g-pcie',\n        name: 'RTX4090-24G PCIe'\n      }, {\n        id: 'a100-80g-nvlink-2',\n        name: 'A100-80G NVLink'\n      }, {\n        id: 'h100-80g-nvlink',\n        name: 'H100-80G NVLink'\n      }, {\n        id: 'a800-80g-nvlink',\n        name: 'A800-80G NVLink'\n      }, {\n        id: 'h800-80g-nvlink',\n        name: 'H800-80G NVLink'\n      }],\n      // 服务器状态类型定义\n      serverStatuses: {\n        AVAILABLE: 'available',\n        // 资源充足\n        SHORTAGE: 'shortage',\n        // 资源紧张\n        UNAVAILABLE: 'unavailable' // 已售罄\n      },\n\n      // 筛选条件 - 改为单选的计费模式\n      filters: {\n        allRegions: true,\n        selectedRegions: [],\n        billingMethod: 'hourly',\n        // 改为单个字符串值\n        allGpuModels: true,\n        selectedGpuModels: [],\n        usageScenarios: {\n          development: true\n        }\n      },\n      // 服务器数据库 - 模拟从API获取的数据\n      allServers: [{\n        id: 1,\n        gpuModel: 'NVIDIA A100-80G NVLink',\n        gpuModelId: 'a100-80g-nvlink',\n        gpuCount: 8,\n        gpuMemory: 80,\n        vcpu: 112,\n        systemDisk: 50,\n        cloudDisk: null,\n        memory: 912,\n        pricePerHour: 69.76,\n        status: 'unavailable',\n        // 这里不再硬编码，而是从后端获取\n        regionId: 'ningxia-b'\n      }, {\n        id: 2,\n        gpuModel: 'NVIDIA A100-80G NVLink',\n        gpuModelId: 'a100-80g-nvlink',\n        gpuCount: 4,\n        gpuMemory: 80,\n        vcpu: 56,\n        systemDisk: 50,\n        cloudDisk: null,\n        memory: 456,\n        pricePerHour: 34.88,\n        status: 'shortage',\n        // 示例：资源紧张状态\n        regionId: 'ningxia-b'\n      }, {\n        id: 3,\n        gpuModel: 'NVIDIA A100-80G NVLink',\n        gpuModelId: 'a100-80g-nvlink',\n        gpuCount: 2,\n        gpuMemory: 80,\n        vcpu: 28,\n        systemDisk: 50,\n        cloudDisk: null,\n        memory: 228,\n        pricePerHour: 17.44,\n        status: 'available',\n        regionId: 'ningxia-b'\n      }, {\n        id: 4,\n        gpuModel: 'NVIDIA H100-80G NVLink',\n        gpuModelId: 'h100-80g-nvlink',\n        gpuCount: 8,\n        gpuMemory: 80,\n        vcpu: 128,\n        systemDisk: 100,\n        cloudDisk: 500,\n        memory: 1024,\n        pricePerHour: 99.88,\n        status: 'available',\n        regionId: 'guangdong-b'\n      }, {\n        id: 5,\n        gpuModel: 'NVIDIA RTX4090-24G PCIe',\n        gpuModelId: 'rtx4090-24g-pcie',\n        gpuCount: 4,\n        gpuMemory: 24,\n        vcpu: 48,\n        systemDisk: 50,\n        cloudDisk: null,\n        memory: 256,\n        pricePerHour: 18.56,\n        status: 'shortage',\n        regionId: 'sichuan-a'\n      }, {\n        id: 6,\n        gpuModel: 'NVIDIA A800-80G PCIe',\n        gpuModelId: 'a800-80g-pcie',\n        gpuCount: 4,\n        gpuMemory: 80,\n        vcpu: 56,\n        systemDisk: 50,\n        cloudDisk: null,\n        memory: 456,\n        pricePerHour: 32.64,\n        status: 'unavailable',\n        regionId: 'sichuan-a'\n      }]\n    };\n  },\n  computed: {\n    // 根据所选区域动态获取可用的GPU型号\n    availableGpuModels() {\n      // 确保有选择的区域\n      if (this.filters.selectedRegions.length === 0) {\n        return [];\n      }\n      // 根据所选区域筛选出可用的GPU型号ID\n      const availableGpuIds = this.allServers.filter(server => this.filters.selectedRegions.includes(server.region)).map(server => server.name);\n\n      // 使用Set去重\n      const uniqueGpuIds = [...new Set(availableGpuIds)];\n\n      // 返回可用的GPU型号完整信息\n      return this.allGpuModels.filter(gpu => uniqueGpuIds.includes(gpu.name));\n    },\n    // 根据筛选条件过滤服务器\n    filteredServers() {\n      // 如果首次加载，设置默认选中所有可用的GPU型号\n      if (this.filters.allGpuModels && this.filters.selectedGpuModels.length === 0 && this.availableGpuModels.length > 0) {\n        this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\n      }\n\n      // 如果没有选择可用区或GPU型号，则不显示服务器\n      if (this.filters.selectedRegions.length === 0 || this.filters.selectedGpuModels.length === 0) {\n        return [];\n      }\n      return this.allServers.filter(server => {\n        // 区域筛选\n        if (!this.filters.selectedRegions.includes(server.region)) {\n          return false;\n        }\n\n        // GPU型号筛选\n        if (!this.filters.selectedGpuModels.includes(server.name)) {\n          return false;\n        }\n\n        // 使用场景筛选\n        if (!this.filters.usageScenarios.development) {\n          return false;\n        }\n\n        // 所有服务器都支持所有计费方式，此处无需进行过滤\n        // 保留此注释作为业务逻辑说明\n\n        return true;\n      });\n    }\n  },\n  methods: {\n    closeOrderDetail() {\n      this.showDetail = false;\n    },\n    //接收订单时间\n    orderTimes(newval) {\n      this.orderTime = newval;\n    },\n    //接收订单价格\n    orderPirce(newval) {\n      this.price = newval;\n    },\n    async directToConsole() {\n      // 检查登录状态\n      if (!getToken()) {\n        this.$router.push('/login');\n        this.$toast.success(\"请先登录后再进行购买\");\n        return;\n      }\n\n      // 获取用户实名信息\n      try {\n        const res = await postAnyData(\"/logout/cilent/getInfo\");\n        if (res.data.code === 200) {\n          const isReal = res.data.data.isReal;\n\n          // 未实名认证处理\n          if (isReal !== 1) {\n            this.notificationMessage = \"请先完成实名认证，正在为您跳转到对应页面\";\n            this.showComingSoon = true;\n            setTimeout(() => {\n              this.$router.push({\n                path: '/personal',\n                query: {\n                  activeTab: 'verification'\n                }\n              });\n            }, 2000);\n            return;\n          }\n\n          // 已实名直接跳转\n          this.$router.push('/console');\n        }\n      } catch (error) {\n        this.$toast.error(\"获取用户信息失败\");\n        // console.error(error);\n      }\n    },\n\n    //购买Gpu\n    orderOK(serverinfo) {\n      // 再次检查库存，防止通过其他方式触发\n      if (serverinfo.inventoryNumber <= 0) {\n        this.$toast.error(\"该资源已售罄，无法购买\");\n        return;\n      }\n      if (!getToken()) {\n        this.$router.push('/login');\n        this.$toast.success(\"请先登录后再进行购买\");\n        return;\n      }\n      this.serverss = serverinfo;\n      this.selectedBillingMethod = this.showindex;\n      this.showDetail = true;\n    },\n    buyGpu(server) {\n      let params = {\n        id: server.id,\n        price: '',\n        name: server.name,\n        time: this.orderTime\n      };\n      params.price = this.price;\n      params.time = this.orderTime;\n      postJsonData(\"/system/parameter/decrease\", params).then(res => {\n        if (res.data.msg === '余额不足') {\n          this.$toast.error(\"余额不足,请前往充值\");\n          return;\n        }\n        if (res.data.msg === '库存不足') {\n          this.$toast.error(\"库存不足，请稍后再试\");\n          return;\n        }\n        this.$toast.success(\"租赁成功，请前往控制台使用\");\n      });\n    },\n    // 切换筛选区域的可见性\n    toggleFilterVisibility() {\n      this.isFilterVisible = !this.isFilterVisible;\n    },\n    // 动画钩子函数 - 进入动画开始前\n    beforeEnter(el) {\n      el.style.height = '0';\n      el.style.opacity = '0';\n      el.style.overflow = 'hidden';\n    },\n    // 进入动画中\n    enter(el) {\n      const height = el.scrollHeight;\n      // 使用 requestAnimationFrame 确保过渡平滑\n      requestAnimationFrame(() => {\n        el.style.height = height + 'px';\n        el.style.opacity = '1';\n      });\n    },\n    // 进入动画结束\n    afterEnter(el) {\n      // 移除高度限制，允许内容自然流动\n      el.style.height = '';\n      el.style.overflow = '';\n    },\n    // 离开动画开始前\n    beforeLeave(el) {\n      // 设置具体高度，为动画做准备\n      el.style.height = el.scrollHeight + 'px';\n      el.style.overflow = 'hidden';\n    },\n    // 离开动画中\n    leave(el) {\n      // 强制浏览器重排以确保动画正确开始\n      void el.offsetHeight;\n\n      // 使用 requestAnimationFrame 确保过渡平滑\n      requestAnimationFrame(() => {\n        el.style.height = '0';\n        el.style.opacity = '0';\n      });\n    },\n    // 离开动画结束\n    afterLeave(el) {\n      // 清理样式\n      el.style.height = '';\n      el.style.overflow = '';\n    },\n    // 全选/取消全选区域\n    toggleAllRegions() {\n      if (this.filters.allRegions) {\n        this.filters.selectedRegions = this.regions.map(region => region.name);\n      } else {\n        this.filters.selectedRegions = [];\n      }\n      this.updateFilters();\n    },\n    // 全选/取消全选GPU型号\n    toggleAllGpuModels() {\n      if (this.filters.allGpuModels) {\n        this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\n      } else {\n        this.filters.selectedGpuModels = [];\n      }\n      this.updateFilters();\n    },\n    // 更新筛选条件，重新加载GPU型号和服务器数据\n    updateFilters() {\n      // 同步全选状态\n      this.filters.allRegions = this.filters.selectedRegions.length === this.regions.length;\n\n      // 确保availableGpuModels更新后再检查全选状态\n      this.$nextTick(() => {\n        this.filters.allGpuModels = this.availableGpuModels.length > 0 && this.filters.selectedGpuModels.length === this.availableGpuModels.length;\n      });\n\n      // 在实际应用中，这里应该调用API重新获取数据\n      // this.fetchServers();\n    },\n\n    // 根据区域ID获取区域名称\n    getRegionName(regionId) {\n      const region = this.regions.find(r => r.id === regionId);\n      return region ? region.name : '未知区域';\n    },\n    // 获取服务器状态显示文本\n    getServerStatusText(status) {\n      if (status > 0 && status <= 3) {\n        status = 'shortage';\n      } else if (status > 3) {\n        status = 'available';\n      } else if (status == 0) {\n        status = 'unavailable';\n      }\n      switch (status) {\n        case this.serverStatuses.AVAILABLE:\n          return '资源充足';\n        case this.serverStatuses.SHORTAGE:\n          return '资源紧张';\n        case this.serverStatuses.UNAVAILABLE:\n          return '已售罄';\n        default:\n          return '未知状态';\n      }\n    },\n    // 获取服务器状态样式类\n    getServerStatusClass(status) {\n      if (status > 0 && status <= 3) {\n        status = 'shortage';\n      } else if (status > 3) {\n        status = 'available';\n      } else if (status == 0) {\n        status = 'unavailable';\n      }\n      switch (status) {\n        case this.serverStatuses.AVAILABLE:\n          return 'server-available';\n        case this.serverStatuses.SHORTAGE:\n          return 'server-shortage';\n        case this.serverStatuses.UNAVAILABLE:\n          return 'server-unavailable';\n        default:\n          return '';\n      }\n    },\n    // 从API获取服务器数据\n    fetchServers() {\n      //加载数据\n      let params;\n      getNotAuth(\"/auth/getGpuList\").then(respose => {\n        this.allServers = respose.data.data;\n        postNotAuth(\"/system/parameter/getGpu\").then(res => {\n          this.allGpuModels = res.data.data;\n          for (let i = 0; i < this.allGpuModels.length; i++) {\n            this.allGpuModels[i].id = this.allGpuModels[i].name;\n          }\n          postNotAuth(\"/system/parameter/getRegion\").then(region => {\n            this.regions = region.data.data;\n            for (let i = 0; i < this.regions.length; i++) {\n              this.regions[i].id = this.regions[i].region;\n              this.regions[i].name = this.regions[i].region;\n            }\n            this.toggleAllRegions();\n            this.toggleAllGpuModels();\n          });\n        });\n        params = {\n          regions: this.filters.selectedRegions,\n          gpuModels: this.filters.selectedGpuModels,\n          billingMethod: this.filters.billingMethod\n        };\n      });\n    }\n  },\n  created() {\n    // 在组件创建时获取服务器数据\n    this.fetchServers();\n    // 初始化选中所有可用的GPU型号\n    this.$nextTick(() => {\n      this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\n    });\n  },\n  watch: {\n    // 监听区域选择变化，同步更新GPU型号选择\n    'filters.selectedRegions': {\n      handler() {\n        // 当选择的区域变化时，使用新的可用GPU型号列表更新选中的GPU型号\n        if (this.filters.allGpuModels) {\n          this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\n        } else {\n          // 仅保留在新的可用列表中的GPU型号\n          const availableIds = this.availableGpuModels.map(gpu => gpu.id);\n          this.filters.selectedGpuModels = this.filters.selectedGpuModels.filter(id => availableIds.includes(id));\n        }\n      },\n      immediate: true\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "chatAi", "postAnyData", "getNotAuth", "postNotAuth", "getAnyData", "postJsonData", "formatDateTime", "getToken", "orderDetail", "SlideNotification", "name", "components", "data", "orderTime", "price", "selectedBillingMethod", "serverss", "showComingSoon", "notificationMessage", "showDetail", "showindex", "isFilterVisible", "hoveredServer", "regions", "id", "allGpuModels", "serverStatuses", "AVAILABLE", "SHORTAGE", "UNAVAILABLE", "filters", "allRegions", "selectedRegions", "billingMethod", "selectedGpuModels", "usageScenarios", "development", "allServers", "gpuModel", "gpuModelId", "gpuCount", "gpuMemory", "vcpu", "systemDisk", "cloudDisk", "memory", "pricePerHour", "status", "regionId", "computed", "availableGpuModels", "length", "availableGpuIds", "filter", "server", "includes", "region", "map", "uniqueGpuIds", "Set", "gpu", "filteredServers", "methods", "closeOrderDetail", "orderTimes", "newval", "orderPirce", "directToConsole", "$router", "push", "$toast", "success", "res", "code", "isReal", "setTimeout", "path", "query", "activeTab", "error", "orderOK", "serverinfo", "inventoryNumber", "buyGpu", "params", "time", "then", "msg", "toggleFilterVisibility", "beforeEnter", "el", "style", "height", "opacity", "overflow", "enter", "scrollHeight", "requestAnimationFrame", "afterEnter", "beforeLeave", "leave", "offsetHeight", "afterLeave", "toggleAllRegions", "updateFilters", "toggleAllGpuModels", "$nextTick", "getRegionName", "find", "r", "getServerStatusText", "getServerStatusClass", "fetchServers", "respose", "i", "gpuModels", "created", "watch", "handler", "availableIds", "immediate"], "sources": ["src/views/Product/ProductView.vue"], "sourcesContent": ["<template>\r\n  <div style=\"margin-left: 7%\">\r\n    <SlideNotification\r\n        v-if=\"showComingSoon\"\r\n        :message=\"notificationMessage\"\r\n        type=\"warning\"\r\n        :duration=\"2000\"\r\n        @close=\"showComingSoon = false\"\r\n    />\r\n    <div style=\"width: 95%\">\r\n      <!-- 导航占位符 -->\r\n      <div class=\"compute-market\">\r\n        <!-- 筛选条件区域 -->\r\n        <div class=\"filter-section\">\r\n          <div class=\"filter-header\" @click=\"toggleFilterVisibility\">\r\n            <span class=\"filter-title\">筛选</span>\r\n            <i class=\"filter-icon\" :class=\"{ 'collapsed': !isFilterVisible }\">{{ isFilterVisible ? '▼' : '►' }}</i>\r\n          </div>\r\n\r\n          <!-- 改进的过渡动画 -->\r\n          <transition\r\n              name=\"slide\"\r\n              @before-enter=\"beforeEnter\"\r\n              @enter=\"enter\"\r\n              @after-enter=\"afterEnter\"\r\n              @before-leave=\"beforeLeave\"\r\n              @leave=\"leave\"\r\n              @after-leave=\"afterLeave\">\r\n            <div v-if=\"isFilterVisible\" class=\"filter-content\" ref=\"filterContent\">\r\n              <!-- 内容保持不变 -->\r\n              <!-- 计费模式 - 改为单选按钮 -->\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-label\">计费模式</div>\r\n                <div class=\"filter-options checkbox-group\">\r\n                  <label class=\"radio-item\">\r\n                    <input type=\"radio\" v-model=\"showindex\" value=\"priceHour\" name=\"billing\">\r\n                    <span class=\"radio-item\"></span>\r\n                    <span class=\"radio-text\">按量</span>\r\n                  </label>\r\n                  <label class=\"radio-item\">\r\n                    <input type=\"radio\" v-model=\"showindex\" value=\"priceDay\" name=\"billing\">\r\n                    <span class=\"radio-item\"></span>\r\n                    <span class=\"radio-text\">包日</span>\r\n                  </label>\r\n                  <label class=\"radio-item\">\r\n                    <input type=\"radio\" v-model=\"showindex\" value=\"priceMouth\" name=\"billing\">\r\n                    <span class=\"radio-item\"></span>\r\n                    <span class=\"radio-text\">包月</span>\r\n                  </label>\r\n                  <label class=\"radio-item\">\r\n                    <input type=\"radio\" v-model=\"showindex\" value=\"priceYear\" name=\"billing\">\r\n                    <span class=\"radio-item\"></span>\r\n                    <span class=\"radio-text\">包年</span>\r\n                  </label>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 可用区筛选 -->\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-label\">选择可用区</div>\r\n                <div class=\"filter-options checkbox-group\">\r\n                  <label class=\"checkbox-item\">\r\n                    <input type=\"checkbox\" v-model=\"filters.allRegions\" @change=\"toggleAllRegions\">\r\n                    <span class=\"checkbox-text\">全选</span>\r\n                  </label>\r\n                  <label class=\"checkbox-item\" v-for=\"region in regions\" :key=\"region.id\">\r\n                    <input type=\"checkbox\" v-model=\"filters.selectedRegions\" :value=\"region.id\" @change=\"updateFilters\">\r\n                    <span class=\"checkbox-text\">{{ region.name }}</span>\r\n                  </label>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- GPU型号 -->\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-label\">GPU型号</div>\r\n                <div class=\"filter-options\">\r\n                  <div class=\"gpu-brand\">NVIDIA</div>\r\n                  <div v-if=\"availableGpuModels.length > 0\" class=\"checkbox-group gpu-list\">\r\n                    <label class=\"checkbox-item\">\r\n                      <input type=\"checkbox\" v-model=\"filters.allGpuModels\" @change=\"toggleAllGpuModels\">\r\n                      <span class=\"checkbox-text\">全选</span>\r\n                    </label>\r\n                    <label class=\"checkbox-item\" v-for=\"gpu in availableGpuModels\" :key=\"gpu.id\">\r\n                      <input type=\"checkbox\" v-model=\"filters.selectedGpuModels\" :value=\"gpu.id\" @change=\"updateFilters\">\r\n                      <span class=\"checkbox-text\">{{ gpu.name }}</span>\r\n                    </label>\r\n                  </div>\r\n                  <div v-else class=\"no-options-message\">\r\n                    当前筛选条件下无可用GPU型号\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 使用场景 -->\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-label\">使用场景</div>\r\n                <div class=\"filter-options checkbox-group\">\r\n                  <label class=\"checkbox-item\">\r\n                    <input type=\"checkbox\" v-model=\"filters.usageScenarios.development\">\r\n                    <span class=\"checkbox-text\">开发机</span>\r\n                  </label>\r\n                  <!-- 更多场景可以在这里添加 -->\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </transition>\r\n        </div>\r\n\r\n        <!-- 服务器列表 -->\r\n        <div v-if=\"filteredServers.length > 0\" class=\"servers-grid\">\r\n          <div\r\n              class=\"server-card\"\r\n              v-for=\"server in filteredServers\"\r\n              :key=\"server.id\"\r\n              @mouseenter=\"hoveredServer = server.id\"\r\n              @mouseleave=\"hoveredServer = null\"\r\n              :class=\"{ 'server-card-hovered': hoveredServer === server.id }\"\r\n          >\r\n            <!-- 区域标识放在右上角 -->\r\n            <div class=\"region-tag\">{{ getRegionName(server.region) }}</div>\r\n\r\n            <!-- 服务器标题 -->\r\n            <div class=\"server-title\">\r\n              {{ server.name }}\r\n            </div>\r\n\r\n            <!-- 价格和状态 -->\r\n            <div class=\"server-price-section\" v-show=\"showindex === 'priceHour'\">\r\n              <div class=\"price\">\r\n                <span class=\"currency\">¥</span>\r\n                <span class=\"amount\">{{ server.priceHour }}</span>\r\n                <span class=\"unit\">/小时</span>\r\n              </div>\r\n              <!-- 使用动态生成的状态文本和CSS类 -->\r\n              <div class=\"server-status\" :class=\"getServerStatusClass(server.inventoryNumber)\">\r\n                {{ getServerStatusText(server.inventoryNumber) }}\r\n              </div>\r\n            </div>\r\n            <div class=\"server-price-section\" v-show=\"showindex === 'priceDay'\">\r\n              <div class=\"price\">\r\n                <span class=\"currency\">¥</span>\r\n                <span class=\"amount\">{{ server.priceDay }}</span>\r\n                <span class=\"unit\">/天</span>\r\n              </div>\r\n              <!-- 使用动态生成的状态文本和CSS类 -->\r\n              <div class=\"server-status\" :class=\"getServerStatusClass(server.inventoryNumber)\">\r\n                {{ getServerStatusText(server.inventoryNumber) }}\r\n              </div>\r\n            </div>\r\n            <div class=\"server-price-section\" v-show=\"showindex === 'priceMouth'\">\r\n              <div class=\"price\">\r\n                <span class=\"currency\">¥</span>\r\n                <span class=\"amount\">{{ server.priceMouth }}</span>\r\n                <span class=\"unit\">/月</span>\r\n              </div>\r\n              <!-- 使用动态生成的状态文本和CSS类 -->\r\n              <div class=\"server-status\" :class=\"getServerStatusClass(server.inventoryNumber)\">\r\n                {{ getServerStatusText(server.inventoryNumber) }}\r\n              </div>\r\n            </div>\r\n            <div class=\"server-price-section\" v-show=\"showindex === 'priceYear'\">\r\n              <div class=\"price\">\r\n                <span class=\"currency\">¥</span>\r\n                <span class=\"amount\">{{ server.priceYear }}</span>\r\n                <span class=\"unit\">/年</span>\r\n              </div>\r\n              <!-- 使用动态生成的状态文本和CSS类 -->\r\n              <div class=\"server-status\" :class=\"getServerStatusClass(server.inventoryNumber)\">\r\n                {{ getServerStatusText(server.inventoryNumber) }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 服务器规格 - 修改为垂直对齐的布局 -->\r\n            <div class=\"server-specs\">\r\n              <div class=\"specs-grid\">\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">显卡数量</div>\r\n                  <div class=\"spec-value\">{{ server.graphicsCardNumber }}</div>\r\n                </div>\r\n\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">显存(GB)</div>\r\n                  <div class=\"spec-value\">{{ server.videoMemory }}</div>\r\n                </div>\r\n\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">VCPU核数</div>\r\n                  <div class=\"spec-value\">{{ server.gpuNuclearNumber }}</div>\r\n                </div>\r\n\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">系统盘(GB)</div>\r\n                  <div class=\"spec-value\">{{ server.systemDisk }}</div>\r\n                </div>\r\n\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">云盘(GB)</div>\r\n                  <div class=\"spec-value\">{{ server.cloudDisk || '-' }}</div>\r\n                </div>\r\n\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">内存(GB)</div>\r\n                  <div class=\"spec-value\">{{ server.internalMemory }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 购买按钮状态根据服务器状态动态调整 -->\r\n            <!--            <button-->\r\n            <!--                class=\"buy-button\"-->\r\n            <!--                :class=\"{-->\r\n            <!--      'disabled': server.inventoryNumber === 0,-->\r\n            <!--      'buy-button-hovered': hoveredServer === server.id-->\r\n            <!--    }\"-->\r\n            <!--                @click=\"server.inventoryNumber > 0 ? orderOK(server) : null\"-->\r\n            <!--            >-->\r\n            <!--              立即租赁-->\r\n            <!--            </button>-->\r\n\r\n            <button\r\n                class=\"buy-button\"\r\n                :class=\"{\r\n      'disabled': server.inventoryNumber === 0,\r\n      'buy-button-hovered': hoveredServer === server.id\r\n    }\"\r\n                @click=\"server.inventoryNumber > 0 ? directToConsole() : null\"\r\n            >\r\n              立即租赁\r\n            </button>\r\n\r\n          </div>\r\n        </div>\r\n        <!-- 空状态提示 -->\r\n        <div v-else class=\"empty-state\">\r\n          <div class=\"empty-state-icon\">\r\n            <!--            <img src=\"/path/to/empty-state-icon.png\" alt=\"暂无数据\" />-->\r\n          </div>\r\n          <div class=\"empty-state-text\">暂无数据</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <order-detail :visible=\"showDetail\" :server=\"serverss\" :selectedBillingMethod=\"selectedBillingMethod\" @orderSubmitted=\"buyGpu(serverss)\" @price-updated=\"orderPirce\" @time-updated=\"orderTimes\" @close=\"closeOrderDetail\"></order-detail>\r\n    <chatAi></chatAi>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout-header\";\r\nimport chatAi from \"@/components/common/mider/chatAi\";\r\nimport {postAnyData, getNotAuth, postNotAuth,getAnyData,postJsonData} from \"@/api/login\";\r\nimport {formatDateTime} from \"@/utils/component\"\r\nimport {getToken} from '@/utils/auth'\r\nimport orderDetail from \"@/views/Product/OrderDetail\";\r\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\r\n\r\n\r\nexport default {\r\n  name: \"ProductView\",\r\n  components: { Layout,orderDetail,chatAi,SlideNotification },\r\n  data() {\r\n    return {\r\n      orderTime:null,\r\n      price:null,\r\n      //记录当前用户选择计费方式\r\n      selectedBillingMethod:'priceHour',\r\n      serverss: {},\r\n      showComingSoon: false,\r\n      notificationMessage: \"\",\r\n      showDetail:false,\r\n      showindex:'priceHour',\r\n      // 筛选区域可见性\r\n      isFilterVisible: true,\r\n\r\n      // 当前悬浮的服务器ID\r\n      hoveredServer: null,\r\n\r\n      // 区域数据\r\n      regions: [\r\n        { id: '宁夏-B', name: '宁夏-B' },\r\n        { id: '四川-A', name: '四川-A' },\r\n        { id: '广东-B', name: '广东-B' }\r\n      ],\r\n\r\n      // GPU型号数据 - 作为所有可能的GPU型号库\r\n      allGpuModels: [\r\n        { id: 'a100-80g-nvlink', name: 'A100-80G NVLink' },\r\n        { id: 'a800-80g-pcie', name: 'A800-80G PCIe' },\r\n        { id: 'rtx4090-24g-pcie', name: 'RTX4090-24G PCIe' },\r\n        { id: 'a100-80g-nvlink-2', name: 'A100-80G NVLink' },\r\n        { id: 'h100-80g-nvlink', name: 'H100-80G NVLink' },\r\n        { id: 'a800-80g-nvlink', name: 'A800-80G NVLink' },\r\n        { id: 'h800-80g-nvlink', name: 'H800-80G NVLink' }\r\n      ],\r\n\r\n      // 服务器状态类型定义\r\n      serverStatuses: {\r\n        AVAILABLE: 'available',     // 资源充足\r\n        SHORTAGE: 'shortage',       // 资源紧张\r\n        UNAVAILABLE: 'unavailable'  // 已售罄\r\n      },\r\n\r\n      // 筛选条件 - 改为单选的计费模式\r\n      filters: {\r\n        allRegions: true,\r\n        selectedRegions: [],\r\n        billingMethod: 'hourly', // 改为单个字符串值\r\n        allGpuModels: true,\r\n        selectedGpuModels: [],\r\n        usageScenarios: {\r\n          development: true\r\n        }\r\n      },\r\n\r\n      // 服务器数据库 - 模拟从API获取的数据\r\n      allServers: [\r\n        {\r\n          id: 1,\r\n          gpuModel: 'NVIDIA A100-80G NVLink',\r\n          gpuModelId: 'a100-80g-nvlink',\r\n          gpuCount: 8,\r\n          gpuMemory: 80,\r\n          vcpu: 112,\r\n          systemDisk: 50,\r\n          cloudDisk: null,\r\n          memory: 912,\r\n          pricePerHour: 69.76,\r\n          status: 'unavailable',  // 这里不再硬编码，而是从后端获取\r\n          regionId: 'ningxia-b'\r\n        },\r\n        {\r\n          id: 2,\r\n          gpuModel: 'NVIDIA A100-80G NVLink',\r\n          gpuModelId: 'a100-80g-nvlink',\r\n          gpuCount: 4,\r\n          gpuMemory: 80,\r\n          vcpu: 56,\r\n          systemDisk: 50,\r\n          cloudDisk: null,\r\n          memory: 456,\r\n          pricePerHour: 34.88,\r\n          status: 'shortage',  // 示例：资源紧张状态\r\n          regionId: 'ningxia-b'\r\n        },\r\n        {\r\n          id: 3,\r\n          gpuModel: 'NVIDIA A100-80G NVLink',\r\n          gpuModelId: 'a100-80g-nvlink',\r\n          gpuCount: 2,\r\n          gpuMemory: 80,\r\n          vcpu: 28,\r\n          systemDisk: 50,\r\n          cloudDisk: null,\r\n          memory: 228,\r\n          pricePerHour: 17.44,\r\n          status: 'available',\r\n          regionId: 'ningxia-b'\r\n        },\r\n        {\r\n          id: 4,\r\n          gpuModel: 'NVIDIA H100-80G NVLink',\r\n          gpuModelId: 'h100-80g-nvlink',\r\n          gpuCount: 8,\r\n          gpuMemory: 80,\r\n          vcpu: 128,\r\n          systemDisk: 100,\r\n          cloudDisk: 500,\r\n          memory: 1024,\r\n          pricePerHour: 99.88,\r\n          status: 'available',\r\n          regionId: 'guangdong-b'\r\n        },\r\n        {\r\n          id: 5,\r\n          gpuModel: 'NVIDIA RTX4090-24G PCIe',\r\n          gpuModelId: 'rtx4090-24g-pcie',\r\n          gpuCount: 4,\r\n          gpuMemory: 24,\r\n          vcpu: 48,\r\n          systemDisk: 50,\r\n          cloudDisk: null,\r\n          memory: 256,\r\n          pricePerHour: 18.56,\r\n          status: 'shortage',\r\n          regionId: 'sichuan-a'\r\n        },\r\n        {\r\n          id: 6,\r\n          gpuModel: 'NVIDIA A800-80G PCIe',\r\n          gpuModelId: 'a800-80g-pcie',\r\n          gpuCount: 4,\r\n          gpuMemory: 80,\r\n          vcpu: 56,\r\n          systemDisk: 50,\r\n          cloudDisk: null,\r\n          memory: 456,\r\n          pricePerHour: 32.64,\r\n          status: 'unavailable',\r\n          regionId: 'sichuan-a'\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    // 根据所选区域动态获取可用的GPU型号\r\n    availableGpuModels() {\r\n      // 确保有选择的区域\r\n      if (this.filters.selectedRegions.length === 0) {\r\n        return [];\r\n      }\r\n      // 根据所选区域筛选出可用的GPU型号ID\r\n      const availableGpuIds = this.allServers\r\n          .filter(server => this.filters.selectedRegions.includes(server.region))\r\n          .map(server => server.name);\r\n\r\n      // 使用Set去重\r\n      const uniqueGpuIds = [...new Set(availableGpuIds)];\r\n\r\n      // 返回可用的GPU型号完整信息\r\n      return this.allGpuModels.filter(gpu => uniqueGpuIds.includes(gpu.name));\r\n    },\r\n\r\n    // 根据筛选条件过滤服务器\r\n    filteredServers() {\r\n      // 如果首次加载，设置默认选中所有可用的GPU型号\r\n      if (this.filters.allGpuModels && this.filters.selectedGpuModels.length === 0 && this.availableGpuModels.length > 0) {\r\n        this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\r\n      }\r\n\r\n      // 如果没有选择可用区或GPU型号，则不显示服务器\r\n      if (this.filters.selectedRegions.length === 0 || this.filters.selectedGpuModels.length === 0) {\r\n        return [];\r\n      }\r\n\r\n      return this.allServers.filter(server => {\r\n        // 区域筛选\r\n        if (!this.filters.selectedRegions.includes(server.region)) {\r\n          return false;\r\n        }\r\n\r\n        // GPU型号筛选\r\n        if (!this.filters.selectedGpuModels.includes(server.name)) {\r\n          return false;\r\n        }\r\n\r\n        // 使用场景筛选\r\n        if (!this.filters.usageScenarios.development) {\r\n          return false;\r\n        }\r\n\r\n        // 所有服务器都支持所有计费方式，此处无需进行过滤\r\n        // 保留此注释作为业务逻辑说明\r\n\r\n        return true;\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    closeOrderDetail(){\r\n      this.showDetail = false\r\n    },\r\n    //接收订单时间\r\n    orderTimes(newval){\r\n      this.orderTime = newval\r\n    },\r\n    //接收订单价格\r\n    orderPirce(newval){\r\n      this.price = newval\r\n    },\r\n    async directToConsole() {\r\n      // 检查登录状态\r\n      if (!getToken()) {\r\n        this.$router.push('/login');\r\n        this.$toast.success(\"请先登录后再进行购买\");\r\n        return;\r\n      }\r\n\r\n      // 获取用户实名信息\r\n      try {\r\n        const res = await postAnyData(\"/logout/cilent/getInfo\");\r\n        if (res.data.code === 200) {\r\n          const isReal = res.data.data.isReal;\r\n\r\n          // 未实名认证处理\r\n          if (isReal !== 1) {\r\n            this.notificationMessage = \"请先完成实名认证，正在为您跳转到对应页面\";\r\n            this.showComingSoon = true;\r\n\r\n            setTimeout(() => {\r\n              this.$router.push({\r\n                path: '/personal',\r\n                query: { activeTab: 'verification' }\r\n              });\r\n            }, 2000);\r\n            return;\r\n          }\r\n\r\n          // 已实名直接跳转\r\n          this.$router.push('/console');\r\n        }\r\n      } catch (error) {\r\n        this.$toast.error(\"获取用户信息失败\");\r\n        // console.error(error);\r\n      }\r\n    },\r\n    //购买Gpu\r\n    orderOK(serverinfo) {\r\n      // 再次检查库存，防止通过其他方式触发\r\n      if (serverinfo.inventoryNumber <= 0) {\r\n        this.$toast.error(\"该资源已售罄，无法购买\");\r\n        return;\r\n      }\r\n\r\n      if(!getToken()) {\r\n        this.$router.push('/login');\r\n        this.$toast.success(\"请先登录后再进行购买\");\r\n        return;\r\n      }\r\n\r\n      this.serverss = serverinfo;\r\n      this.selectedBillingMethod = this.showindex;\r\n      this.showDetail = true;\r\n    },\r\n\r\n    buyGpu(server){\r\n      let params = {\r\n        id:server.id,\r\n        price:'',\r\n        name:server.name,\r\n        time:this.orderTime\r\n      }\r\n      params.price = this.price\r\n      params.time = this.orderTime\r\n      postJsonData(\"/system/parameter/decrease\",params).then(res =>{\r\n        if (res.data.msg === '余额不足'){\r\n          this.$toast.error(\"余额不足,请前往充值\")\r\n          return\r\n        }\r\n        if (res.data.msg === '库存不足'){\r\n          this.$toast.error(\"库存不足，请稍后再试\")\r\n          return;\r\n        }\r\n        this.$toast.success(\"租赁成功，请前往控制台使用\")\r\n      })\r\n    },\r\n    // 切换筛选区域的可见性\r\n    toggleFilterVisibility() {\r\n      this.isFilterVisible = !this.isFilterVisible;\r\n    },\r\n\r\n    // 动画钩子函数 - 进入动画开始前\r\n    beforeEnter(el) {\r\n      el.style.height = '0';\r\n      el.style.opacity = '0';\r\n      el.style.overflow = 'hidden';\r\n    },\r\n\r\n    // 进入动画中\r\n    enter(el) {\r\n      const height = el.scrollHeight;\r\n      // 使用 requestAnimationFrame 确保过渡平滑\r\n      requestAnimationFrame(() => {\r\n        el.style.height = height + 'px';\r\n        el.style.opacity = '1';\r\n      });\r\n    },\r\n\r\n    // 进入动画结束\r\n    afterEnter(el) {\r\n      // 移除高度限制，允许内容自然流动\r\n      el.style.height = '';\r\n      el.style.overflow = '';\r\n    },\r\n\r\n    // 离开动画开始前\r\n    beforeLeave(el) {\r\n      // 设置具体高度，为动画做准备\r\n      el.style.height = el.scrollHeight + 'px';\r\n      el.style.overflow = 'hidden';\r\n    },\r\n\r\n    // 离开动画中\r\n    leave(el) {\r\n      // 强制浏览器重排以确保动画正确开始\r\n      void el.offsetHeight;\r\n\r\n      // 使用 requestAnimationFrame 确保过渡平滑\r\n      requestAnimationFrame(() => {\r\n        el.style.height = '0';\r\n        el.style.opacity = '0';\r\n      });\r\n    },\r\n\r\n    // 离开动画结束\r\n    afterLeave(el) {\r\n      // 清理样式\r\n      el.style.height = '';\r\n      el.style.overflow = '';\r\n    },\r\n\r\n    // 全选/取消全选区域\r\n    toggleAllRegions() {\r\n      if (this.filters.allRegions) {\r\n        this.filters.selectedRegions = this.regions.map(region => region.name);\r\n      } else {\r\n        this.filters.selectedRegions = [];\r\n      }\r\n      this.updateFilters();\r\n    },\r\n\r\n    // 全选/取消全选GPU型号\r\n    toggleAllGpuModels() {\r\n      if (this.filters.allGpuModels) {\r\n        this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\r\n      } else {\r\n        this.filters.selectedGpuModels = [];\r\n      }\r\n      this.updateFilters();\r\n    },\r\n\r\n    // 更新筛选条件，重新加载GPU型号和服务器数据\r\n    updateFilters() {\r\n      // 同步全选状态\r\n      this.filters.allRegions = this.filters.selectedRegions.length === this.regions.length;\r\n\r\n      // 确保availableGpuModels更新后再检查全选状态\r\n      this.$nextTick(() => {\r\n        this.filters.allGpuModels = this.availableGpuModels.length > 0 &&\r\n            this.filters.selectedGpuModels.length === this.availableGpuModels.length;\r\n      });\r\n\r\n      // 在实际应用中，这里应该调用API重新获取数据\r\n      // this.fetchServers();\r\n    },\r\n\r\n    // 根据区域ID获取区域名称\r\n    getRegionName(regionId) {\r\n      const region = this.regions.find(r => r.id === regionId);\r\n      return region ? region.name : '未知区域';\r\n    },\r\n\r\n    // 获取服务器状态显示文本\r\n    getServerStatusText(status) {\r\n      if (status>0 && status<=3){\r\n        status = 'shortage'\r\n      }else if(status>3){\r\n        status = 'available'\r\n      }else if (status ==0){\r\n        status = 'unavailable'\r\n      }\r\n      switch(status) {\r\n        case this.serverStatuses.AVAILABLE:\r\n          return '资源充足';\r\n        case this.serverStatuses.SHORTAGE:\r\n          return '资源紧张';\r\n        case this.serverStatuses.UNAVAILABLE:\r\n          return '已售罄';\r\n        default:\r\n          return '未知状态';\r\n      }\r\n    },\r\n\r\n    // 获取服务器状态样式类\r\n    getServerStatusClass(status) {\r\n      if (status>0 && status<=3){\r\n        status = 'shortage'\r\n      }else if(status>3){\r\n        status = 'available'\r\n      }else if (status ==0){\r\n        status = 'unavailable'\r\n      }\r\n      switch(status) {\r\n        case this.serverStatuses.AVAILABLE:\r\n          return 'server-available';\r\n        case this.serverStatuses.SHORTAGE:\r\n          return 'server-shortage';\r\n        case this.serverStatuses.UNAVAILABLE:\r\n          return 'server-unavailable';\r\n        default:\r\n          return '';\r\n      }\r\n    },\r\n\r\n    // 从API获取服务器数据\r\n    fetchServers() {\r\n      //加载数据\r\n      let params;\r\n      getNotAuth(\"/auth/getGpuList\").then(respose =>{\r\n        this.allServers = respose.data.data;\r\n        postNotAuth(\"/system/parameter/getGpu\").then(res =>{\r\n          this.allGpuModels = res.data.data;\r\n          for (let i =0;i<this.allGpuModels.length;i++){\r\n            this.allGpuModels[i].id = this.allGpuModels[i].name\r\n          }\r\n          postNotAuth(\"/system/parameter/getRegion\").then(region =>{\r\n            this.regions = region.data.data\r\n            for (let i =0;i<this.regions.length;i++){\r\n              this.regions[i].id = this.regions[i].region\r\n              this.regions[i].name = this.regions[i].region\r\n            }\r\n            this.toggleAllRegions()\r\n            this.toggleAllGpuModels()\r\n          })\r\n        })\r\n        params = {\r\n          regions: this.filters.selectedRegions,\r\n          gpuModels: this.filters.selectedGpuModels,\r\n          billingMethod: this.filters.billingMethod\r\n        };\r\n      })\r\n    }\r\n  },\r\n  created() {\r\n    // 在组件创建时获取服务器数据\r\n    this.fetchServers();\r\n    // 初始化选中所有可用的GPU型号\r\n    this.$nextTick(() => {\r\n      this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\r\n    });\r\n  },\r\n  watch: {\r\n    // 监听区域选择变化，同步更新GPU型号选择\r\n    'filters.selectedRegions': {\r\n      handler() {\r\n        // 当选择的区域变化时，使用新的可用GPU型号列表更新选中的GPU型号\r\n        if (this.filters.allGpuModels) {\r\n          this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\r\n        } else {\r\n          // 仅保留在新的可用列表中的GPU型号\r\n          const availableIds = this.availableGpuModels.map(gpu => gpu.id);\r\n          this.filters.selectedGpuModels = this.filters.selectedGpuModels.filter(id => availableIds.includes(id));\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 基础样式 */\r\n.compute-market {\r\n  font-family: Arial, sans-serif;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n  color: #333;\r\n}\r\n\r\n/* 筛选区域样式 */\r\n.filter-section {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  padding: 8px 16px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.filter-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 6px 0;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  min-height: 32px;\r\n}\r\n\r\n.filter-title {\r\n  font-size: 15px;\r\n  font-weight: 400;\r\n  color: #333;\r\n}\r\n\r\n.filter-icon {\r\n  color: #409eff;\r\n  margin-left: 8px;\r\n  font-size: 12px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.filter-icon.collapsed {\r\n  transform: rotate(-90deg);\r\n}\r\n\r\n/* 改进过渡动画效果 */\r\n.slide-enter-active,\r\n.slide-leave-active {\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  max-height: 800px;\r\n  overflow: hidden;\r\n}\r\n\r\n.slide-enter-from,\r\n.slide-leave-to {\r\n  max-height: 0;\r\n  opacity: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n.filter-content {\r\n  overflow: hidden;\r\n}\r\n\r\n.filter-row {\r\n  display: flex;\r\n  padding: 10px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  flex-direction: column;\r\n}\r\n\r\n.filter-row:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.filter-label {\r\n  width: 100%;\r\n  color: #333;\r\n  font-size: 13px;\r\n  font-weight: 400;\r\n  padding-top: 2px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.filter-options {\r\n  flex: 1;\r\n}\r\n\r\n.checkbox-group, .radio-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12px;\r\n}\r\n\r\n.checkbox-item, .radio-item {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  min-width: 2px;\r\n}\r\n\r\n.checkbox-item input[type=\"checkbox\"], .radio-item input[type=\"radio\"] {\r\n  margin-right: 5px;\r\n  width: 14px;\r\n  height: 14px;\r\n  accent-color: #2196f3;\r\n  cursor: pointer;\r\n}\r\n\r\n.checkbox-text, .radio-text {\r\n  font-size: 12px;\r\n  font-weight: 400;\r\n  margin-bottom: -5px;\r\n}\r\n\r\n.gpu-brand {\r\n  font-size: 13px;\r\n  font-weight: 400;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.gpu-list {\r\n  margin-top: 6px;\r\n}\r\n\r\n/* 服务器卡片网格 - 响应式布局 */\r\n.servers-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n/* 服务器卡片样式 */\r\n.server-card {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n  padding: 16px;\r\n  border: 1px solid #f0f0f0;\r\n  transition: all 0.2s ease;\r\n  position: relative;\r\n}\r\n\r\n.server-card-hovered {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(128, 65, 255, 0.2);\r\n}\r\n\r\n/* 区域标签 */\r\n.region-tag {\r\n  position: absolute;\r\n  top: 16px;\r\n  right: 16px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #666;\r\n}\r\n\r\n.server-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin-bottom: 12px;\r\n  height: 3em;\r\n  color: #333;\r\n  padding-right: 60px;\r\n}\r\n\r\n.server-price-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.price {\r\n  display: flex;\r\n  align-items: baseline;\r\n}\r\n\r\n.currency {\r\n  font-size: 16px;\r\n  color: #2196f3;\r\n  font-weight: 500;\r\n}\r\n\r\n.amount {\r\n  font-size: 26px;\r\n  font-weight: bold;\r\n  color: #2196f3;\r\n  margin: 0 2px;\r\n}\r\n\r\n.unit {\r\n  font-size: 14px;\r\n  color: #2196f3;\r\n}\r\n\r\n.server-status {\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 资源充足状态 */\r\n.server-available {\r\n  color: #52c41a;\r\n  background-color: #f6ffed;\r\n  border: 1px solid #b7eb8f;\r\n}\r\n\r\n/* 资源紧张状态 */\r\n.server-shortage {\r\n  color: #fa8c16;\r\n  background-color: #fff7e6;\r\n  border: 1px solid #ffd591;\r\n}\r\n\r\n/* 已售罄状态 */\r\n.server-unavailable {\r\n  color: #333;\r\n  background-color: #f5f5f5;\r\n  border: 1px solid #d9d9d9;\r\n}\r\n\r\n/* 服务器规格 - 响应式网格布局 */\r\n.server-specs {\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  padding: 12px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.specs-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 12px;\r\n}\r\n\r\n.spec-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n}\r\n\r\n.spec-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.spec-value {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n/* 购买按钮 */\r\n.buy-button {\r\n  width: 100%;\r\n  padding: 10px 0;\r\n  background-color: white;\r\n  color: #333;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  text-align: center;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.buy-button.disabled {\r\n  background-color: #f5f5f5;\r\n  color: #999;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.buy-button-hovered:not(.disabled) {\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border-color: #2196f3;\r\n}\r\n\r\n/* 空状态提示 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 60px 0;\r\n  color: #999;\r\n}\r\n\r\n.empty-state-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.empty-state-icon img {\r\n  width: 80px;\r\n  height: 80px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.empty-state-text {\r\n  font-size: 16px;\r\n  color: #999;\r\n}\r\n\r\n/* 移动端适配 */\r\n@media (max-width: 768px) {\r\n  .compute-market {\r\n    padding: 10px;\r\n  }\r\n\r\n  .filter-section {\r\n    padding: 6px 12px;\r\n  }\r\n\r\n  .filter-row {\r\n    padding: 8px 0;\r\n  }\r\n\r\n  .checkbox-group, .radio-group {\r\n    gap: 8px;\r\n  }\r\n\r\n  .radio-item {\r\n    margin-right: -15px; /* 移动端减小间距 */\r\n  }\r\n\r\n  .radio-item input[type=\"radio\"] {\r\n    margin-right: -5px; /* 移动端进一步减小间距 */\r\n  }\r\n\r\n  .radio-text {\r\n    font-size: 11px; /* 移动端减小字体 */\r\n    margin-left: -30px; /* 移动端减小间距 */\r\n  }\r\n  .checkbox-item {\r\n    min-width: 70px;\r\n  }\r\n\r\n  .servers-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 12px;\r\n  }\r\n\r\n  .server-card {\r\n    padding: 12px;\r\n  }\r\n\r\n  .specs-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n  .price .amount {\r\n    font-size: 22px;\r\n  }\r\n\r\n  .price .unit {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .server-title {\r\n    font-size: 15px;\r\n  }\r\n\r\n  .buy-button {\r\n    padding: 8px 0;\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .filter-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .filter-label {\r\n    margin-bottom: 3px;\r\n  }\r\n\r\n  .checkbox-group, .radio-group {\r\n    gap: 6px;\r\n  }\r\n\r\n  .checkbox-item, .radio-item {\r\n    min-width: 60px;\r\n  }\r\n\r\n  .specs-grid {\r\n    grid-template-columns: 1fr 1fr;\r\n  }\r\n\r\n  .price .amount {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .server-status {\r\n    font-size: 11px;\r\n    padding: 3px 6px;\r\n  }\r\n\r\n  .empty-state-icon img {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .empty-state-text {\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n/* 调整左侧边距在移动端 */\r\n@media (max-width: 768px) {\r\n  div[style^=\"margin-left\"] {\r\n    margin-left: 0 !important;\r\n  }\r\n\r\n  div[style^=\"width\"] {\r\n    width: 100% !important;\r\n  }\r\n}\r\n/* 调整左侧边距在安卓显示器端 */\r\n@media (min-width: 1440px) {\r\n  div[style^=\"margin-left\"] {\r\n    margin-left: 3.5% !important;\r\n  }\r\n\r\n  div[style^=\"width\"] {\r\n    width: 96% !important;\r\n  }\r\n}\r\n\r\n</style>"], "mappings": ";AAuPA,OAAAA,MAAA;AACA,OAAAC,MAAA;AACA,SAAAC,WAAA,EAAAC,UAAA,EAAAC,WAAA,EAAAC,UAAA,EAAAC,YAAA;AACA,SAAAC,cAAA;AACA,SAAAC,QAAA;AACA,OAAAC,WAAA;AACA,OAAAC,iBAAA;AAGA;EACAC,IAAA;EACAC,UAAA;IAAAZ,MAAA;IAAAS,WAAA;IAAAR,MAAA;IAAAS;EAAA;EACAG,KAAA;IACA;MACAC,SAAA;MACAC,KAAA;MACA;MACAC,qBAAA;MACAC,QAAA;MACAC,cAAA;MACAC,mBAAA;MACAC,UAAA;MACAC,SAAA;MACA;MACAC,eAAA;MAEA;MACAC,aAAA;MAEA;MACAC,OAAA,GACA;QAAAC,EAAA;QAAAd,IAAA;MAAA,GACA;QAAAc,EAAA;QAAAd,IAAA;MAAA,GACA;QAAAc,EAAA;QAAAd,IAAA;MAAA,EACA;MAEA;MACAe,YAAA,GACA;QAAAD,EAAA;QAAAd,IAAA;MAAA,GACA;QAAAc,EAAA;QAAAd,IAAA;MAAA,GACA;QAAAc,EAAA;QAAAd,IAAA;MAAA,GACA;QAAAc,EAAA;QAAAd,IAAA;MAAA,GACA;QAAAc,EAAA;QAAAd,IAAA;MAAA,GACA;QAAAc,EAAA;QAAAd,IAAA;MAAA,GACA;QAAAc,EAAA;QAAAd,IAAA;MAAA,EACA;MAEA;MACAgB,cAAA;QACAC,SAAA;QAAA;QACAC,QAAA;QAAA;QACAC,WAAA;MACA;;MAEA;MACAC,OAAA;QACAC,UAAA;QACAC,eAAA;QACAC,aAAA;QAAA;QACAR,YAAA;QACAS,iBAAA;QACAC,cAAA;UACAC,WAAA;QACA;MACA;MAEA;MACAC,UAAA,GACA;QACAb,EAAA;QACAc,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,IAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QAAA;QACAC,QAAA;MACA,GACA;QACAxB,EAAA;QACAc,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,IAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QAAA;QACAC,QAAA;MACA,GACA;QACAxB,EAAA;QACAc,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,IAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAxB,EAAA;QACAc,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,IAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAxB,EAAA;QACAc,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,IAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAxB,EAAA;QACAc,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,IAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,QAAA;MACA;IAEA;EACA;EACAC,QAAA;IACA;IACAC,mBAAA;MACA;MACA,SAAApB,OAAA,CAAAE,eAAA,CAAAmB,MAAA;QACA;MACA;MACA;MACA,MAAAC,eAAA,QAAAf,UAAA,CACAgB,MAAA,CAAAC,MAAA,SAAAxB,OAAA,CAAAE,eAAA,CAAAuB,QAAA,CAAAD,MAAA,CAAAE,MAAA,GACAC,GAAA,CAAAH,MAAA,IAAAA,MAAA,CAAA5C,IAAA;;MAEA;MACA,MAAAgD,YAAA,WAAAC,GAAA,CAAAP,eAAA;;MAEA;MACA,YAAA3B,YAAA,CAAA4B,MAAA,CAAAO,GAAA,IAAAF,YAAA,CAAAH,QAAA,CAAAK,GAAA,CAAAlD,IAAA;IACA;IAEA;IACAmD,gBAAA;MACA;MACA,SAAA/B,OAAA,CAAAL,YAAA,SAAAK,OAAA,CAAAI,iBAAA,CAAAiB,MAAA,eAAAD,kBAAA,CAAAC,MAAA;QACA,KAAArB,OAAA,CAAAI,iBAAA,QAAAgB,kBAAA,CAAAO,GAAA,CAAAG,GAAA,IAAAA,GAAA,CAAApC,EAAA;MACA;;MAEA;MACA,SAAAM,OAAA,CAAAE,eAAA,CAAAmB,MAAA,eAAArB,OAAA,CAAAI,iBAAA,CAAAiB,MAAA;QACA;MACA;MAEA,YAAAd,UAAA,CAAAgB,MAAA,CAAAC,MAAA;QACA;QACA,UAAAxB,OAAA,CAAAE,eAAA,CAAAuB,QAAA,CAAAD,MAAA,CAAAE,MAAA;UACA;QACA;;QAEA;QACA,UAAA1B,OAAA,CAAAI,iBAAA,CAAAqB,QAAA,CAAAD,MAAA,CAAA5C,IAAA;UACA;QACA;;QAEA;QACA,UAAAoB,OAAA,CAAAK,cAAA,CAAAC,WAAA;UACA;QACA;;QAEA;QACA;;QAEA;MACA;IACA;EACA;EACA0B,OAAA;IACAC,iBAAA;MACA,KAAA5C,UAAA;IACA;IACA;IACA6C,WAAAC,MAAA;MACA,KAAApD,SAAA,GAAAoD,MAAA;IACA;IACA;IACAC,WAAAD,MAAA;MACA,KAAAnD,KAAA,GAAAmD,MAAA;IACA;IACA,MAAAE,gBAAA;MACA;MACA,KAAA5D,QAAA;QACA,KAAA6D,OAAA,CAAAC,IAAA;QACA,KAAAC,MAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA;QACA,MAAAC,GAAA,SAAAvE,WAAA;QACA,IAAAuE,GAAA,CAAA5D,IAAA,CAAA6D,IAAA;UACA,MAAAC,MAAA,GAAAF,GAAA,CAAA5D,IAAA,CAAAA,IAAA,CAAA8D,MAAA;;UAEA;UACA,IAAAA,MAAA;YACA,KAAAxD,mBAAA;YACA,KAAAD,cAAA;YAEA0D,UAAA;cACA,KAAAP,OAAA,CAAAC,IAAA;gBACAO,IAAA;gBACAC,KAAA;kBAAAC,SAAA;gBAAA;cACA;YACA;YACA;UACA;;UAEA;UACA,KAAAV,OAAA,CAAAC,IAAA;QACA;MACA,SAAAU,KAAA;QACA,KAAAT,MAAA,CAAAS,KAAA;QACA;MACA;IACA;;IACA;IACAC,QAAAC,UAAA;MACA;MACA,IAAAA,UAAA,CAAAC,eAAA;QACA,KAAAZ,MAAA,CAAAS,KAAA;QACA;MACA;MAEA,KAAAxE,QAAA;QACA,KAAA6D,OAAA,CAAAC,IAAA;QACA,KAAAC,MAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAvD,QAAA,GAAAiE,UAAA;MACA,KAAAlE,qBAAA,QAAAK,SAAA;MACA,KAAAD,UAAA;IACA;IAEAgE,OAAA7B,MAAA;MACA,IAAA8B,MAAA;QACA5D,EAAA,EAAA8B,MAAA,CAAA9B,EAAA;QACAV,KAAA;QACAJ,IAAA,EAAA4C,MAAA,CAAA5C,IAAA;QACA2E,IAAA,OAAAxE;MACA;MACAuE,MAAA,CAAAtE,KAAA,QAAAA,KAAA;MACAsE,MAAA,CAAAC,IAAA,QAAAxE,SAAA;MACAR,YAAA,+BAAA+E,MAAA,EAAAE,IAAA,CAAAd,GAAA;QACA,IAAAA,GAAA,CAAA5D,IAAA,CAAA2E,GAAA;UACA,KAAAjB,MAAA,CAAAS,KAAA;UACA;QACA;QACA,IAAAP,GAAA,CAAA5D,IAAA,CAAA2E,GAAA;UACA,KAAAjB,MAAA,CAAAS,KAAA;UACA;QACA;QACA,KAAAT,MAAA,CAAAC,OAAA;MACA;IACA;IACA;IACAiB,uBAAA;MACA,KAAAnE,eAAA,SAAAA,eAAA;IACA;IAEA;IACAoE,YAAAC,EAAA;MACAA,EAAA,CAAAC,KAAA,CAAAC,MAAA;MACAF,EAAA,CAAAC,KAAA,CAAAE,OAAA;MACAH,EAAA,CAAAC,KAAA,CAAAG,QAAA;IACA;IAEA;IACAC,MAAAL,EAAA;MACA,MAAAE,MAAA,GAAAF,EAAA,CAAAM,YAAA;MACA;MACAC,qBAAA;QACAP,EAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAA,MAAA;QACAF,EAAA,CAAAC,KAAA,CAAAE,OAAA;MACA;IACA;IAEA;IACAK,WAAAR,EAAA;MACA;MACAA,EAAA,CAAAC,KAAA,CAAAC,MAAA;MACAF,EAAA,CAAAC,KAAA,CAAAG,QAAA;IACA;IAEA;IACAK,YAAAT,EAAA;MACA;MACAA,EAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAF,EAAA,CAAAM,YAAA;MACAN,EAAA,CAAAC,KAAA,CAAAG,QAAA;IACA;IAEA;IACAM,MAAAV,EAAA;MACA;MACA,KAAAA,EAAA,CAAAW,YAAA;;MAEA;MACAJ,qBAAA;QACAP,EAAA,CAAAC,KAAA,CAAAC,MAAA;QACAF,EAAA,CAAAC,KAAA,CAAAE,OAAA;MACA;IACA;IAEA;IACAS,WAAAZ,EAAA;MACA;MACAA,EAAA,CAAAC,KAAA,CAAAC,MAAA;MACAF,EAAA,CAAAC,KAAA,CAAAG,QAAA;IACA;IAEA;IACAS,iBAAA;MACA,SAAAzE,OAAA,CAAAC,UAAA;QACA,KAAAD,OAAA,CAAAE,eAAA,QAAAT,OAAA,CAAAkC,GAAA,CAAAD,MAAA,IAAAA,MAAA,CAAA9C,IAAA;MACA;QACA,KAAAoB,OAAA,CAAAE,eAAA;MACA;MACA,KAAAwE,aAAA;IACA;IAEA;IACAC,mBAAA;MACA,SAAA3E,OAAA,CAAAL,YAAA;QACA,KAAAK,OAAA,CAAAI,iBAAA,QAAAgB,kBAAA,CAAAO,GAAA,CAAAG,GAAA,IAAAA,GAAA,CAAApC,EAAA;MACA;QACA,KAAAM,OAAA,CAAAI,iBAAA;MACA;MACA,KAAAsE,aAAA;IACA;IAEA;IACAA,cAAA;MACA;MACA,KAAA1E,OAAA,CAAAC,UAAA,QAAAD,OAAA,CAAAE,eAAA,CAAAmB,MAAA,UAAA5B,OAAA,CAAA4B,MAAA;;MAEA;MACA,KAAAuD,SAAA;QACA,KAAA5E,OAAA,CAAAL,YAAA,QAAAyB,kBAAA,CAAAC,MAAA,QACA,KAAArB,OAAA,CAAAI,iBAAA,CAAAiB,MAAA,UAAAD,kBAAA,CAAAC,MAAA;MACA;;MAEA;MACA;IACA;;IAEA;IACAwD,cAAA3D,QAAA;MACA,MAAAQ,MAAA,QAAAjC,OAAA,CAAAqF,IAAA,CAAAC,CAAA,IAAAA,CAAA,CAAArF,EAAA,KAAAwB,QAAA;MACA,OAAAQ,MAAA,GAAAA,MAAA,CAAA9C,IAAA;IACA;IAEA;IACAoG,oBAAA/D,MAAA;MACA,IAAAA,MAAA,QAAAA,MAAA;QACAA,MAAA;MACA,WAAAA,MAAA;QACAA,MAAA;MACA,WAAAA,MAAA;QACAA,MAAA;MACA;MACA,QAAAA,MAAA;QACA,UAAArB,cAAA,CAAAC,SAAA;UACA;QACA,UAAAD,cAAA,CAAAE,QAAA;UACA;QACA,UAAAF,cAAA,CAAAG,WAAA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAkF,qBAAAhE,MAAA;MACA,IAAAA,MAAA,QAAAA,MAAA;QACAA,MAAA;MACA,WAAAA,MAAA;QACAA,MAAA;MACA,WAAAA,MAAA;QACAA,MAAA;MACA;MACA,QAAAA,MAAA;QACA,UAAArB,cAAA,CAAAC,SAAA;UACA;QACA,UAAAD,cAAA,CAAAE,QAAA;UACA;QACA,UAAAF,cAAA,CAAAG,WAAA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAmF,aAAA;MACA;MACA,IAAA5B,MAAA;MACAlF,UAAA,qBAAAoF,IAAA,CAAA2B,OAAA;QACA,KAAA5E,UAAA,GAAA4E,OAAA,CAAArG,IAAA,CAAAA,IAAA;QACAT,WAAA,6BAAAmF,IAAA,CAAAd,GAAA;UACA,KAAA/C,YAAA,GAAA+C,GAAA,CAAA5D,IAAA,CAAAA,IAAA;UACA,SAAAsG,CAAA,MAAAA,CAAA,QAAAzF,YAAA,CAAA0B,MAAA,EAAA+D,CAAA;YACA,KAAAzF,YAAA,CAAAyF,CAAA,EAAA1F,EAAA,QAAAC,YAAA,CAAAyF,CAAA,EAAAxG,IAAA;UACA;UACAP,WAAA,gCAAAmF,IAAA,CAAA9B,MAAA;YACA,KAAAjC,OAAA,GAAAiC,MAAA,CAAA5C,IAAA,CAAAA,IAAA;YACA,SAAAsG,CAAA,MAAAA,CAAA,QAAA3F,OAAA,CAAA4B,MAAA,EAAA+D,CAAA;cACA,KAAA3F,OAAA,CAAA2F,CAAA,EAAA1F,EAAA,QAAAD,OAAA,CAAA2F,CAAA,EAAA1D,MAAA;cACA,KAAAjC,OAAA,CAAA2F,CAAA,EAAAxG,IAAA,QAAAa,OAAA,CAAA2F,CAAA,EAAA1D,MAAA;YACA;YACA,KAAA+C,gBAAA;YACA,KAAAE,kBAAA;UACA;QACA;QACArB,MAAA;UACA7D,OAAA,OAAAO,OAAA,CAAAE,eAAA;UACAmF,SAAA,OAAArF,OAAA,CAAAI,iBAAA;UACAD,aAAA,OAAAH,OAAA,CAAAG;QACA;MACA;IACA;EACA;EACAmF,QAAA;IACA;IACA,KAAAJ,YAAA;IACA;IACA,KAAAN,SAAA;MACA,KAAA5E,OAAA,CAAAI,iBAAA,QAAAgB,kBAAA,CAAAO,GAAA,CAAAG,GAAA,IAAAA,GAAA,CAAApC,EAAA;IACA;EACA;EACA6F,KAAA;IACA;IACA;MACAC,QAAA;QACA;QACA,SAAAxF,OAAA,CAAAL,YAAA;UACA,KAAAK,OAAA,CAAAI,iBAAA,QAAAgB,kBAAA,CAAAO,GAAA,CAAAG,GAAA,IAAAA,GAAA,CAAApC,EAAA;QACA;UACA;UACA,MAAA+F,YAAA,QAAArE,kBAAA,CAAAO,GAAA,CAAAG,GAAA,IAAAA,GAAA,CAAApC,EAAA;UACA,KAAAM,OAAA,CAAAI,iBAAA,QAAAJ,OAAA,CAAAI,iBAAA,CAAAmB,MAAA,CAAA7B,EAAA,IAAA+F,YAAA,CAAAhE,QAAA,CAAA/B,EAAA;QACA;MACA;MACAgG,SAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}