{"ast": null, "code": "import Header from \"@/components/common/header/Header\";\n// import Footer from \"@/components/common/footer/Footer\";\n\nexport default {\n  name: \"Layout\",\n  components: {\n    Header\n  }\n};", "map": {"version": 3, "names": ["Header", "name", "components"], "sources": ["src/components/common/layout-fee.vue"], "sourcesContent": ["<template>\r\n  <main class=\"page-wrapper\">\r\n    <Header/>\r\n\r\n    <div class=\"main-content\">\r\n      <slot></slot>\r\n    </div>\r\n\r\n    <Footer/>\r\n  </main>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"@/components/common/header/Header\";\r\n// import Footer from \"@/components/common/footer/Footer\";\r\n\r\nexport default {\r\n  name: \"Layout\",\r\n  components:{Header}\r\n}\r\n</script>\r\n<style scoped>\r\n.main-content{\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  /*display: flex;*/\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n</style>"], "mappings": "AAaA,OAAAA,MAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}