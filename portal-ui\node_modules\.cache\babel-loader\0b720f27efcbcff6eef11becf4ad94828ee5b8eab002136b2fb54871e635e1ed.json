{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"Layout\", [_c(\"div\", {\n    staticClass: \"layout-container\",\n    staticStyle: {\n      width: \"100%\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"page-header\",\n    staticStyle: {\n      \"max-height\": \"14px\"\n    }\n  }), _c(\"div\", {\n    staticClass: \"breadcrumb-box\"\n  }, [_c(\"div\", {\n    staticClass: \"am-container\"\n  }, [_c(\"ol\", {\n    staticClass: \"am-breadcrumb\"\n  }, [_c(\"li\", [_c(\"router-link\", {\n    attrs: {\n      to: \"/news\"\n    }\n  }, [_vm._v(\"公司动态\")])], 1), _c(\"li\", {\n    staticClass: \"am-active\"\n  }, [_vm._v(\"文章详情\")])])])])]), _c(\"div\", {\n    staticClass: \"section\"\n  }, [_c(\"div\", {\n    staticClass: \"container\",\n    staticStyle: {\n      \"max-width\": \"1160px\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"section--header\"\n  }, [_c(\"h2\", {\n    staticClass: \"section--title\"\n  }, [_vm._v(_vm._s(_vm.article.title))]), _c(\"p\", {\n    staticClass: \"section--description\"\n  }, [_vm._v(_vm._s(_vm.article.introduction))])]), _c(\"div\", {\n    staticClass: \"join-container\"\n  }, [_c(\"div\", {\n    staticClass: \"am-g\"\n  }, [_c(\"div\", {\n    staticClass: \"am-u-md-3\"\n  }, [_c(\"div\", {\n    staticClass: \"careers--articles\"\n  }, [_c(\"div\", {\n    staticClass: \"careers_articles\"\n  }, _vm._l(_vm.recentArticles, function (article, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"careers_article\"\n    }, [_c(\"div\", {\n      staticClass: \"image\"\n    }, [_c(\"img\", {\n      staticStyle: {\n        height: \"160px\"\n      },\n      attrs: {\n        src: article.cover,\n        alt: \"\"\n      }\n    })]), _c(\"h3\", {\n      staticClass: \"careers_article--title\"\n    }, [_vm._v(_vm._s(article.title))]), _c(\"div\", {\n      staticClass: \"careers_article--text\"\n    }, [_vm._v(_vm._s(article.introduction))]), _c(\"div\", {\n      staticClass: \"careers_article--footer\"\n    }, [_c(\"router-link\", {\n      staticClass: \"link\",\n      attrs: {\n        to: {\n          name: \"newsDetails\",\n          params: {\n            newsId: article.articleId\n          }\n        }\n      }\n    }, [_vm._v(\"查看更多\")])], 1)]);\n  }), 0)])]), _c(\"div\", {\n    staticClass: \"am-u-md-9\"\n  }, [_c(\"div\", {\n    staticClass: \"careers--vacancies\"\n  }, [_c(\"div\", {\n    staticClass: \"am-panel-group\",\n    attrs: {\n      id: \"accordion\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"am-panel am-panel-default\"\n  }, [_c(\"div\", {\n    staticClass: \"am-panel-hd\"\n  }, [_c(\"h4\", {\n    staticClass: \"am-panel-title\",\n    attrs: {\n      \"data-am-collapse\": \"{parent: '#accordion', target: '#do-not-say-1'}\"\n    }\n  }, [_vm._v(\" 作者：\" + _vm._s(_vm.article.author) + \"      /      发布时间：\" + _vm._s(_vm.article.createTime) + \" \")])]), _c(\"div\", {\n    staticClass: \"am-panel-collapse am-collapse am-in\"\n  }, [_c(\"div\", {\n    staticClass: \"am-panel-bd\"\n  }, [_c(\"div\", {\n    staticClass: \"vacancies--item_content js-accordion--pane_content\",\n    domProps: {\n      innerHTML: _vm._s(_vm.article.contentHtml)\n    }\n  })])])])])])])])])])])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "width", "attrs", "to", "_v", "_s", "article", "title", "introduction", "_l", "recentArticles", "index", "key", "height", "src", "cover", "alt", "name", "params", "newsId", "articleId", "id", "author", "createTime", "domProps", "innerHTML", "contentHtml", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/views/NewsDetailsView.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"Layout\", [\n    _c(\n      \"div\",\n      { staticClass: \"layout-container\", staticStyle: { width: \"100%\" } },\n      [\n        _c(\"div\", {\n          staticClass: \"page-header\",\n          staticStyle: { \"max-height\": \"14px\" },\n        }),\n        _c(\"div\", { staticClass: \"breadcrumb-box\" }, [\n          _c(\"div\", { staticClass: \"am-container\" }, [\n            _c(\"ol\", { staticClass: \"am-breadcrumb\" }, [\n              _c(\n                \"li\",\n                [\n                  _c(\"router-link\", { attrs: { to: \"/news\" } }, [\n                    _vm._v(\"公司动态\"),\n                  ]),\n                ],\n                1\n              ),\n              _c(\"li\", { staticClass: \"am-active\" }, [_vm._v(\"文章详情\")]),\n            ]),\n          ]),\n        ]),\n      ]\n    ),\n    _c(\"div\", { staticClass: \"section\" }, [\n      _c(\n        \"div\",\n        { staticClass: \"container\", staticStyle: { \"max-width\": \"1160px\" } },\n        [\n          _c(\"div\", { staticClass: \"section--header\" }, [\n            _c(\"h2\", { staticClass: \"section--title\" }, [\n              _vm._v(_vm._s(_vm.article.title)),\n            ]),\n            _c(\"p\", { staticClass: \"section--description\" }, [\n              _vm._v(_vm._s(_vm.article.introduction)),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"join-container\" }, [\n            _c(\"div\", { staticClass: \"am-g\" }, [\n              _c(\"div\", { staticClass: \"am-u-md-3\" }, [\n                _c(\"div\", { staticClass: \"careers--articles\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"careers_articles\" },\n                    _vm._l(_vm.recentArticles, function (article, index) {\n                      return _c(\n                        \"div\",\n                        { key: index, staticClass: \"careers_article\" },\n                        [\n                          _c(\"div\", { staticClass: \"image\" }, [\n                            _c(\"img\", {\n                              staticStyle: { height: \"160px\" },\n                              attrs: { src: article.cover, alt: \"\" },\n                            }),\n                          ]),\n                          _c(\"h3\", { staticClass: \"careers_article--title\" }, [\n                            _vm._v(_vm._s(article.title)),\n                          ]),\n                          _c(\"div\", { staticClass: \"careers_article--text\" }, [\n                            _vm._v(_vm._s(article.introduction)),\n                          ]),\n                          _c(\n                            \"div\",\n                            { staticClass: \"careers_article--footer\" },\n                            [\n                              _c(\n                                \"router-link\",\n                                {\n                                  staticClass: \"link\",\n                                  attrs: {\n                                    to: {\n                                      name: \"newsDetails\",\n                                      params: { newsId: article.articleId },\n                                    },\n                                  },\n                                },\n                                [_vm._v(\"查看更多\")]\n                              ),\n                            ],\n                            1\n                          ),\n                        ]\n                      )\n                    }),\n                    0\n                  ),\n                ]),\n              ]),\n              _c(\"div\", { staticClass: \"am-u-md-9\" }, [\n                _c(\"div\", { staticClass: \"careers--vacancies\" }, [\n                  _c(\n                    \"div\",\n                    {\n                      staticClass: \"am-panel-group\",\n                      attrs: { id: \"accordion\" },\n                    },\n                    [\n                      _c(\"div\", { staticClass: \"am-panel am-panel-default\" }, [\n                        _c(\"div\", { staticClass: \"am-panel-hd\" }, [\n                          _c(\n                            \"h4\",\n                            {\n                              staticClass: \"am-panel-title\",\n                              attrs: {\n                                \"data-am-collapse\":\n                                  \"{parent: '#accordion', target: '#do-not-say-1'}\",\n                              },\n                            },\n                            [\n                              _vm._v(\n                                \" 作者：\" +\n                                  _vm._s(_vm.article.author) +\n                                  \"      /      发布时间：\" +\n                                  _vm._s(_vm.article.createTime) +\n                                  \" \"\n                              ),\n                            ]\n                          ),\n                        ]),\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"am-panel-collapse am-collapse am-in\",\n                          },\n                          [\n                            _c(\"div\", { staticClass: \"am-panel-bd\" }, [\n                              _c(\"div\", {\n                                staticClass:\n                                  \"vacancies--item_content js-accordion--pane_content\",\n                                domProps: {\n                                  innerHTML: _vm._s(_vm.article.contentHtml),\n                                },\n                              }),\n                            ]),\n                          ]\n                        ),\n                      ]),\n                    ]\n                  ),\n                ]),\n              ]),\n            ]),\n          ]),\n        ]\n      ),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,QAAQ,EAAE,CAClBA,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,kBAAkB;IAAEC,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EACnE,CACEJ,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO;EACtC,CAAC,CAAC,EACFH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCF,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,aAAa,EAAE;IAAEK,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAQ;EAAE,CAAC,EAAE,CAC5CP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,EACD,CAAC,CACF,EACDP,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CAACH,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACzD,CAAC,CACH,CAAC,CACH,CAAC,CACH,CACF,EACDP,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,WAAW;IAAEC,WAAW,EAAE;MAAE,WAAW,EAAE;IAAS;EAAE,CAAC,EACpE,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,OAAO,CAACC,KAAK,CAAC,CAAC,CAClC,CAAC,EACFV,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CAC/CH,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,OAAO,CAACE,YAAY,CAAC,CAAC,CACzC,CAAC,CACH,CAAC,EACFX,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAmB,CAAC,EACnCH,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,cAAc,EAAE,UAAUJ,OAAO,EAAEK,KAAK,EAAE;IACnD,OAAOd,EAAE,CACP,KAAK,EACL;MAAEe,GAAG,EAAED,KAAK;MAAEZ,WAAW,EAAE;IAAkB,CAAC,EAC9C,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAQ,CAAC,EAAE,CAClCF,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE;QAAEa,MAAM,EAAE;MAAQ,CAAC;MAChCX,KAAK,EAAE;QAAEY,GAAG,EAAER,OAAO,CAACS,KAAK;QAAEC,GAAG,EAAE;MAAG;IACvC,CAAC,CAAC,CACH,CAAC,EACFnB,EAAE,CAAC,IAAI,EAAE;MAAEE,WAAW,EAAE;IAAyB,CAAC,EAAE,CAClDH,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACS,EAAE,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC,CAC9B,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAwB,CAAC,EAAE,CAClDH,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACS,EAAE,CAACC,OAAO,CAACE,YAAY,CAAC,CAAC,CACrC,CAAC,EACFX,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAA0B,CAAC,EAC1C,CACEF,EAAE,CACA,aAAa,EACb;MACEE,WAAW,EAAE,MAAM;MACnBG,KAAK,EAAE;QACLC,EAAE,EAAE;UACFc,IAAI,EAAE,aAAa;UACnBC,MAAM,EAAE;YAAEC,MAAM,EAAEb,OAAO,CAACc;UAAU;QACtC;MACF;IACF,CAAC,EACD,CAACxB,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,EACD,CAAC,CACF,CACF,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CAC/CF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,gBAAgB;IAC7BG,KAAK,EAAE;MAAEmB,EAAE,EAAE;IAAY;EAC3B,CAAC,EACD,CACExB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA4B,CAAC,EAAE,CACtDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE,gBAAgB;IAC7BG,KAAK,EAAE;MACL,kBAAkB,EAChB;IACJ;EACF,CAAC,EACD,CACEN,GAAG,CAACQ,EAAE,CACJ,MAAM,GACJR,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,OAAO,CAACgB,MAAM,CAAC,GAC1B,oBAAoB,GACpB1B,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,OAAO,CAACiB,UAAU,CAAC,GAC9B,GAAG,CACN,CACF,CACF,CACF,CAAC,EACF1B,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EACT,oDAAoD;IACtDyB,QAAQ,EAAE;MACRC,SAAS,EAAE7B,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,OAAO,CAACoB,WAAW;IAC3C;EACF,CAAC,CAAC,CACH,CAAC,CACH,CACF,CACF,CAAC,CACH,CACF,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CACF,CACF,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBhC,MAAM,CAACiC,aAAa,GAAG,IAAI;AAE3B,SAASjC,MAAM,EAAEgC,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}