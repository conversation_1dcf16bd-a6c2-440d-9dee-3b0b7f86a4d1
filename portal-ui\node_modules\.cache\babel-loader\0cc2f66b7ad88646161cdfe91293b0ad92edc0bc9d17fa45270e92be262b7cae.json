{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", [_c(\"div\", {\n    staticClass: \"chat-container\"\n  }, [_c(\"div\", {\n    staticClass: \"question-carousel\",\n    on: {\n      mouseenter: _vm.pauseCarousel,\n      mouseleave: _vm.resumeCarousel\n    }\n  }, [_c(\"transition-group\", {\n    staticClass: \"carousel-wrapper\",\n    attrs: {\n      name: \"slide\",\n      tag: \"div\"\n    }\n  }, _vm._l(_vm.questions, function (question, index) {\n    return _c(\"div\", {\n      directives: [{\n        name: \"show\",\n        rawName: \"v-show\",\n        value: _vm.currentQuestionIndex === index,\n        expression: \"currentQuestionIndex === index\"\n      }],\n      key: question,\n      staticClass: \"question-item\",\n      on: {\n        click: function ($event) {\n          return _vm.sendCarouselQuestion(question);\n        },\n        mouseenter: function ($event) {\n          return _vm.witde(index);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(question) + \" \")]);\n  }), 0)], 1), _c(\"div\", {\n    staticClass: \"chat-icon\",\n    class: {\n      \"chat-icon-active\": _vm.showChat\n    },\n    on: {\n      click: _vm.toggleChat\n    }\n  }, [_c(\"i\", {\n    staticClass: \"fas fa-comment\"\n  })])]), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.showChat,\n      expression: \"showChat\"\n    }],\n    staticClass: \"chat-window\"\n  }, [_c(\"div\", {\n    staticClass: \"chat-header\"\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"chat-controls\"\n  }, [_c(\"i\", {\n    staticClass: \"fas fa-times\",\n    on: {\n      click: _vm.toggleChat\n    }\n  })])]), _c(\"div\", {\n    ref: \"messagesContainer\",\n    staticClass: \"chat-messages\"\n  }, [_vm._l(_vm.messages, function (message, index) {\n    return _c(\"div\", {\n      key: index,\n      class: [\"message\", message.type]\n    }, [message.type === \"bot\" ? _c(\"div\", {\n      staticClass: \"avatar\"\n    }, [_c(\"i\", {\n      staticClass: \"fas fa-robot\"\n    })]) : _vm._e(), _c(\"div\", {\n      staticClass: \"message-content\"\n    }, [_c(\"div\", {\n      staticClass: \"message-text\",\n      domProps: {\n        innerHTML: _vm._s(_vm.formatMessage(message.text))\n      }\n    }), _c(\"div\", {\n      staticClass: \"message-time\"\n    }, [_vm._v(_vm._s(_vm.formatTime(message.time)))])])]);\n  }), _vm.loading ? _c(\"div\", {\n    staticClass: \"typing-indicator\"\n  }, [_c(\"div\", {\n    staticClass: \"dot\"\n  }), _c(\"div\", {\n    staticClass: \"dot\"\n  }), _c(\"div\", {\n    staticClass: \"dot\"\n  })]) : _vm._e()], 2), _c(\"div\", {\n    staticClass: \"chat-input\"\n  }, [_c(\"input\", {\n    directives: [{\n      name: \"model\",\n      rawName: \"v-model\",\n      value: _vm.userInput,\n      expression: \"userInput\"\n    }],\n    attrs: {\n      type: \"text\",\n      placeholder: \"请输入您的问题...\",\n      disabled: _vm.loading\n    },\n    domProps: {\n      value: _vm.userInput\n    },\n    on: {\n      keyup: function ($event) {\n        if (!$event.type.indexOf(\"key\") && _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")) return null;\n        return _vm.sendMessage.apply(null, arguments);\n      },\n      input: function ($event) {\n        if ($event.target.composing) return;\n        _vm.userInput = $event.target.value;\n      }\n    }\n  }), _c(\"button\", {\n    attrs: {\n      disabled: _vm.loading || !_vm.userInput.trim()\n    },\n    on: {\n      click: _vm.sendMessage\n    }\n  }, [_c(\"i\", {\n    staticClass: \"fas fa-paper-plane\"\n  })])])])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"chat-title\"\n  }, [_c(\"i\", {\n    staticClass: \"fas fa-robot\"\n  }), _c(\"span\", [_vm._v(\"智能客服\")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "on", "mouseenter", "pauseCarousel", "mouseleave", "resumeCarousel", "attrs", "name", "tag", "_l", "questions", "question", "index", "directives", "rawName", "value", "currentQuestionIndex", "expression", "key", "click", "$event", "sendCarouselQuestion", "witde", "_v", "_s", "class", "showChat", "toggleChat", "_m", "ref", "messages", "message", "type", "_e", "domProps", "innerHTML", "formatMessage", "text", "formatTime", "time", "loading", "userInput", "placeholder", "disabled", "keyup", "indexOf", "_k", "keyCode", "sendMessage", "apply", "arguments", "input", "target", "composing", "trim", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/components/common/mider/chatAi.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", [\n    _c(\"div\", { staticClass: \"chat-container\" }, [\n      _c(\n        \"div\",\n        {\n          staticClass: \"question-carousel\",\n          on: { mouseenter: _vm.pauseCarousel, mouseleave: _vm.resumeCarousel },\n        },\n        [\n          _c(\n            \"transition-group\",\n            {\n              staticClass: \"carousel-wrapper\",\n              attrs: { name: \"slide\", tag: \"div\" },\n            },\n            _vm._l(_vm.questions, function (question, index) {\n              return _c(\n                \"div\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.currentQuestionIndex === index,\n                      expression: \"currentQuestionIndex === index\",\n                    },\n                  ],\n                  key: question,\n                  staticClass: \"question-item\",\n                  on: {\n                    click: function ($event) {\n                      return _vm.sendCarouselQuestion(question)\n                    },\n                    mouseenter: function ($event) {\n                      return _vm.witde(index)\n                    },\n                  },\n                },\n                [_vm._v(\" \" + _vm._s(question) + \" \")]\n              )\n            }),\n            0\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        {\n          staticClass: \"chat-icon\",\n          class: { \"chat-icon-active\": _vm.showChat },\n          on: { click: _vm.toggleChat },\n        },\n        [_c(\"i\", { staticClass: \"fas fa-comment\" })]\n      ),\n    ]),\n    _c(\n      \"div\",\n      {\n        directives: [\n          {\n            name: \"show\",\n            rawName: \"v-show\",\n            value: _vm.showChat,\n            expression: \"showChat\",\n          },\n        ],\n        staticClass: \"chat-window\",\n      },\n      [\n        _c(\"div\", { staticClass: \"chat-header\" }, [\n          _vm._m(0),\n          _c(\"div\", { staticClass: \"chat-controls\" }, [\n            _c(\"i\", {\n              staticClass: \"fas fa-times\",\n              on: { click: _vm.toggleChat },\n            }),\n          ]),\n        ]),\n        _c(\n          \"div\",\n          { ref: \"messagesContainer\", staticClass: \"chat-messages\" },\n          [\n            _vm._l(_vm.messages, function (message, index) {\n              return _c(\n                \"div\",\n                { key: index, class: [\"message\", message.type] },\n                [\n                  message.type === \"bot\"\n                    ? _c(\"div\", { staticClass: \"avatar\" }, [\n                        _c(\"i\", { staticClass: \"fas fa-robot\" }),\n                      ])\n                    : _vm._e(),\n                  _c(\"div\", { staticClass: \"message-content\" }, [\n                    _c(\"div\", {\n                      staticClass: \"message-text\",\n                      domProps: {\n                        innerHTML: _vm._s(_vm.formatMessage(message.text)),\n                      },\n                    }),\n                    _c(\"div\", { staticClass: \"message-time\" }, [\n                      _vm._v(_vm._s(_vm.formatTime(message.time))),\n                    ]),\n                  ]),\n                ]\n              )\n            }),\n            _vm.loading\n              ? _c(\"div\", { staticClass: \"typing-indicator\" }, [\n                  _c(\"div\", { staticClass: \"dot\" }),\n                  _c(\"div\", { staticClass: \"dot\" }),\n                  _c(\"div\", { staticClass: \"dot\" }),\n                ])\n              : _vm._e(),\n          ],\n          2\n        ),\n        _c(\"div\", { staticClass: \"chat-input\" }, [\n          _c(\"input\", {\n            directives: [\n              {\n                name: \"model\",\n                rawName: \"v-model\",\n                value: _vm.userInput,\n                expression: \"userInput\",\n              },\n            ],\n            attrs: {\n              type: \"text\",\n              placeholder: \"请输入您的问题...\",\n              disabled: _vm.loading,\n            },\n            domProps: { value: _vm.userInput },\n            on: {\n              keyup: function ($event) {\n                if (\n                  !$event.type.indexOf(\"key\") &&\n                  _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")\n                )\n                  return null\n                return _vm.sendMessage.apply(null, arguments)\n              },\n              input: function ($event) {\n                if ($event.target.composing) return\n                _vm.userInput = $event.target.value\n              },\n            },\n          }),\n          _c(\n            \"button\",\n            {\n              attrs: { disabled: _vm.loading || !_vm.userInput.trim() },\n              on: { click: _vm.sendMessage },\n            },\n            [_c(\"i\", { staticClass: \"fas fa-paper-plane\" })]\n          ),\n        ]),\n      ]\n    ),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"chat-title\" }, [\n      _c(\"i\", { staticClass: \"fas fa-robot\" }),\n      _c(\"span\", [_vm._v(\"智能客服\")]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE,CACfA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,mBAAmB;IAChCC,EAAE,EAAE;MAAEC,UAAU,EAAEL,GAAG,CAACM,aAAa;MAAEC,UAAU,EAAEP,GAAG,CAACQ;IAAe;EACtE,CAAC,EACD,CACEP,EAAE,CACA,kBAAkB,EAClB;IACEE,WAAW,EAAE,kBAAkB;IAC/BM,KAAK,EAAE;MAAEC,IAAI,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAM;EACrC,CAAC,EACDX,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACa,SAAS,EAAE,UAAUC,QAAQ,EAAEC,KAAK,EAAE;IAC/C,OAAOd,EAAE,CACP,KAAK,EACL;MACEe,UAAU,EAAE,CACV;QACEN,IAAI,EAAE,MAAM;QACZO,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAElB,GAAG,CAACmB,oBAAoB,KAAKJ,KAAK;QACzCK,UAAU,EAAE;MACd,CAAC,CACF;MACDC,GAAG,EAAEP,QAAQ;MACbX,WAAW,EAAE,eAAe;MAC5BC,EAAE,EAAE;QACFkB,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOvB,GAAG,CAACwB,oBAAoB,CAACV,QAAQ,CAAC;QAC3C,CAAC;QACDT,UAAU,EAAE,SAAAA,CAAUkB,MAAM,EAAE;UAC5B,OAAOvB,GAAG,CAACyB,KAAK,CAACV,KAAK,CAAC;QACzB;MACF;IACF,CAAC,EACD,CAACf,GAAG,CAAC0B,EAAE,CAAC,GAAG,GAAG1B,GAAG,CAAC2B,EAAE,CAACb,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CACvC;EACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,EACD,CAAC,CACF,EACDb,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxByB,KAAK,EAAE;MAAE,kBAAkB,EAAE5B,GAAG,CAAC6B;IAAS,CAAC;IAC3CzB,EAAE,EAAE;MAAEkB,KAAK,EAAEtB,GAAG,CAAC8B;IAAW;EAC9B,CAAC,EACD,CAAC7B,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,CAAC,CAC7C,CACF,CAAC,EACFF,EAAE,CACA,KAAK,EACL;IACEe,UAAU,EAAE,CACV;MACEN,IAAI,EAAE,MAAM;MACZO,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAElB,GAAG,CAAC6B,QAAQ;MACnBT,UAAU,EAAE;IACd,CAAC,CACF;IACDjB,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCH,GAAG,CAAC+B,EAAE,CAAC,CAAC,CAAC,EACT9B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,cAAc;IAC3BC,EAAE,EAAE;MAAEkB,KAAK,EAAEtB,GAAG,CAAC8B;IAAW;EAC9B,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACF7B,EAAE,CACA,KAAK,EACL;IAAE+B,GAAG,EAAE,mBAAmB;IAAE7B,WAAW,EAAE;EAAgB,CAAC,EAC1D,CACEH,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACiC,QAAQ,EAAE,UAAUC,OAAO,EAAEnB,KAAK,EAAE;IAC7C,OAAOd,EAAE,CACP,KAAK,EACL;MAAEoB,GAAG,EAAEN,KAAK;MAAEa,KAAK,EAAE,CAAC,SAAS,EAAEM,OAAO,CAACC,IAAI;IAAE,CAAC,EAChD,CACED,OAAO,CAACC,IAAI,KAAK,KAAK,GAClBlC,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAS,CAAC,EAAE,CACnCF,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,CAAC,CACzC,CAAC,GACFH,GAAG,CAACoC,EAAE,EAAE,EACZnC,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,cAAc;MAC3BkC,QAAQ,EAAE;QACRC,SAAS,EAAEtC,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAACuC,aAAa,CAACL,OAAO,CAACM,IAAI,CAAC;MACnD;IACF,CAAC,CAAC,EACFvC,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCH,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAACyC,UAAU,CAACP,OAAO,CAACQ,IAAI,CAAC,CAAC,CAAC,CAC7C,CAAC,CACH,CAAC,CACH,CACF;EACH,CAAC,CAAC,EACF1C,GAAG,CAAC2C,OAAO,GACP1C,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAM,CAAC,CAAC,EACjCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAM,CAAC,CAAC,EACjCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAM,CAAC,CAAC,CAClC,CAAC,GACFH,GAAG,CAACoC,EAAE,EAAE,CACb,EACD,CAAC,CACF,EACDnC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,OAAO,EAAE;IACVe,UAAU,EAAE,CACV;MACEN,IAAI,EAAE,OAAO;MACbO,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAElB,GAAG,CAAC4C,SAAS;MACpBxB,UAAU,EAAE;IACd,CAAC,CACF;IACDX,KAAK,EAAE;MACL0B,IAAI,EAAE,MAAM;MACZU,WAAW,EAAE,YAAY;MACzBC,QAAQ,EAAE9C,GAAG,CAAC2C;IAChB,CAAC;IACDN,QAAQ,EAAE;MAAEnB,KAAK,EAAElB,GAAG,CAAC4C;IAAU,CAAC;IAClCxC,EAAE,EAAE;MACF2C,KAAK,EAAE,SAAAA,CAAUxB,MAAM,EAAE;QACvB,IACE,CAACA,MAAM,CAACY,IAAI,CAACa,OAAO,CAAC,KAAK,CAAC,IAC3BhD,GAAG,CAACiD,EAAE,CAAC1B,MAAM,CAAC2B,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE3B,MAAM,CAACF,GAAG,EAAE,OAAO,CAAC,EAExD,OAAO,IAAI;QACb,OAAOrB,GAAG,CAACmD,WAAW,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC/C,CAAC;MACDC,KAAK,EAAE,SAAAA,CAAU/B,MAAM,EAAE;QACvB,IAAIA,MAAM,CAACgC,MAAM,CAACC,SAAS,EAAE;QAC7BxD,GAAG,CAAC4C,SAAS,GAAGrB,MAAM,CAACgC,MAAM,CAACrC,KAAK;MACrC;IACF;EACF,CAAC,CAAC,EACFjB,EAAE,CACA,QAAQ,EACR;IACEQ,KAAK,EAAE;MAAEqC,QAAQ,EAAE9C,GAAG,CAAC2C,OAAO,IAAI,CAAC3C,GAAG,CAAC4C,SAAS,CAACa,IAAI;IAAG,CAAC;IACzDrD,EAAE,EAAE;MAAEkB,KAAK,EAAEtB,GAAG,CAACmD;IAAY;EAC/B,CAAC,EACD,CAAClD,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,CAAC,CACjD,CACF,CAAC,CACH,CACF,CACF,CAAC;AACJ,CAAC;AACD,IAAIuD,eAAe,GAAG,CACpB,YAAY;EACV,IAAI1D,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAC9CF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAAC0B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC7B,CAAC;AACJ,CAAC,CACF;AACD3B,MAAM,CAAC4D,aAAa,GAAG,IAAI;AAE3B,SAAS5D,MAAM,EAAE2D,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}