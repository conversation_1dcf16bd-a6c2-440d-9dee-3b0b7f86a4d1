{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"iframe-page\"\n  }, [_c(\"div\", {\n    staticClass: \"iframe-container\"\n  }, [_c(\"iframe\", {\n    key: _vm.iframe<PERSON>ey,\n    ref: \"iframe\",\n    attrs: {\n      src: _vm.iframeSrc\n    }\n  })])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "attrs", "src", "iframeSrc", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/views/Console.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"iframe-page\" }, [\n    _c(\"div\", { staticClass: \"iframe-container\" }, [\n      _c(\"iframe\", {\n        key: _vm.iframe<PERSON>ey,\n        ref: \"iframe\",\n        attrs: { src: _vm.iframeSrc },\n      }),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC/CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,QAAQ,EAAE;IACXG,GAAG,EAAEJ,GAAG,CAACK,SAAS;IAClBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;MAAEC,GAAG,EAAER,GAAG,CAACS;IAAU;EAC9B,CAAC,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBX,MAAM,CAACY,aAAa,GAAG,IAAI;AAE3B,SAASZ,MAAM,EAAEW,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}