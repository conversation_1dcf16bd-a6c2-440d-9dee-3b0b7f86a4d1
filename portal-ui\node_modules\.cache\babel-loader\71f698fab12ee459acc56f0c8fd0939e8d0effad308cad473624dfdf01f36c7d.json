{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, ref } from 'vue';\n\n// 图标组件\nconst PerformanceIcon = {\n  template: `\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n      <path d=\"M12 2v4\"></path>\n      <path d=\"m16.24 7.76 2.83-2.83\"></path>\n      <path d=\"M18 12h4\"></path>\n      <path d=\"m16.24 16.24 2.83 2.83\"></path>\n      <path d=\"M12 18v4\"></path>\n      <path d=\"m7.76 16.24-2.83 2.83\"></path>\n      <path d=\"M6 12H2\"></path>\n      <path d=\"m7.76 7.76-2.83-2.83\"></path>\n      <circle cx=\"12\" cy=\"12\" r=\"4\"></circle>\n    </svg>\n  `\n};\nconst ServerIcon = {\n  template: `\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n      <rect width=\"20\" height=\"8\" x=\"2\" y=\"2\" rx=\"2\" ry=\"2\"></rect>\n      <rect width=\"20\" height=\"8\" x=\"2\" y=\"14\" rx=\"2\" ry=\"2\"></rect>\n      <line x1=\"6\" x2=\"6\" y1=\"6\" y2=\"6\"></line>\n      <line x1=\"6\" x2=\"6\" y1=\"18\" y2=\"18\"></line>\n    </svg>\n  `\n};\nconst ShieldIcon = {\n  template: `\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n      <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"></path>\n    </svg>\n  `\n};\nexport default defineComponent({\n  name: 'backgroundlogin',\n  components: {\n    PerformanceIcon,\n    ServerIcon,\n    ShieldIcon\n  },\n  methods: {\n    navigateTo(path) {\n      // 记录当前活动路径作为上一个活动路径\n      if (this.currentPath && this.currentPath !== path) {\n        this.previousActivePath = this.currentPath;\n\n        // 为当前活动链接和登录按钮添加 active-exit 类\n        this.$nextTick(() => {\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\n          navLinks.forEach(link => {\n            if ((link.classList.contains('active') || path === '/login' && link.classList.contains('btn-login')) && !link.classList.contains('active-exit')) {\n              link.classList.add('active-exit');\n\n              // 等待动画完成后移除 active-exit 类\n              setTimeout(() => {\n                link.classList.remove('active-exit');\n              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)\n            }\n          });\n\n          // 更新当前路径\n          this.currentPath = path;\n        });\n      } else {\n        this.currentPath = path;\n      }\n\n      // 如果当前路径与目标路径相同，则重新加载页面\n      if (this.$route.path === path) {\n        this.$nextTick(() => {\n          window.scrollTo({\n            top: 0,\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\n          });\n\n          this.$router.go(0); // 刷新当前页面\n        });\n      } else {\n        // 不同路径，正常导航并滚动到顶部\n        this.$router.push(path);\n        window.scrollTo({\n          top: 0,\n          behavior: 'instant'\n        });\n      }\n    }\n  },\n  setup() {\n    const logoSrc = ref('/api/placeholder/100/100');\n    const servers = ref(Array(5).fill(null));\n    const features = ref([{\n      icon: 'PerformanceIcon',\n      title: '高性能算力',\n      description: '提供GPU/CPU灵活配置，满足AI训练、渲染等高算力需求'\n    }, {\n      icon: 'am-icon-shield',\n      title: '安全可靠',\n      description: '数据加密传输，多重备份，确保您的业务安全稳定运行'\n    }]);\n    return {\n      logoSrc,\n      servers,\n      features\n    };\n  }\n});", "map": {"version": 3, "names": ["defineComponent", "ref", "PerformanceIcon", "template", "ServerIcon", "ShieldIcon", "name", "components", "methods", "navigateTo", "path", "currentPath", "previousActivePath", "$nextTick", "navLinks", "document", "querySelectorAll", "for<PERSON>ach", "link", "classList", "contains", "add", "setTimeout", "remove", "$route", "window", "scrollTo", "top", "behavior", "$router", "go", "push", "setup", "logoSrc", "servers", "Array", "fill", "features", "icon", "title", "description"], "sources": ["src/views/Login/backgroundlogin.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login-left-side\">\r\n    <!-- 公司Logo -->\r\n    <div class=\"logo-container\">\r\n      <a @click=\"navigateTo('/index')\" class=\"logo-link\">\r\n        <img class=\"logo\" src=\"../../assets/logo_tiangong.png\" alt=\"算力租赁\" />\r\n      </a>\r\n      <!--      <h1 class=\"company-name\">天工云</h1>-->\r\n    </div>\r\n\r\n    <div class=\"bottom-text\">\r\n      <h2 class=\"slogan\">高效算力 · 智慧未来</h2>\r\n      <p class=\"sub-slogan\">专业算力租赁服务，为您的业务提供强大支持</p>\r\n    </div>\r\n\r\n    <!-- 主要视觉元素 -->\r\n    <div class=\"visual-element\">\r\n      <div class=\"server-illustration\">\r\n        <div v-for=\"(server, index) in servers\" :key=\"index\"\r\n             class=\"server-unit\"\r\n             :style=\"{\r\n               animationDelay: `${index * 0.2}s`,\r\n               transform: `translateY(${index * 4}px)`\r\n             }\">\r\n          <div class=\"server-light\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"connections\">\r\n<!--        <div v-for=\"i in 10\" :key=\"i\" class=\"connection-line\"></div>-->\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 特点介绍 -->\r\n    <div class=\"features\">\r\n      <div v-for=\"(feature, index) in features\" :key=\"index\" class=\"feature-item\">\r\n<!--        <div class=\"feature-icon\">-->\r\n<!--          <component :is=\"feature.icon\" />-->\r\n<!--        </div>-->\r\n        <div class=\"feature-text\">\r\n          <h3>{{ feature.title }}</h3>\r\n          <p>{{ feature.description }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 背景动画元素 -->\r\n    <div class=\"background-elements\">\r\n      <div v-for=\"i in 20\" :key=\"i\"\r\n           class=\"floating-particle\"\r\n           :style=\"{\r\n             left: `${Math.random() * 100}%`,\r\n             top: `${Math.random() * 100}%`,\r\n             animationDuration: `${3 + Math.random() * 10}s`,\r\n             animationDelay: `${Math.random() * 5}s`\r\n           }\">\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { defineComponent, ref } from 'vue';\r\n\r\n// 图标组件\r\nconst PerformanceIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <path d=\"M12 2v4\"></path>\r\n      <path d=\"m16.24 7.76 2.83-2.83\"></path>\r\n      <path d=\"M18 12h4\"></path>\r\n      <path d=\"m16.24 16.24 2.83 2.83\"></path>\r\n      <path d=\"M12 18v4\"></path>\r\n      <path d=\"m7.76 16.24-2.83 2.83\"></path>\r\n      <path d=\"M6 12H2\"></path>\r\n      <path d=\"m7.76 7.76-2.83-2.83\"></path>\r\n      <circle cx=\"12\" cy=\"12\" r=\"4\"></circle>\r\n    </svg>\r\n  `\r\n}\r\n\r\nconst ServerIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <rect width=\"20\" height=\"8\" x=\"2\" y=\"2\" rx=\"2\" ry=\"2\"></rect>\r\n      <rect width=\"20\" height=\"8\" x=\"2\" y=\"14\" rx=\"2\" ry=\"2\"></rect>\r\n      <line x1=\"6\" x2=\"6\" y1=\"6\" y2=\"6\"></line>\r\n      <line x1=\"6\" x2=\"6\" y1=\"18\" y2=\"18\"></line>\r\n    </svg>\r\n  `\r\n}\r\n\r\nconst ShieldIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"></path>\r\n    </svg>\r\n  `\r\n}\r\n\r\nexport default defineComponent({\r\n  name: 'backgroundlogin',\r\n  components: {\r\n    PerformanceIcon,\r\n    ServerIcon,\r\n    ShieldIcon\r\n  },\r\n  methods:{\r\n    navigateTo(path) {\r\n      // 记录当前活动路径作为上一个活动路径\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        // 为当前活动链接和登录按钮添加 active-exit 类\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              // 等待动画完成后移除 active-exit 类\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)\r\n            }\r\n          });\r\n\r\n          // 更新当前路径\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    },\r\n  },\r\n  setup() {\r\n    const logoSrc = ref('/api/placeholder/100/100');\r\n    const servers = ref(Array(5).fill(null));\r\n\r\n    const features = ref([\r\n      {\r\n        icon: 'PerformanceIcon',\r\n        title: '高性能算力',\r\n        description: '提供GPU/CPU灵活配置，满足AI训练、渲染等高算力需求'\r\n      },\r\n      {\r\n        icon: 'am-icon-shield',\r\n        title: '安全可靠',\r\n        description: '数据加密传输，多重备份，确保您的业务安全稳定运行'\r\n      }\r\n    ]);\r\n\r\n\r\n    return {\r\n      logoSrc,\r\n      servers,\r\n      features\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.login-left-side {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: linear-gradient(135deg, #025af7 0%, #2196f3 100%);\r\n  color: white;\r\n  padding: 40px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Logo样式 */\r\n.logo-container {\r\n  margin-top: -60px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  margin-left: -40px;\r\n  z-index: 10;\r\n}\r\n\r\n.logo {\r\n  width: 180px;\r\n  height: 140px;\r\n  border-radius: 12px;\r\n  margin-right: 15px;\r\n}\r\n\r\n/* 主视觉元素 */\r\n.visual-element {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  perspective: 1000px;\r\n  z-index: 5;\r\n}\r\n\r\n.server-illustration {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  transform: rotateY(25deg) rotateX(10deg);\r\n  transform-style: preserve-3d;\r\n}\r\n\r\n.server-unit {\r\n  width: 200px;\r\n  height: 30px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 6px;\r\n  position: relative;\r\n  backdrop-filter: blur(5px);\r\n  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.server-light {\r\n  position: absolute;\r\n  right: 10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: rgba(231, 12, 12, 0);\r\n  box-shadow: 0 0 10px #ffffff;\r\n  animation: blink 1.5s infinite;\r\n}\r\n\r\n.connections {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.connection-line {\r\n  position: absolute;\r\n  height: 2px;\r\n  width: 100px;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.7), transparent);\r\n  top: calc(30% + (40% * Math.random()));\r\n  left: calc(10% + (50% * Math.random()));\r\n  animation: move 4s infinite linear;\r\n  transform: rotate(calc(-30deg + (60deg * Math.random())));\r\n  opacity: 0.6;\r\n}\r\n\r\n/* 特点介绍 */\r\n.features {\r\n  margin-top: 40px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  z-index: 10;\r\n}\r\n\r\n.feature-item {\r\n  flex: 1;\r\n  min-width: 250px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 15px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 12px;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: transform 0.3s, box-shadow 0.3s;\r\n}\r\n\r\n.feature-item:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\r\n  border-color: rgba(255, 255, 255, 0.4);\r\n}\r\n\r\n.feature-icon {\r\n  margin-right: 15px;\r\n  color: white;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  padding: 10px;\r\n  border-radius: 10px;\r\n  height: 44px;\r\n  width: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.feature-icon svg {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.feature-text h3 {\r\n  margin: 0 0 5px 0;\r\n  font-size: 18px;\r\n}\r\n\r\n.feature-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部文案 */\r\n.bottom-text {\r\n  margin-top: -20px;\r\n  text-align: center;\r\n  z-index: 10;\r\n}\r\n\r\n.slogan {\r\n  font-size: 28px;\r\n  margin: 0 0 10px 0;\r\n  background: whitesmoke;\r\n  /*background: linear-gradient(to right, #ffffff, #2196f3);*/\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  font-weight: bold;\r\n}\r\n\r\n.sub-slogan {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  color: whitesmoke;\r\n  margin: 0;\r\n}\r\n\r\n/* 背景元素 */\r\n.background-elements {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.floating-particle {\r\n  position: absolute;\r\n  width: 6px;\r\n  height: 6px;\r\n  background-color: rgba(255, 255, 255, 0.5);\r\n  border-radius: 50%;\r\n  animation: float 10s infinite linear;\r\n}\r\n\r\n/* 动画 */\r\n@keyframes pulse {\r\n  0% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }\r\n  50% { box-shadow: 0 0 15px rgba(255, 255, 255, 0.5); }\r\n  100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }\r\n}\r\n\r\n@keyframes blink {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n}\r\n\r\n@keyframes move {\r\n  0% { transform: translateX(-50px) rotate(var(--rotation, -20deg)); opacity: 0; }\r\n  50% { opacity: 0.8; }\r\n  100% { transform: translateX(150px) rotate(var(--rotation, -20deg)); opacity: 0; }\r\n}\r\n\r\n@keyframes float {\r\n  0% { transform: translate(0, 0); opacity: 0; }\r\n  25% { opacity: 0.8; }\r\n  50% { transform: translate(10px, 10px); }\r\n  75% { opacity: 0.4; }\r\n  100% { transform: translate(0, 0); opacity: 0; }\r\n}\r\n</style>"], "mappings": ";AA8DA,SAAAA,eAAA,EAAAC,GAAA;;AAEA;AACA,MAAAC,eAAA;EACAC,QAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAAC,UAAA;EACAD,QAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAAE,UAAA;EACAF,QAAA;AACA;AACA;AACA;AACA;AACA;AAEA,eAAAH,eAAA;EACAM,IAAA;EACAC,UAAA;IACAL,eAAA;IACAE,UAAA;IACAC;EACA;EACAG,OAAA;IACAC,WAAAC,IAAA;MACA;MACA,SAAAC,WAAA,SAAAA,WAAA,KAAAD,IAAA;QACA,KAAAE,kBAAA,QAAAD,WAAA;;QAEA;QACA,KAAAE,SAAA;UACA,MAAAC,QAAA,GAAAC,QAAA,CAAAC,gBAAA;UACAF,QAAA,CAAAG,OAAA,CAAAC,IAAA;YACA,KAAAA,IAAA,CAAAC,SAAA,CAAAC,QAAA,cACAV,IAAA,iBAAAQ,IAAA,CAAAC,SAAA,CAAAC,QAAA,kBACA,CAAAF,IAAA,CAAAC,SAAA,CAAAC,QAAA;cACAF,IAAA,CAAAC,SAAA,CAAAE,GAAA;;cAEA;cACAC,UAAA;gBACAJ,IAAA,CAAAC,SAAA,CAAAI,MAAA;cACA;YACA;UACA;;UAEA;UACA,KAAAZ,WAAA,GAAAD,IAAA;QACA;MACA;QACA,KAAAC,WAAA,GAAAD,IAAA;MACA;;MAEA;MACA,SAAAc,MAAA,CAAAd,IAAA,KAAAA,IAAA;QACA,KAAAG,SAAA;UACAY,MAAA,CAAAC,QAAA;YACAC,GAAA;YACAC,QAAA;UACA;;UACA,KAAAC,OAAA,CAAAC,EAAA;QACA;MACA;QACA;QACA,KAAAD,OAAA,CAAAE,IAAA,CAAArB,IAAA;QACAe,MAAA,CAAAC,QAAA;UACAC,GAAA;UACAC,QAAA;QACA;MACA;IACA;EACA;EACAI,MAAA;IACA,MAAAC,OAAA,GAAAhC,GAAA;IACA,MAAAiC,OAAA,GAAAjC,GAAA,CAAAkC,KAAA,IAAAC,IAAA;IAEA,MAAAC,QAAA,GAAApC,GAAA,EACA;MACAqC,IAAA;MACAC,KAAA;MACAC,WAAA;IACA,GACA;MACAF,IAAA;MACAC,KAAA;MACAC,WAAA;IACA,EACA;IAGA;MACAP,OAAA;MACAC,OAAA;MACAG;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}