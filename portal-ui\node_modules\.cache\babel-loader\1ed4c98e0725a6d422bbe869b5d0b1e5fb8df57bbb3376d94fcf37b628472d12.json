{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    ref: \"globeContainer\",\n    staticClass: \"globe-container\"\n  }, [_c(\"div\", {\n    ref: \"canvasContainer\",\n    staticClass: \"globe-canvas\"\n  }), _vm.isLoading ? _c(\"div\", {\n    staticClass: \"loading-indicator\"\n  }, [_c(\"div\", {\n    staticClass: \"loading-spinner\"\n  }), _c(\"div\", {\n    staticClass: \"loading-text\"\n  }, [_vm._v(\"正在加载全球算力网络...\")])]) : _vm._e()]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "ref", "staticClass", "isLoading", "_v", "_e", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/components/common/Globe3D.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { ref: \"globeContainer\", staticClass: \"globe-container\" }, [\n    _c(\"div\", { ref: \"canvasContainer\", staticClass: \"globe-canvas\" }),\n    _vm.isLoading\n      ? _c(\"div\", { staticClass: \"loading-indicator\" }, [\n          _c(\"div\", { staticClass: \"loading-spinner\" }),\n          _c(\"div\", { staticClass: \"loading-text\" }, [\n            _vm._v(\"正在加载全球算力网络...\"),\n          ]),\n        ])\n      : _vm._e(),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,GAAG,EAAE,gBAAgB;IAAEC,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC1EH,EAAE,CAAC,KAAK,EAAE;IAAEE,GAAG,EAAE,iBAAiB;IAAEC,WAAW,EAAE;EAAe,CAAC,CAAC,EAClEJ,GAAG,CAACK,SAAS,GACTJ,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC7CH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCJ,GAAG,CAACM,EAAE,CAAC,eAAe,CAAC,CACxB,CAAC,CACH,CAAC,GACFN,GAAG,CAACO,EAAE,EAAE,CACb,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBT,MAAM,CAACU,aAAa,GAAG,IAAI;AAE3B,SAASV,MAAM,EAAES,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}